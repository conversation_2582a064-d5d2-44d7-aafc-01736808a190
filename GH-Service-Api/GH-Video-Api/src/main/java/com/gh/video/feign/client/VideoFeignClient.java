package com.gh.video.feign.client;


import com.gh.common.feign.FeignConfig;
import com.gh.common.utils.GHResponse;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(value = "video-service",
        configuration = FeignConfig.class,
        fallback = VideoFeignClientFallback.class)
@Component
public interface VideoFeignClient {



    @GetMapping("/server/session")
    @ApiOperation("获取session")
    GHResponse<String> session(@RequestParam("id") Integer id,@RequestParam("ip") String ip, @RequestParam("port") Integer port);


}
