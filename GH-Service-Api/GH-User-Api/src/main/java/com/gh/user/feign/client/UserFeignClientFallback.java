package com.gh.user.feign.client;


import com.gh.common.model.entity.User;
import com.gh.common.model.vo.UserListVo;
import com.gh.common.model.vo.UserVo;
import com.gh.common.utils.GHResponse;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
public class UserFeignClientFallback implements UserFeignClient {


    @Override
    public GHResponse<List<Integer>> getUserRole(Integer projectId, Integer userId) {
        return GHResponse.ok(new ArrayList<>());
    }


    @Override
    public GHResponse<List<UserListVo>> GetUsers(String name, Integer status, Integer projectId, Integer roleId, Integer deptId, Integer page, Integer size) {
        return null;
    }

    @Override
    public GHResponse<User> GetUser(Integer id) {
        return null;
    }

    @Override
    public GHResponse<List<UserVo>> GetRoleUsers(String name, Integer roleId, Integer projectId, Integer page, Integer size) {
        return null;
    }

}
