package com.gh.authentication.controller;

import cn.binarywang.wx.miniapp.api.WxMaUserService;
import cn.binarywang.wx.miniapp.bean.WxMaJscode2SessionResult;
import cn.hutool.core.codec.Base64;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.asymmetric.KeyType;
import cn.hutool.crypto.asymmetric.RSA;
import cn.hutool.system.oshi.OshiUtil;
import com.alibaba.fastjson.JSONObject;
import com.gh.authentication.config.WxMaConfiguration;
import com.gh.authentication.enhancer.JwtTokenEnhancer;
import com.gh.authentication.filter.GHAuthenticationManager;
import com.gh.authentication.filter.GHAuthenticationProvider;
import com.gh.authentication.filter.GHAuthenticationToken;
import com.gh.authentication.model.entity.GHKey;
import com.gh.authentication.model.vo.ChatVo;
import com.gh.authentication.model.vo.UserVo;
import com.gh.authentication.service.GHClientDetailsService;
import com.gh.authentication.service.GHKeyService;
import com.gh.authentication.service.UserService;
import com.gh.common.exception.ExceptionEnum;
import com.gh.common.exception.GhCustomException;
import com.gh.common.model.entity.User;
import com.gh.common.redis.RedisHelper;
import com.gh.common.utils.GHResponse;
import com.gh.common.utils.PassUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.security.oauth2.common.OAuth2AccessToken;
import org.springframework.security.oauth2.provider.ClientDetails;
import org.springframework.security.oauth2.provider.OAuth2Authentication;
import org.springframework.security.oauth2.provider.OAuth2Request;
import org.springframework.security.oauth2.provider.TokenRequest;
import org.springframework.security.oauth2.provider.token.DefaultTokenServices;
import org.springframework.security.oauth2.provider.token.TokenEnhancer;
import org.springframework.security.oauth2.provider.token.TokenEnhancerChain;
import org.springframework.security.oauth2.provider.token.TokenStore;
import org.springframework.security.oauth2.provider.token.store.JwtAccessTokenConverter;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.*;

@RestController
@Api(tags = "用户登陆")
@Slf4j
public class TokenController {

    @Autowired
    private HttpServletRequest request;
    @Autowired
    private GHClientDetailsService clientDetails;
    @Autowired
    private TokenStore tokenStore;
    @Autowired
    private JwtAccessTokenConverter jwtAccessTokenConverter;

    @Autowired
    private GHAuthenticationProvider provider;

    @Resource(name = "_jwtTokenEnhancer")
    private JwtTokenEnhancer jwtTokenEnhancer;

    @Autowired
    private RedisHelper redisHelper;

    @Autowired
    private GHKeyService ghKeyService;

    private static String privateKey = "MIICeAIBADANBgkqhkiG9w0BAQEFAASCAmIwggJeAgEAAoGBAKlTTQd0lwQ4QsvaU+iw+Y9vSk/P5gV6rmA2A1sYu8UFMOXPsgrIE/pmCnAGlZVjYt7I7iWc2d4sJL8lE5fpMZmZqWwq8XtMURujYAPhWzmAM6cTEHmVf6rGPEoAk4+RsTEiyZjC3Z+SaaO66SPW+qHxYD/R6grc+ynQpD2qGxgxAgMBAAECgYEApX8Un7/VxrsSllprfthS412cHUwO/7X77297cjNHe3bqZwPMefs6g5fuhbX5yAxe2286TKr0ytEMjYK4ltbGsUkdCbU1FKI+xmiViBgtV4i2dU59Aoq+QW/Zv7IU/0jVf3umhoBknU+nTcY44fVoiQep2XC2DGSBJaE0WkwQgX0CQQDhRNp++LNYE4za3JgkArNgrKldjzqKF6JmhyzkKEt4PqbeUaq4XTXi6XTLae6XXWwhzXhk4LCiciFZGRE6wmM/AkEAwGy185xv43D2hESsDK4MLgLJ4LTbmFJ0XFJXJVQdzxmlsPiga4ikmAbEFtk0uEOyhuBWAEvIb4wthpUx6JhYjwJBAIVdzj3936/1Yx7wGuK29pv1nQ/XDEFVXMB2pw0tDgFSllho4TnldVGyBYOoY6kZljss+mo+3FRQXY3yWVEAZOECQQCdx0W2N7qCyrhHoitUxk2UWJMBnipFDq78mEN+hydeDhCPOH4JTmRvh3S3q2gQAupar4209m6mEFGv/ZuLtYc/AkA7q6wan3/vNO3hwGZhFclUZ0Rs++wmjJS84Xy/scExv09kq2slYFwft9K2Y9S6E0vlruKFgOG9ls0sDWfLNtj1";

    @Value("${weChat.appId}")
    private String appId;

    @Autowired
    private UserService userService;


    @PostMapping("login")
    public GHResponse<OAuth2AccessToken> postAccessToken(@RequestBody UserVo userVo) {

        List<GHKey> list = ghKeyService.lambdaQuery().list();
        if (CollUtil.isNotEmpty(list)) {
            GHKey ghKey = list.get(0);
            RSA rsa = new RSA(privateKey, null);

            byte[] decrypt = rsa.decrypt(Base64.decode(ghKey.getContent()), KeyType.PrivateKey);
            String key = StrUtil.str(decrypt, CharsetUtil.CHARSET_UTF_8);

            if (StrUtil.isNotEmpty(key)) {
                String[] keys = key.split("_");
                if (ArrayUtil.isNotEmpty(keys)) {
                    Set<String> hardwareUUID = redisHelper.PopSet(0, "hardwareUUID");
//                    if (CollUtil.isNotEmpty(hardwareUUID) && hardwareUUID.contains(Base64.encode(keys[0]))) {
//                        if (DateUtil.date().isBeforeOrEquals(DateUtil.parse(keys[1]))) {
                            String username = Base64.decodeStr(userVo.getUsername());
                            String password = Base64.decodeStr(userVo.getPassword());
                            if (username.equalsIgnoreCase("demo")) {
                                String code = redisHelper.StringGet(0, "invitecode:" + userVo.getCode());
                                if (StrUtil.isEmpty(code)) {
                                    throw new GhCustomException(ExceptionEnum.CODE_ERROR);
                                }
                            }
                            GHAuthenticationToken authRequest = new GHAuthenticationToken(username, password);
                            setDetails(request, authRequest);
                            Authentication authenticate = provider.authenticate(authRequest);
                            HashMap<String, String> hashMap = new HashMap<String, String>() {{
                                put("username", username);
                                put("password", password);
                            }};
                            TokenRequest tokenRequest = new TokenRequest(hashMap, "gh", Arrays.asList("all"), "password");
                            ClientDetails client = clientDetails.loadClientByClientId("gh");
                            if (null != client) {
                                OAuth2Request oAuth2Request = tokenRequest.createOAuth2Request(client);
                                OAuth2Authentication oAuth2Authentication = new OAuth2Authentication(oAuth2Request, authenticate);
                                DefaultTokenServices defaultTokenServices = new DefaultTokenServices();
                                defaultTokenServices.setTokenStore(tokenStore);
                                defaultTokenServices.setClientDetailsService(clientDetails);
                                TokenEnhancerChain enhancerChain = new TokenEnhancerChain();
                                List<TokenEnhancer> enhancerList = new ArrayList<>();
                                enhancerList.add(jwtAccessTokenConverter);
                                enhancerList.add(jwtTokenEnhancer);
                                enhancerChain.setTokenEnhancers(enhancerList);
                                defaultTokenServices.setTokenEnhancer(enhancerChain);

                                OAuth2AccessToken accessToken = defaultTokenServices.createAccessToken(oAuth2Authentication);
                                return GHResponse.ok(accessToken);
                            }
                            throw new GhCustomException(ExceptionEnum.NOT_FOUND_ClIENT);

//                        } else {
//                            throw new GhCustomException(ExceptionEnum.EXPIRE_TIME);
//                        }
//                    } else {
//                        throw new GhCustomException(ExceptionEnum.HARDWARE_ERROR);
//                    }
                }
            }
        } else {
            throw new GhCustomException(ExceptionEnum.NOT_FOUND_ClIENT);
        }
        return GHResponse.ok(null);


    }


    @PostMapping("weChat")
    public GHResponse<OAuth2AccessToken> weChat(@RequestBody ChatVo chatVo) {
        WxMaJscode2SessionResult sessionInfo;
        WxMaUserService maUserService = WxMaConfiguration.getMaService(appId).getUserService();
        try {
            sessionInfo = maUserService.getSessionInfo(chatVo.getCode());
        } catch (WxErrorException e) {
            log.error("授权失败，code:" + chatVo.getCode());
            throw new GhCustomException(ExceptionEnum.CODE_ERROR);
        }
        if (null != sessionInfo) {
            String openid = sessionInfo.getUnionid();//用户在微信的唯一标识
            if (StrUtil.isNotEmpty(openid)) {
                List<User> users = userService.lambdaQuery().eq(User::getOpenId, openid)
                        .list();
                if (CollUtil.isNotEmpty(users)) {
                    User user = users.get(0);
                    return weChatLogin(user.getUserName(), PassUtil.Decrypt(user.getPassword(), user.getSalt()));
                } else {
                    return weChatLogin("mobile", "123456");
                }
            }
//            WxMaUserInfo userInfo = maUserService.getUserInfo(sessionInfo.getSessionKey(), chatVo.getEncryptedData(), chatVo.getIv());
//            WxMaPhoneNumberInfo phoneNoInfo = maUserService.getPhoneNoInfo(sessionInfo.getSessionKey(), chatVo.getEncryptedData(), chatVo.getIv());
//            log.error("-----"+phoneNoInfo.getPhoneNumber());
        }

        return null;


    }


    @PostMapping("bind")
    public GHResponse bind(@RequestBody ChatVo chatVo) {
        WxMaJscode2SessionResult sessionInfo;
        try {
            sessionInfo = WxMaConfiguration.getMaService(appId).getUserService().getSessionInfo(chatVo.getCode());
        } catch (WxErrorException e) {
            log.error("授权失败，code:" + chatVo.getCode());
            throw new GhCustomException(ExceptionEnum.CODE_ERROR);
        }
        if (null != sessionInfo) {
            String uid = sessionInfo.getUnionid();
            if (StrUtil.isNotEmpty(uid) && StrUtil.isNotEmpty(chatVo.getUsername())) {
                boolean update = userService.lambdaUpdate().set(User::getOpenId, uid)
                        .eq(User::getUserName, chatVo.getUsername())
                        .update();
                if (update) {
                    return GHResponse.ok();
                }
            }
        }

        return GHResponse.failed();

    }

    @PostMapping("unBind")
    @PreAuthorize("hasAnyAuthority('sys_admin','sys_project_admin','sys_user_unbind')")
    public GHResponse unBind(@RequestBody ChatVo chatVo) {
        if (chatVo.getUserId() != null) {
            boolean update = userService.lambdaUpdate().set(User::getOpenId, null)
                    .eq(User::getId, chatVo.getUserId())
                    .update();
            if (update) {
                return GHResponse.ok();
            }
        }

        return GHResponse.failed();

    }


    @PostMapping("refresh")
    public GHResponse<OAuth2AccessToken> refreshAccessToken(String refreshTokenValue) {
        Map<String, String> requestParameters = new HashMap<String, String>() {
            {
                put("grant_type", "refresh_token");
                put("refresh_token", refreshTokenValue);
            }
        };
        TokenRequest tokenRequest = new TokenRequest(requestParameters, "gh", Arrays.asList("all"), "refresh_token");

        DefaultTokenServices defaultTokenServices = new DefaultTokenServices();
        defaultTokenServices.setTokenStore(tokenStore);
        defaultTokenServices.setClientDetailsService(clientDetails);
        TokenEnhancerChain enhancerChain = new TokenEnhancerChain();
        List<TokenEnhancer> enhancerList = new ArrayList<>();
        enhancerList.add(jwtAccessTokenConverter);
        enhancerList.add(jwtTokenEnhancer);
        enhancerChain.setTokenEnhancers(enhancerList);
        defaultTokenServices.setTokenEnhancer(enhancerChain);
        defaultTokenServices.setSupportRefreshToken(true);
        defaultTokenServices.setAuthenticationManager(new GHAuthenticationManager(provider));
        OAuth2AccessToken oAuth2AccessToken = defaultTokenServices.refreshAccessToken(refreshTokenValue, tokenRequest);

        return GHResponse.ok(oAuth2AccessToken);

    }

    protected void setDetails(HttpServletRequest request, GHAuthenticationToken authRequest) {
        authRequest.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));
    }

    private ResponseEntity<OAuth2AccessToken> getResponse(OAuth2AccessToken accessToken) {
        HttpHeaders headers = new HttpHeaders();
        headers.set("Cache-Control", "no-store");
        headers.set("Pragma", "no-cache");
        headers.set("Content-Type", "application/json;charset=UTF-8");
        return new ResponseEntity<OAuth2AccessToken>(accessToken, headers, HttpStatus.OK);
    }

    private GHResponse<OAuth2AccessToken> weChatLogin(String username, String password) {
        GHAuthenticationToken authRequest = new GHAuthenticationToken(username, password);
        setDetails(request, authRequest);
        Authentication authenticate = provider.authenticate(authRequest);
        HashMap<String, String> hashMap = new HashMap<String, String>() {{
            put("username", username);
            put("password", password);
        }};
        TokenRequest tokenRequest = new TokenRequest(hashMap, "gh", Arrays.asList("all"), "password");
        ClientDetails client = clientDetails.loadClientByClientId("gh");
        if (null != client) {
            OAuth2Request oAuth2Request = tokenRequest.createOAuth2Request(client);
            OAuth2Authentication oAuth2Authentication = new OAuth2Authentication(oAuth2Request, authenticate);
            DefaultTokenServices defaultTokenServices = new DefaultTokenServices();
            defaultTokenServices.setTokenStore(tokenStore);
            defaultTokenServices.setClientDetailsService(clientDetails);
            TokenEnhancerChain enhancerChain = new TokenEnhancerChain();
            List<TokenEnhancer> enhancerList = new ArrayList<>();
            enhancerList.add(jwtAccessTokenConverter);
            enhancerList.add(jwtTokenEnhancer);
            enhancerChain.setTokenEnhancers(enhancerList);
            defaultTokenServices.setTokenEnhancer(enhancerChain);
            OAuth2AccessToken accessToken = defaultTokenServices.createAccessToken(oAuth2Authentication);
            return GHResponse.ok(accessToken);
        }
        return GHResponse.ok(null);


    }


    @PostMapping("key")
    @ApiOperation("获取机器码")
    public GHResponse<String> getHardWareId() {
        String hardwareUUID = OshiUtil.getSystem().getHardwareUUID();
        return GHResponse.ok(hardwareUUID);
    }

    @PostMapping("auth")
    @ApiOperation("软件授权")
    public GHResponse<String> setKey(@RequestBody JSONObject object) {
        if (object != null && object.get("key") != null) {
            ghKeyService.remove(null);
            GHKey ghKey = GHKey.builder().content(object.get("key").toString()).build();
            RSA rsa = new RSA(privateKey, null);
            byte[] decrypt = rsa.decrypt(Base64.decode(ghKey.getContent()), KeyType.PrivateKey);
            String str = StrUtil.str(decrypt, CharsetUtil.CHARSET_UTF_8);

            if (StrUtil.isNotEmpty(str) && str.contains("_")) {
                String date = str.split("_")[1];
                ghKey.setExpire(DateUtil.parse(date));
            }
            boolean save = ghKeyService.save(ghKey);
            if (save) {
                return GHResponse.ok("授权成功");
            }
        }
        return GHResponse.failed();
    }


    @PostMapping("token")
    public GHResponse token(@RequestBody UserVo userVo) {
        String username = Base64.decodeStr(userVo.getUsername());
        String password = Base64.decodeStr(userVo.getPassword());
        GHAuthenticationToken authRequest = new GHAuthenticationToken(username, password);
        setDetails(request, authRequest);
        Authentication authenticate = provider.authenticate(authRequest);
        HashMap<String, String> hashMap = new HashMap<String, String>() {{
            put("username", username);
            put("password", password);
        }};
        TokenRequest tokenRequest = new TokenRequest(hashMap, "gh", Arrays.asList("all"), "password");
        ClientDetails client = clientDetails.loadClientByClientId("third");
        if (null != client) {
            OAuth2Request oAuth2Request = tokenRequest.createOAuth2Request(client);
            OAuth2Authentication oAuth2Authentication = new OAuth2Authentication(oAuth2Request, authenticate);
            DefaultTokenServices defaultTokenServices = new DefaultTokenServices();
            defaultTokenServices.setTokenStore(tokenStore);
            defaultTokenServices.setClientDetailsService(clientDetails);
            TokenEnhancerChain enhancerChain = new TokenEnhancerChain();
            List<TokenEnhancer> enhancerList = new ArrayList<>();
            enhancerList.add(jwtAccessTokenConverter);
            enhancerList.add(jwtTokenEnhancer);
            enhancerChain.setTokenEnhancers(enhancerList);
            defaultTokenServices.setTokenEnhancer(enhancerChain);

            OAuth2AccessToken accessToken = defaultTokenServices.createAccessToken(oAuth2Authentication);

            return GHResponse.ok(accessToken);
        }
        throw new GhCustomException(ExceptionEnum.NOT_FOUND_ClIENT);

    }


}
