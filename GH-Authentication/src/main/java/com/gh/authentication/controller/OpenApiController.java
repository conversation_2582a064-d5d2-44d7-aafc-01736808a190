package com.gh.authentication.controller;

import cn.hutool.core.codec.Base64;
import com.gh.authentication.enhancer.JwtTokenEnhancer;
import com.gh.authentication.filter.GHAuthenticationProvider;
import com.gh.authentication.filter.GHAuthenticationToken;
import com.gh.authentication.model.vo.UserVo;
import com.gh.authentication.service.GHClientDetailsService;
import com.gh.common.exception.ExceptionEnum;
import com.gh.common.exception.GhCustomException;
import com.gh.common.utils.GHResponse;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.security.oauth2.common.OAuth2AccessToken;
import org.springframework.security.oauth2.provider.ClientDetails;
import org.springframework.security.oauth2.provider.OAuth2Authentication;
import org.springframework.security.oauth2.provider.OAuth2Request;
import org.springframework.security.oauth2.provider.TokenRequest;
import org.springframework.security.oauth2.provider.token.DefaultTokenServices;
import org.springframework.security.oauth2.provider.token.TokenEnhancer;
import org.springframework.security.oauth2.provider.token.TokenEnhancerChain;
import org.springframework.security.oauth2.provider.token.TokenStore;
import org.springframework.security.oauth2.provider.token.store.JwtAccessTokenConverter;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;

/**
 * Description:
 * User: zhangkeguang
 * Date: 2024-12-03-09:11
 */
@RestController
@Api(tags = "开放接口")
@Slf4j
public class OpenApiController {

    @Autowired
    private HttpServletRequest request;


    @Autowired
    private GHAuthenticationProvider provider;


    @Autowired
    private GHClientDetailsService clientDetails;


    @Autowired
    private JwtAccessTokenConverter jwtAccessTokenConverter;



    @Autowired
    private TokenStore tokenStore;


    @Resource(name = "_jwtTokenEnhancer")
    private JwtTokenEnhancer jwtTokenEnhancer;



    @PostMapping("/v1/token")
    public GHResponse token(@RequestBody UserVo userVo) {
        String username = Base64.decodeStr(userVo.getUsername());
        String password = Base64.decodeStr(userVo.getPassword());
        GHAuthenticationToken authRequest = new GHAuthenticationToken(username, password);
        setDetails(request, authRequest);
        Authentication authenticate = provider.authenticate(authRequest);
        HashMap<String, String> hashMap = new HashMap<String, String>() {{
            put("username", username);
            put("password", password);
        }};
        TokenRequest tokenRequest = new TokenRequest(hashMap, "gh", Arrays.asList("all"), "password");
        ClientDetails client = clientDetails.loadClientByClientId("third");
        if (null != client) {
            OAuth2Request oAuth2Request = tokenRequest.createOAuth2Request(client);
            OAuth2Authentication oAuth2Authentication = new OAuth2Authentication(oAuth2Request, authenticate);
            DefaultTokenServices defaultTokenServices = new DefaultTokenServices();
            defaultTokenServices.setTokenStore(tokenStore);
            defaultTokenServices.setClientDetailsService(clientDetails);
            TokenEnhancerChain enhancerChain = new TokenEnhancerChain();
            List<TokenEnhancer> enhancerList = new ArrayList<>();
            enhancerList.add(jwtAccessTokenConverter);
            enhancerList.add(jwtTokenEnhancer);
            enhancerChain.setTokenEnhancers(enhancerList);
            defaultTokenServices.setTokenEnhancer(enhancerChain);

            OAuth2AccessToken accessToken = defaultTokenServices.createAccessToken(oAuth2Authentication);

            return GHResponse.ok(accessToken);
        }
        throw new GhCustomException(ExceptionEnum.NOT_FOUND_ClIENT);

    }




    protected void setDetails(HttpServletRequest request, GHAuthenticationToken authRequest) {
        authRequest.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));
    }


}
