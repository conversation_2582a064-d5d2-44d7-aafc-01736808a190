spring:
  application:
    name: bim-service
  cloud:
    nacos:
      server-addr: ************:28848
      config:
        file-extension: yaml
        namespace: 734f1747-1d86-4f76-9cef-91fc7ea262b6
        extension-configs:
          - data-id: redis.yml
          - data-id: mysql.yml
          - data-id: json.yml
          - data-id: swagger.yml
          - data-id: feign.yml
#          - data-id: admin.yml
#          - data-id: zipkin.yml
      discovery:
        server-addr: ************:28848
        namespace: f0afb10d-cc93-4f3f-9dae-f7d7c3363802

  mvc:
    servlet:
      load-on-startup: 1
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB

server:
  port: 9007


minio:
  url: http://*************:9000
  domainUrl: http://*************:9000
  username: root
  password: Njgh8888
  bucket:
    - bim

bim:
  url: http://*************:9300/{}/index.html
  unzip: /Users/<USER>/bim/


logging:
  level:
    com.gh.bim.mapper: debug

mybatis-plus:
  global-config:
    db-config:
      update-strategy: ignored
