package com.gh.bim.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSON;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.gh.bim.config.MinioConfig;
import com.gh.bim.model.dto.ViewToken;
import com.gh.bim.model.entity.BimFile;
import com.gh.bim.service.BimFileService;
import com.gh.bim.service.UploadService;
import io.minio.MinioClient;
import io.minio.PutObjectArgs;
import io.minio.RemoveObjectArgs;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.lingala.zip4j.ZipFile;
import org.apache.commons.codec.Charsets;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.BufferedInputStream;
import java.io.File;
import java.io.IOException;
import java.util.List;

@Service
@Slf4j
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class UploadServiceImpl implements UploadService {
    private final BimFileService fileService;

    @Value("${bim.unzip}")
    private String unZipUrl;

    @Value("${bim.url}")
    private String bimUrl;

    @Autowired
    private MinioConfig config;

    @Autowired
    private MinioClient minioClient;


    @Override
    public String upload(MultipartFile file, Integer fileType, String person, Integer projectId) throws IOException {
        try {
            String extName = StrUtil.subAfter(file.getOriginalFilename(), ".", true);
            String fileName = StrUtil.subBefore(file.getOriginalFilename(), ".", true);
            PutObjectArgs objectArgs = PutObjectArgs.builder().contentType(file.getContentType())
                    .bucket(config.getBucket().get(0))
                    .object(file.getOriginalFilename())
                    .stream(new BufferedInputStream(file.getInputStream()), file.getSize(), -1)
                    .build();
            minioClient.putObject(objectArgs);
            String url = config.getDomainUrl() + "/" + config.getBucket().get(0) + "/" + file.getOriginalFilename();
            String fileId = null;

            File file2 = FileUtil.file(unZipUrl, file.getOriginalFilename());
            file.transferTo(file2);
            new ZipFile(file2).extractAll(unZipUrl);
            FileUtil.del(file2);

            //提取bim信息
            JSON json = JSONUtil.readJSON(FileUtil.file(unZipUrl + fileName + "/" + "viewToken.json"), Charsets.UTF_8);
            ViewToken viewToken = json.toBean(ViewToken.class);
            fileId = viewToken.getModelId();

            BimFile file1 = BimFile.builder().createPerson(person)
                    .createTime(DateUtil.date())
                    .extName(extName)
                    .fileType(fileType)
                    .groupId(null)
                    .name(file.getOriginalFilename())
                    .path(url)
                    .fileId(fileId)
                    .modelName(viewToken.getName())
                    .url(StrUtil.format(bimUrl, fileName))
                    .size(file.getSize())
                    .projectId(projectId)
                    .build();
            fileService.saveOrUpdate(file1, Wrappers.<BimFile>lambdaUpdate().eq(BimFile::getProjectId, projectId).eq(BimFile::getFileId, fileId));
            return url;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "";


    }

    @Override
    public void delete(String path) {
        try {
            List<BimFile> list = fileService.list(Wrappers.<BimFile>lambdaQuery().eq(BimFile::getPath, path));
            for (BimFile fileVo : list) {
                fileService.remove(Wrappers.<BimFile>lambdaQuery().eq(BimFile::getPath, path));
                String bimUnZipPath = StrUtil.subBefore(fileVo.getName(), ".", true);
                FileUtil.del(FileUtil.file(FileUtil.file(unZipUrl + bimUnZipPath)));
                minioClient.removeObject(RemoveObjectArgs.builder().bucket(config.getBucket().get(0))
                        .object(bimUnZipPath).build());
            }
        } catch (Exception e) {
            log.error(e.getMessage());
        }
    }
}
