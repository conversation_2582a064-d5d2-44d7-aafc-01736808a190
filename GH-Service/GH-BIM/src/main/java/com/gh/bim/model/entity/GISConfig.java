package com.gh.bim.model.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gh.bim.model.dto.DeviceStd;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.List;

@Builder
@Data
@TableName("GHGIS_Config")
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("gis配置")
public class GISConfig {
    @TableId(type = IdType.AUTO)
    private Integer id;

    private Integer projectId;
    private String alarmColor;
    @ApiModelProperty("边框颜色")
    private String borderColor;
    @ApiModelProperty("填充染色")
    private String fillColor;
    @ApiModelProperty("关联的设备")
    private Integer deviceId;
    @ApiModelProperty("设备的报警指标")
    private Integer alarmStd;
    @ApiModelProperty("设备的状态指标")
    private Integer statusStd;
    @ApiModelProperty("动作类型 1打开视频 2 打开设备面板")
    private Integer actionType;
    @ApiModelProperty("流媒体")
    private String server;
    @ApiModelProperty("摄像机")
    private String cam;
    @ApiModelProperty("地图自定义标签")
    private String icon;
    @ApiModelProperty("标签颜色")
    private String iconColor;
    @ApiModelProperty("overlay类型  marker polyline rectangle polygon circle")
    private String drawType;
    @ApiModelProperty("图形points集合，用于回显")
    private String points;
    @ApiModelProperty("配置id,也是每个overlay id")
    private String configId;
    private Double radius;
    private String conditionName;
    private String conditionValue;
    private String conditionValue1;
    private Double strokeWeight;
    private Double opacity;
    @TableField(exist = false)
    private Integer productId;
    @TableField(exist = false)
    private String productName;
    @TableField(exist = false)
    private List<DeviceStd> stds;
    @TableField(exist = false)
    private String deviceName;

    @TableField(exist = false)
    private String deviceIcon;

    private String model;

    private String vr;


}
