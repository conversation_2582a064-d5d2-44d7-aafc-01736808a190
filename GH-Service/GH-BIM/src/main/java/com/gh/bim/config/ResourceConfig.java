package com.gh.bim.config;


import lombok.AllArgsConstructor;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.method.configuration.EnableGlobalMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.oauth2.config.annotation.web.configuration.EnableResourceServer;
import org.springframework.security.oauth2.config.annotation.web.configuration.ResourceServerConfigurerAdapter;
import org.springframework.security.oauth2.config.annotation.web.configurers.ResourceServerSecurityConfigurer;


@Configuration
@EnableResourceServer
@EnableGlobalMethodSecurity(prePostEnabled = true) //开启方法注解
@AllArgsConstructor
public class ResourceConfig extends ResourceServerConfigurerAdapter {

//    private final TokenStore tokenStore;
//    private final TokenAuthenticationFilter filter;

    @Override
    public void configure(ResourceServerSecurityConfigurer resources) throws Exception {
//        resources.
//              resourceId("a-api")
//                .tokenStore(tokenStore);//jwt 本地认证


//                远程认证
//                .tokenServices(resourceServerTokenServices());




    }

//    @Bean
//    public ResourceServerTokenServices resourceServerTokenServices()
//    {
//        RemoteTokenServices tokenServices = new RemoteTokenServices();
//        tokenServices.setCheckTokenEndpointUrl("http://localhost:6000/oauth/check_token");
//        tokenServices.setClientId("gh");
//        tokenServices.setClientSecret("gh");
//        return  tokenServices;
//    }

    @Override
    public void configure(HttpSecurity http) throws Exception {
        //关闭session
        http.sessionManagement().sessionCreationPolicy(SessionCreationPolicy.STATELESS)
        .and().authorizeRequests()
        .antMatchers("/**").permitAll();

    }
}
