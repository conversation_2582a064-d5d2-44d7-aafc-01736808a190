package com.gh.bim.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.gh.bim.model.entity.BimFile;
import com.gh.common.utils.PageResult;

import java.util.List;

public interface BimFileService extends IService<BimFile> {

    PageResult<List<BimFile>> select(Integer id, String keyword, Integer fileType, Integer projectId, Integer size, Integer page);

    BimFile select(Integer areaId,Integer menuId,Integer projectId);
}
