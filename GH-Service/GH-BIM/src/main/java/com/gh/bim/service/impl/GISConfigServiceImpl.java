package com.gh.bim.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gh.bim.mapper.ConfigMapper;
import com.gh.bim.mapper.GISConfigMapper;
import com.gh.bim.model.entity.Config;
import com.gh.bim.model.entity.ConfigItemData;
import com.gh.bim.model.entity.GISConfig;
import com.gh.bim.service.*;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;


@Service
@RequiredArgsConstructor
public class GISConfigServiceImpl extends ServiceImpl<GISConfigMapper, GISConfig> implements GisConfigService  {

    @Autowired
    private GISConfigMapper mapper;

    @Override
    public List<GISConfig> GetConfig(String configId, Integer projectId,List<Integer> types) {
       return mapper.GetConfig(projectId,types,configId);
    }
}
