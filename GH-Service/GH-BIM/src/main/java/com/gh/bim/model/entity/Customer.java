package com.gh.bim.model.entity;


import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Builder
@Data
@TableName("GHFM_Customer")
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("客户信息")
public class Customer {
    @TableId(type = IdType.AUTO)
    private Integer id;
    private String name;
    private Integer customerType;
    private String phone;
    private String address;

    @TableField(fill = FieldFill.INSERT)
    private Date createTime;
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    private Integer projectId;

}
