package com.gh.bim.model.vo;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

@Builder
@Data
@ApiModel(description = "config")
@TableName("GHBIM_Config")
public class ConfigVo {
    @TableId(type = IdType.AUTO)
    private Integer id;
    private String name;
    private Integer deviceId;
    private String objectId;
    private Integer projectId;
    private String fileId;
    private Integer areaId;
    private Integer menuId;
    private String floor;
    @ApiModelProperty("1播放视频2打开面板")
    private Integer actionType;
    private String server;
    private String cam;
    private Integer panelId;
    private Integer productId;
}
