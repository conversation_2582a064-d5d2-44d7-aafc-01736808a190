package com.gh.bim.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;


@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("GHBIM_ItemData")
@ApiModel(value = "ConfigItemData" ,description = "数据项")
public class ConfigItemData {

    @TableId(type = IdType.AUTO)
    private Integer id;
    private Integer configId;
    private String name;
    @ApiModelProperty("1数值显示2图标显示3文本显示")
    private Integer type;
    private Integer unit;
//    private String value;
    private Integer standardId;
    @TableField(exist = false)
    private List<ConfigCondition> conditions;

    @TableField(exist = false)
    private String variable;
}
