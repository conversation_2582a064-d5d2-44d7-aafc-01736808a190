server:
  port: 9032
spring:
  application:
    name: ws-service
  kafka:
    consumer:
      group-id: ws33
      properties:
        spring:
          json:
            trusted:
              packages: "*"
      value-deserializer: org.springframework.kafka.support.serializer.JsonDeserializer
    producer:
      value-serializer: org.apache.kafka.common.serialization.StringSerializer

  redis:
    database: 15
    cache:
      expire-time: 300  # 缓存过期时间(秒)
      max-size: 1000    # 最大缓存条目

  cloud:
    nacos:
      server-addr: ${host:*************:28848}
      config:
        file-extension: yaml
        namespace: 9027158d-ab5d-43f7-a691-3b7546cc79f9
        extension-configs:
          - data-id: kafka.yml
          - data-id: mysql.yml
          - data-id: redis.yml
          - data-id: json.yml
      discovery:
        server-addr: ${host:*************:28848}
        namespace: 6cf15815-a3cb-4cba-a940-16026af4257c
mybatis-plus:
  global-config:
    db-config:
      update-strategy: ignored

ws:
  server:
    port: 32000
    host: localhost

pool:
  corePool: 10
  maxPool: 20
  queueCapacity: 1000




