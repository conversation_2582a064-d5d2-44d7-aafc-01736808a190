<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gh.ws.mapper.RuleConfigMapper">

    <resultMap id="config" type="com.gh.ws.model.entity.RuleConfig" autoMapping="true">
        <id column="id" property="id"></id>
        <!--        <result property="deviceType" column="deviceType"></result>-->
        <!--        <result property="enableScene" column="enableScene"></result>-->
        <!--        <result property="enableProcess" column="enableProcess"></result>-->
        <!--        <result property="flowKey" column="flowKey"></result>-->
        <!--        <result property="sceneId" column="sceneId"></result>-->
        <!--        <result property="standardId" column="standardId"></result>-->
        <!--        <result property="ruleId" column="ruleId"></result>-->
        <!--        <result property="deviceId" column="deviceId"></result>-->
        <collection property="ruleCondition" column="id" ofType="com.gh.ws.model.entity.RuleCondition">
            <id column="cid" property="id"></id>
            <result property="conditionValue" column="conditionValue"></result>
            <result property="expression" column="expression"></result>
            <result property="operator" column="operator"></result>
            <result property="ruleConfigId" column="ruleConfigId"></result>
        </collection>
    </resultMap>

    <select id="getRuleConfig" resultMap="config">
        SELECT r.productId ,r.enableScene,r.enableProcess,r.flowKey,r.sceneId,r.deviceId,
        r.enableGroup,r.groupId,
        r.id,r.standardId,r.ruleId,c.conditionValue,c.expression,c.id as cid,c.operator,c.ruleConfigId

        FROM ghrule_ruleconfig r
        LEFT JOIN ghrule_rulecondition c on r.id=c.ruleConfigId
        <where>
            <if test="ruleId!=null">
                and r.ruleId=#{ruleId}
            </if>

        </where>
        order by c.id asc
    </select>

</mapper>
