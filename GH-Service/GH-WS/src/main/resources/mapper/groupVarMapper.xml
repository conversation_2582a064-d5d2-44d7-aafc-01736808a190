<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gh.ws.mapper.GroupVarMapper">


   <!--    规则联动控制组-->
    <select id="selectGroupVars" resultType="com.gh.ws.model.dto.GroupVarDto">
        select ds.variable,s.`name` as stdName,d.`name` as deviceName,gr.varValue from ghscene_groupvar gr
                                                      left JOIN ghdm_devicestandard ds on gr.deviceStandardId=ds.id
                                                      LEFT JOIN ghdm_standard s on ds.standardId=s.id
                                                      LEFT JOIN ghdm_device d on ds.deviceId=d.id
                                                      LEFT JOIN ghscene_group g on gr.groupId=g.id
        WHERE g.status=1 and gr.status=1
        <if test="ids!=null and ids.size()>0">
            <foreach collection="ids" open=" and gr.groupId in (" close=")" item="id" separator=",">
                #{id}
            </foreach>
        </if>

    </select>

</mapper>