<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gh.ws.mapper.SceneMapper">
    <select id="findVars"  resultType="com.gh.ws.model.dto.SceneVarDto">
        SELECT GHScene_GroupVar.*,GHDM_DeviceStandard.variable,GHDM_Device.`name`, ghdm_device.id AS deviceId, ghdm_productstd.`name` as standardName
        ,ghops_strategygroup.startVal,endVal,ghdm_product.protocol,ghdm_productstd.identifier,ghdm_product.id as productId
        FROM ghops_strategy
        INNER  JOIN ghops_strategygroup ON ghops_strategy.id=ghops_strategygroup.strategyId
        INNER JOIN GHScene_Group ON ghops_strategygroup.groupId=GHScene_Group.id
        INNER JOIN GHScene_GroupVar ON GHScene_GroupVar.groupId=GHScene_Group.id
        INNER JOIN GHDM_DeviceStandard ON deviceStandardId=GHDM_DeviceStandard.id
        INNER JOIN GHDM_Device ON GHDM_DeviceStandard.deviceId=GHDM_Device.id
        INNER JOIN ghdm_productstd ON GHDM_DeviceStandard.standardId=ghdm_productstd.id
        INNER JOIN ghdm_product on ghdm_productstd.productId=ghdm_product.id
        <where>
            ghops_strategy.`status`=1 AND GHScene_Group.`status`=1 AND GHScene_GroupVar.`status`=1
            <if test="projectId!=null">
                and ghops_strategy.projectId=#{projectId}
            </if>
            <if test="sceneId!=null">
                and ghops_strategy.id=#{sceneId}
            </if>
            <if test="groupId!=null">
                and ghops_strategygroup.groupId=#{groupId}
            </if>
        </where>
    </select>


    <select id="getDeviceByCode" resultType="com.gh.common.model.dto.DeviceDto">
        select d.id,d.name,code,p.gatewayId,p.protocol,d.projectId from ghdm_device d LEFT JOIN ghdm_product p on d.productId=p.id where d.code=#{code} or d.id=#{id}
    </select>






</mapper>
