<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gh.ws.mapper.RunLogMapper">
    <select id="getRunLogRecord"  resultType="com.gh.common.model.entity.RunLog">
        SELECT ghops_runlog.* ,ghauth_user.`name` as username from ghops_runlog LEFT JOIN ghauth_user ON
        userId=ghauth_user.id
        <where>
            <if test="userId!=null">
                and ghops_runlog.userId=#{userId}
            </if>
            <if test="bt!=null">
                and  logTime <![CDATA[>=]]> #{bt}
            </if>
            <if test="et!=null">
                and  logTime <![CDATA[<=]]> #{et}
            </if>
            <if test="type!=null">
                and ghops_runlog.type= #{type}
            </if>
            <if test="projectId!=null">
                and ghops_runlog.projectId= #{projectId}
            </if>
            <if test="menuId!=null">
                and ghops_runlog.menuId= #{menuId}
            </if>
            <if test="tag!=null">
                and ghops_runlog.tag= #{tag}
            </if>
            <if test="deviceId!=null">
                and ghops_runlog.deviceId= #{deviceId}
            </if>
        </where>
        order  by logTime desc
    </select>

    <select id="getDeviceStdByVar" resultType="com.gh.common.model.entity.RunLog">
        SELECT d.`name` as deviceName,s.`name` as standardName,ds.deviceId,s.identifier FROM ghdm_devicestandard ds
        LEFT JOIN ghdm_productstd s on ds.standardId=s.id
        LEFT JOIN ghdm_device d on d.id=ds.deviceId
        <where>
            <if test="variable !='' and variable !=null">
                and ds.variable=#{variable}
            </if>
            <if test="projectId!=null">
                and ds.projectId= #{projectId}
            </if>
        </where>
    </select>

    <select id="getAllRunLogOptimized"  resultType="com.gh.common.model.entity.RunLog">
        SELECT rl.*,s.`name` as standardName1,d.`name` as deviceName1,u.`name` as username
        FROM (
        SELECT * FROM ghops_runlog
        <where>
            <if test="bt!=null">
                and logTime <![CDATA[>=]]> #{bt}
            </if>
            <if test="et!=null">
                and logTime <![CDATA[<=]]> #{et}
            </if>
            <if test="projectId!=null">
                and projectId= #{projectId}
            </if>
            <if test="deviceId!=null">
                and deviceId= #{deviceId}
            </if>
        </where>
        ORDER BY logTime DESC
        LIMIT #{limit},#{offset}
        ) rl
        LEFT JOIN ghdm_device d on rl.deviceId=d.id
        LEFT JOIN ghdm_productstd s on s.identifier=rl.identifier and d.productId=s.productId
        LEFT JOIN ghauth_user u on rl.userId=u.id
        <where>
            <if test="keyword !='' and keyword !=null">
                and (rl.deviceName like #{keyword} or d.name like #{keyword})
            </if>
        </where>
        ORDER BY rl.logTime DESC
    </select>

    <select id="countAllRunLogOptimized" resultType="java.lang.Long">
        SELECT COUNT(1)
        FROM ghops_runlog rl
        <where>
            <if test="bt!=null">
                and rl.logTime <![CDATA[>=]]> #{bt}
            </if>
            <if test="et!=null">
                and rl.logTime <![CDATA[<=]]> #{et}
            </if>
            <if test="projectId!=null">
                and rl.projectId= #{projectId}
            </if>
            <if test="deviceId!=null">
                and rl.deviceId= #{deviceId}
            </if>
            <if test="keyword !='' and keyword !=null">
                and rl.deviceName like #{keyword}
            </if>
        </where>
    </select>



</mapper>
