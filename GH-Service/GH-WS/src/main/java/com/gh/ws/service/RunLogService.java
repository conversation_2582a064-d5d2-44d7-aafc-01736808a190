package com.gh.ws.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.gh.common.model.entity.RunLog;

import java.util.Date;
import java.util.List;

public interface RunLogService extends IService<RunLog> {


    List<RunLog> getAllRunLogOptimized(String keyword, Date bt, Date et, Integer limit, Integer offset, Integer projectId, Integer deviceId);

    Long countAllRunLogOptimized(Integer projectId, String keyword, Date bt, Date et,Integer deviceId);


}
