package com.gh.ws.socket;

public interface Event {
    String SUBSCRIBE="Subscribe";

    //设置项目id
    String SetProject="SetProject";
    String RemoveProject="RemoveProject";
    String SetUserProject="SetUserProject";
    String SetUserId="SetUserId";

    String WriteVarValue="WriteVarValue";
    String SetVarProp="SetVarProp";
    String ProcessAlarmAll="ProcessAlarmAll";
    String ProcessAlarm="ProcessAlarm";
    String SendHisAlarm="SendHisAlarm";
    String SendHisAlarmAll="SendHisAlarmAll";
    String UnSubscribe="UnSubscribe";
    String UnSubscribeBatch="UnSubscribeBatch";

    String SnapPhoto="SnapPhoto";
    String SnapVideo="SnapVideo";

    String ManualWrite="ManualWrite";

    String GetAdviceMes="GetAdviceMes";

    String ConfirmMsg="ConfirmMsg";
    String AgreeInvited="AgreeInvited";

    String OpenLog="OpenLog";

}
