package com.gh.ws.starter;

import com.corundumstudio.socketio.SocketIOServer;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@AllArgsConstructor
public class ServerRunner implements CommandLineRunner {
    private final SocketIOServer socketIOServer;
    @Override
    public void run(String... args)  {
          socketIOServer.start();
          log.info("socketIOServer start");
    }

}
