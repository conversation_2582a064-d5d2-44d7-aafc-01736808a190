package com.gh.ws.controller;

import cn.hutool.json.JSONObject;
import com.gh.common.utils.GHResponse;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * Description:
 * User: zhangkeguang
 * Date: 2024-07-19-09:00
 */
@RestController
@Api(tags = "开发api,接收平板指令")
@Slf4j
public class IpadCmdApiController {


    @Autowired
    private com.gh.ws.service.CmdService service;


    @PostMapping("/ipad-cmd")
    public GHResponse post(@RequestBody JSONObject params) {
        log.info("属性数据:{}", params.toString());
        service.processIpadCmd(params);
        return GHResponse.ok();
    }
}
