package com.gh.ws;


import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableAsync;

@SpringBootApplication
@ComponentScan(basePackages = {"com.gh.common.redis","com.gh.common.model","com.gh.ws"})
@EnableAsync
@EnableDiscoveryClient
@EnableFeignClients("com.gh")
public class WsApplication {
    public static void main(String[] args) {
        SpringApplication.run(WsApplication.class,args);
    }
}
