package com.gh.ws.controller;

import cn.hutool.core.util.StrUtil;
import com.corundumstudio.socketio.SocketIOServer;
import com.gh.common.utils.GHResponse;
import com.gh.ws.model.dto.AIAlarmDTO;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Description:
 * User: zhangkeguang
 * Date: 2024-07-19-09:00
 */
@RestController
@Api(tags = "九江实验室ai预警推送")
@Slf4j
@RequestMapping("/ai")
public class AIAlarmApiController {




    @Autowired
    private SocketIOServer server;



    @PostMapping("push")
    public GHResponse post(@RequestBody AIAlarmDTO params) {
        log.info("属性数据:{}", params.toString());
        if (params != null) {
            if (StrUtil.isNotEmpty(params.getEvent())&&params.getEvent().equalsIgnoreCase("start")) {
                server.getAllClients().forEach(client -> {
                    client.sendEvent("ai_alarm",params);
                });
            }else if(StrUtil.isNotEmpty(params.getEvent())&&params.getEvent().equalsIgnoreCase("test"))
            {
                server.getAllClients().forEach(client -> {
                    params.setVname("2F走廊东向东");
                    client.sendEvent("ai_alarm",params);
                });
            }

        }
        return GHResponse.ok();
    }
}
