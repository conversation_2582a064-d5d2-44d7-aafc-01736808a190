package com.gh.ws.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONArray;
import com.corundumstudio.socketio.SocketIOClient;
import com.corundumstudio.socketio.SocketIOServer;
import com.gh.common.model.dto.RespChangeData;
import com.gh.common.model.dto.RespData;
import com.gh.common.redis.RedisHelper;
import com.gh.ws.kafka.KafkaProduce;
import com.gh.ws.model.dto.LiveData;
import com.gh.ws.model.dto.LiveDataResponse;
import com.gh.ws.socket.Subscribe;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
@Slf4j
@AllArgsConstructor
public class HandleChangeDataService {

    private final RedisHelper redisHelper;

    private final SocketIOServer server;

    private final Subscribe subscribe;

    private final KafkaProduce kafkaProduce;

    @Async("taskExecutor")
    public void subscribeVarChangeCallback(RespChangeData respChangeData) {
        // new Thread(() -> {


        Map<String, Map<String, LiveDataResponse>> data = new HashMap<>(16);

        handleVarData(respChangeData, data);

        handleDevData(respChangeData, data);

        if (CollUtil.isNotEmpty(data)) {
            sendData(data);
        }
    }

    private void handleVarData(RespChangeData respChangeData, Map<String, Map<String, LiveDataResponse>> data) {
        if(CollUtil.isEmpty(respChangeData.getData())){
            return;
        }
        for (RespData respData : respChangeData.getData()) {
            String variable = String.format("%s:%s:%s:%s",
                    respData.getIOServerID(), respData.getChlID(),
                    respData.getCtrlID(), respData.getID());
            Set<String> setMembers = redisHelper.SetMembers(15, "var:" + variable);
            if (CollUtil.isEmpty(setMembers)) {
                continue;
            }

            String changePropName = respData.getPropery() == null ? "Value" : respData.getPropery();

            for (String member : setMembers) {
                String[] parts = member.split("#");
                String propName = parts[2];
                if (!propName.equalsIgnoreCase(changePropName)) {
                    continue;
                }

                updateDataMap(data, member, respData);
            }
        }
    }

    private void handleDevData(RespChangeData respChangeData, Map<String, Map<String, LiveDataResponse>> data) {
        if (CollUtil.isEmpty(respChangeData.getDev())) {
            return;
        }

        for (String dev : respChangeData.getDev()) {
            String id = StrUtil.subBefore(dev, ":", true);
            String value = StrUtil.subAfter(dev, ":", true);
            Set<String> setMembers = redisHelper.SetMembers(15, "dev:" + id);

            sendKafkaMessage(dev, value);

            if (CollUtil.isEmpty(setMembers)) {
                continue;
            }
            for (String member : setMembers) {
                updateDevDataMap(data, member, value);
            }
            


        }
    }

    /**
     * 发送Kafka消息到第三方
     * 
     * @param dev   设备数据字符串 (格式: productId:deviceId:identifier:value)
     * @param value 设备值
     */
    private void sendKafkaMessage(String dev, String value) {
        String[] split = dev.split(":");
        if (split.length != 4) {
//            log.warn("非ioserver设备数据不用下发: {}", dev);
            return;
        }
        try {
            com.alibaba.fastjson.JSONObject message = buildKafkaMessage(split, value);
            kafkaProduce.Produce("gh_data_push_third", message.toJSONString());
        } catch (Exception e) {
            log.error("发送Kafka消息失败, dev: {}, value: {}", dev, value, e);
        }
    }
    private com.alibaba.fastjson.JSONObject buildKafkaMessage(String[] deviceInfo, String value) {
        com.alibaba.fastjson.JSONObject message = new com.alibaba.fastjson.JSONObject();
        message.put("productId", deviceInfo[0]);
        message.put("deviceId", deviceInfo[1]);
        
        JSONArray paramsArray = new JSONArray();
        com.alibaba.fastjson.JSONObject param = new com.alibaba.fastjson.JSONObject();
        param.put("identifier", deviceInfo[2]);
        param.put("value", value);
        paramsArray.add(param);
        
        message.put("params", paramsArray);
        return message;
    }

    private void updateDataMap(Map<String, Map<String, LiveDataResponse>> data, String c, RespData respData) {
        String propName = c.split("#")[2];
        String changePropName = respData.getPropery() == null ? "Value" : respData.getPropery();
        if (propName.equalsIgnoreCase(changePropName)) {
            String[] connect_batch = c.split("#")[0].split("_");
            String connectId = connect_batch[0];
            String batchId = connect_batch[1];
            Map<String, LiveDataResponse> map = data.computeIfAbsent(connectId, k -> new HashMap<>());
            LiveDataResponse response = map.computeIfAbsent(batchId, k -> {
                LiveDataResponse r = new LiveDataResponse();
                r.setBatchDefinitionId(batchId);
                r.setClientId(connectId);
                r.setCode(200);
                r.setData(new ArrayList<>());
                return r;
            });
            // 获取连接下的所有订阅
            LiveData liveData = new LiveData();
            liveData.setId(c.split("#")[1]);
            liveData.setValue(respData.getValue());
            liveData.setTimestamp(DateUtil.now());
            liveData.setItemStatus(respData.isStatus() ? 1 : 0);
            liveData.setPropName(StrUtil.isEmpty(respData.getPropName()) ? "Value" : respData.getPropName());
            response.getData().add(liveData);
        }
    }

    private void updateDevDataMap(Map<String, Map<String, LiveDataResponse>> data, String c, String value) {
        // connectionId_batch#id
        String[] split = c.split("#")[0].split("_");
        Map<String, LiveDataResponse> batchMap = data.computeIfAbsent(split[0],
                k -> new HashMap<>());
        LiveDataResponse response = batchMap.computeIfAbsent(split[1], k -> {
            LiveDataResponse r = new LiveDataResponse();
            r.setBatchDefinitionId(split[1]);
            r.setClientId(split[0]);
            r.setCode(200);
            // 预分配ArrayList大小
            r.setData(new ArrayList<>(16));
            return r;
        });
        // 获取连接下的所有订阅

        LiveData liveData = new LiveData();
        liveData.setId(c.split("#")[1]);
        liveData.setValue(value);
        liveData.setTimestamp(DateUtil.now());
        liveData.setDesc("");
        liveData.setPropName("Value");
        response.getData().add(liveData);
    }

    private void sendData(Map<String, Map<String, LiveDataResponse>> data) {
        data.forEach((clientId, batchMap) -> {
            SocketIOClient client = server.getClient(UUID.fromString(clientId));
            if (client != null) {
                batchMap.values()
                        .forEach(response -> client.sendEvent("onVarsChangedCallback", JSONUtil.toJsonStr(response)));
            } else {
                subscribe.RemoveSubscribe(clientId);
            }
        });
    }

}
