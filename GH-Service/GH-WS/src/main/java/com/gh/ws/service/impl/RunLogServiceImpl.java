package com.gh.ws.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gh.common.model.entity.RunLog;
import com.gh.ws.mapper.RunLogMapper;
import com.gh.ws.service.RunLogService;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;


@Service
@AllArgsConstructor
public class RunLogServiceImpl extends ServiceImpl<RunLogMapper, RunLog> implements RunLogService {

    @Autowired
    private RunLogMapper runLogMapper;

    @Override
    public List<RunLog> getAllRunLogOptimized(String keyword, Date bt, Date et, Integer limit, Integer offset, Integer projectId, Integer deviceId) {
        return runLogMapper.getAllRunLogOptimized(limit,offset, projectId, keyword, bt, et, deviceId);
    }

    @Override
    public Long countAllRunLogOptimized(Integer projectId, String keyword, Date bt, Date et, Integer deviceId) {
        return runLogMapper.countAllRunLogOptimized(projectId, keyword, bt, et, deviceId);
    }


}
