package com.gh.ws.controller;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import com.gh.common.utils.GHResponse;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Description:
 * User: zhangkeguang
 * Date: 2024-07-19-09:00
 */
@RestController
@RequestMapping(value = "/device")
@Api(tags = "开发api,设备控制使用")
@Slf4j
public class DeviceApiController {


    @Autowired
    private com.gh.ws.service.CmdService service;


    @PostMapping("/property/post")
    public GHResponse post(@RequestBody JSONObject params) {
        log.info("属性上报:{}", params.toString());
        String id = params.getStr("id");
        if(StrUtil.isNotEmpty(id))
        {
            boolean push = service.postDeviceProperty(params);
            if (push) {
                return GHResponse.ok();
            }
        }
        return GHResponse.failed();
    }
}
