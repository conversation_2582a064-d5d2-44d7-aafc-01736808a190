package com.gh.ws.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.corundumstudio.socketio.SocketIOServer;
import com.gh.common.model.dto.DeviceDto;
import com.gh.common.model.dto.WriteDataRequestV2;
import com.gh.common.model.dto.WriteDataV2;
import com.gh.common.model.entity.RunLog;
import com.gh.common.redis.RedisHelper;
import com.gh.common.utils.RequestUtil;
import com.gh.ws.kafka.KafkaProduce;
import com.gh.ws.mapper.SceneMapper;
import com.gh.ws.model.dto.SceneVarDto;
import com.gh.ws.service.CmdService;
import com.gh.ws.service.RunLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * Description:
 * User: zhangkeguang
 * Date: 2024-07-19-09:03
 */

@Service
@Slf4j
public class CmdServiceImpl implements CmdService {

    @Autowired
    private RedisHelper redisHelper;

    @Autowired
    private SceneMapper sceneMapper;

    @Autowired
    private KafkaProduce Produce;

    @Autowired
    private SocketIOServer server;

    @Autowired
    private RunLogService runLogService;


    @Override
    public boolean postDeviceProperty(JSONObject params) {
        String deviceCode = params.getStr("deviceCode");
        if (StrUtil.isEmpty(deviceCode)) {
            return false;
        }

        JSONObject items = params.getJSONObject("params");
        List<RunLog> runLogs = new ArrayList<>();
        
        for (String k : items.keySet()) {
            JSONObject item = items.getJSONObject(k);
            String value = item.getStr("value");
            if (StrUtil.isEmpty(value)) {
                continue;
            }

            List<DeviceDto> device = sceneMapper.getDeviceByCode(deviceCode, null);
            if (CollUtil.isEmpty(device)) {
                continue;
            }
            
            DeviceDto deviceDto = device.get(0);
            String dkey = "device:" + deviceDto.getId() + ":" + k;
            String variable = redisHelper.StringGet(0, dkey);
            
            processVariableWrite(variable, value, deviceDto, k, runLogs, "api", false);
        }
        
        saveBatchRunLogs(runLogs);
        return true;
    }

    @Override
    public void processIpadCmd(JSONObject params) {
        String category = params.getStr("category");
        switch (category) {
            case "Time_Selection":
                server.getAllClients().forEach(client -> {
                    client.sendEvent("Time_Selection", params);
                });
                break;
            case "Weather_Simulation":
                server.getAllClients().forEach(client -> {
                    client.sendEvent("Weather_Simulation", params);
                });
                break;
            case "Switch_System":
                server.getAllClients().forEach(client -> {
                    client.sendEvent("Switch_System", params);
                });
                break;
            case "Switch_Floor":
                server.getAllClients().forEach(client -> {
                    client.sendEvent("Switch_Floor", params);
                });
                break;
            case "3D_Tour":
                server.getAllClients().forEach(client -> {
                    client.sendEvent("Tour", params);
                });
                break;
            default:
                break;
        }
    }

    @Override
    public boolean postVaribale(List<SceneVarDto> data, String value, Boolean open) {
        if (CollUtil.isEmpty(data)) {
            return false;
        }

        Map<String, WriteDataRequestV2> pushData = new HashMap<>();
        List<RunLog> runLogs = new ArrayList<>();
        
        for (SceneVarDto d : data) {
            String finalValue = determineFinalValue(value, d, open);
            if (StrUtil.isBlank(finalValue)) {
                continue;
            }

            if (StrUtil.isNotEmpty(d.getVariable()) && d.getVariable().contains(":")
                    && d.getVariable().split(":").length == 4) {
                processVariableWriteForBatch(d.getVariable(), finalValue, d, runLogs, pushData, "openapi");
            } else if (StrUtil.isEmpty(d.getVariable())) {
                String variable = redisHelper.StringGet(0, "device:" + d.getDeviceId() + ":" + d.getIdentifier());
                processVariableWrite(variable, finalValue, createDeviceDto(d), d.getIdentifier(), runLogs, "openapi", false);
            }
        }
        
        // 批量发送
        pushData.forEach((k, v) -> {
            Produce.Produce("gh_mqtt_write", k + "&" + JSONUtil.toJsonStr(v));
        });
        
        saveBatchRunLogs(runLogs);
        return true;
    }

    /**
     * 处理变量写入的通用方法
     */
    private void processVariableWrite(String variable, String value, DeviceDto deviceDto, 
                                    String identifier, List<RunLog> runLogs, String batchDefinitionId, boolean isBatch) {
        if (StrUtil.isEmpty(variable)) {
            return;
        }

        String uuid = RandomUtil.randomString(32);
        RunLog runLog = createRunLog(uuid, deviceDto.getProjectId(), deviceDto.getId(), identifier, value);

        if (variable.contains(":") && variable.split(":").length == 4) {
            processStandardVariable(variable, value, runLog, uuid, batchDefinitionId, isBatch);
        } else if (variable.contains(":")) {
            processThirdPartyVariable(variable, value, deviceDto, identifier, runLog);
        }
        
        runLogs.add(runLog);
    }

    /**
     * 处理批量变量写入
     */
    private void processVariableWriteForBatch(String variable, String value, SceneVarDto sceneVarDto, 
                                            List<RunLog> runLogs, Map<String, WriteDataRequestV2> pushData, String batchDefinitionId) {
        String[] varSplit = variable.split(":");
        String uuid = RandomUtil.randomString(32);
        
        RunLog runLog = createRunLog(uuid, sceneVarDto.getProjectId(), sceneVarDto.getDeviceId(), 
                                   sceneVarDto.getIdentifier(), value);
        
        WriteDataRequestV2 writeDataRequest = pushData.computeIfAbsent(varSplit[0], 
                k -> WriteDataRequestV2.builder().data(new ArrayList<>()).build());
        
        String oldValue = redisHelper.HashGetFields(14, "var:" + variable, "Value");
        runLog.setOldValue(oldValue);
        runLog.setVariable(variable);
        
        WriteDataV2 writeData = createWriteDataV2(varSplit, uuid, value, batchDefinitionId);
        writeDataRequest.getData().add(writeData);
        
        runLogs.add(runLog);
    }

    /**
     * 处理标准变量（4段式）
     */
    private void processStandardVariable(String variable, String value, RunLog runLog, 
                                       String uuid, String batchDefinitionId, boolean isBatch) {
        String[] varSplit = variable.split(":");
        String oldValue = redisHelper.HashGetFields(14, "var:" + variable, "Value");
        runLog.setOldValue(oldValue);
        runLog.setVariable(variable);
        
        if (isBatch) {
            // 批量处理在外层处理
            return;
        }
        
        WriteDataV2 writeData = createWriteDataV2(varSplit, uuid, value, batchDefinitionId);
        WriteDataRequestV2 writeDataRequest = WriteDataRequestV2.builder()
                .data(Arrays.asList(writeData))
                .build();
        
        Produce.Produce("gh_mqtt_write", varSplit[0] + "&" + JSONUtil.toJsonStr(writeDataRequest));
    }

    /**
     * 处理第三方变量
     */
    private void processThirdPartyVariable(String variable, String value, DeviceDto deviceDto, 
                                         String identifier, RunLog runLog) {
        String[] varSplit = variable.split(":");
        JSONObject thirdData = new JSONObject();
        thirdData.set("value", value);
        thirdData.set("productId", deviceDto.getProductId());
        thirdData.set("deviceId", deviceDto.getId());
        thirdData.set("identifier", identifier);
        thirdData.set("protocol", varSplit[0]);
        thirdData.set("deviceCode", varSplit[2]);
        thirdData.set("gatewayId", varSplit[1]);
        
        Produce.Produce("gh_mqtt_write_third", thirdData.toString());
        
        String oldValue = redisHelper.HashGetFields(14,
                "product:" + deviceDto.getProductId() + ":" + deviceDto.getId(), identifier);
        runLog.setOldValue(oldValue);
    }

    /**
     * 创建 RunLog 对象
     */
    private RunLog createRunLog(String uuid, Integer projectId, Integer deviceId, String identifier, String newValue) {
        return RunLog.builder()
                .logTime(DateUtil.date())
                .userId(RequestUtil.getUserId())
                .requestId(uuid)
                .projectId(projectId)
                .deviceId(deviceId)
                .identifier(identifier)
                .type(4)
                .newValue(newValue)
                .build();
    }

    /**
     * 创建 WriteDataV2 对象
     */
    private WriteDataV2 createWriteDataV2(String[] varSplit, String uuid, String value, String batchDefinitionId) {
        return WriteDataV2.builder()
                .chlKey(varSplit[1])
                .iosvrKey(varSplit[0])
                .batchDefinitionId(batchDefinitionId)
                .clientId("third")
                .level(1)
                .ctrlKey(varSplit[2])
                .varKey(varSplit[3])
                .propName("Value")
                .id(uuid)
                .value(value)
                .build();
    }

    /**
     * 确定最终值
     */
    private String determineFinalValue(String value, SceneVarDto sceneVarDto, Boolean open) {
        if (StrUtil.isNotBlank(value)) {
            return value;
        }
        if (null != open && open) {
            return sceneVarDto.getStartVal();
        } else if (null != open && !open) {
            return sceneVarDto.getEndVal();
        }
        return sceneVarDto.getVarValue();
    }

    /**
     * 从 SceneVarDto 创建 DeviceDto
     */
    private DeviceDto createDeviceDto(SceneVarDto sceneVarDto) {
        DeviceDto deviceDto = new DeviceDto();
        deviceDto.setId(sceneVarDto.getDeviceId());
        deviceDto.setProjectId(sceneVarDto.getProjectId());
        deviceDto.setProductId(sceneVarDto.getProductId());
        return deviceDto;
    }

    /**
     * 批量保存运行日志
     */
    private void saveBatchRunLogs(List<RunLog> runLogs) {
        if (CollUtil.isNotEmpty(runLogs)) {
            runLogService.saveBatch(runLogs);
        }
    }
}
