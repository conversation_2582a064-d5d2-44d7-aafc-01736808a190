package com.gh.ws.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import com.gh.common.model.entity.RunLog;
import com.gh.common.utils.GHResponse;
import com.gh.ws.mapper.SceneMapper;
import com.gh.ws.model.dto.SceneVarDto;
import com.gh.ws.service.CmdService;
import com.gh.ws.service.RunLogService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;

/**
 * Description:
 * User: zhangkeguang
 * Date: 2024-07-19-09:00
 */
@RestController
@Api(tags = "开放")
@Slf4j
public class OpenApiController {


    @Autowired
    private CmdService service;

    @Autowired
    private SceneMapper sceneMapper;

    @Autowired
    private RunLogService runLogService;


    @PostMapping("/v1/property/post")
    public GHResponse post(@RequestBody JSONObject params) {
        log.info("属性上报:{}", params.toString());
        String id = params.getStr("id");
        if(StrUtil.isNotEmpty(id))
        {
            boolean push = service.postDeviceProperty(params);
            if (push) {
                return GHResponse.ok();
            }
        }
        return GHResponse.failed();
    }

    @PostMapping("/v1/devices/write")
    @ApiOperation("多个设备写入属性值")
    public GHResponse productProperty(@RequestBody List<JSONObject> params)
    {
        if(CollUtil.isNotEmpty(params))
        {
            params.forEach(p->{
                 service.postDeviceProperty(p);
            });
            return GHResponse.ok();

        }
        return GHResponse.failed();

    }

    @PostMapping("/v1/device/write")
    @ApiOperation("单个设备写入属性值")
    public GHResponse productProperty(@RequestBody JSONObject param)
    {
        if(param!=null)
        {
            boolean posted = service.postDeviceProperty(param);
            return GHResponse.ok(posted);
        }
        return GHResponse.failed();

    }


    @PostMapping("/v1/scene/write")
    @ApiOperation("场景写值")
    public GHResponse scene(@RequestBody JSONObject param)
    {

        List<SceneVarDto> list = sceneMapper.findVars(null, param.getInt("sceneId"),null);
        boolean posted = service.postVaribale(list, param.getStr("value"),null);
        return GHResponse.ok(posted);

    }

    @PostMapping("/v1/scene/open")
    @ApiOperation("场景写值")
    public GHResponse sceneOpen(@RequestBody JSONObject param)
    {

        List<SceneVarDto> list = sceneMapper.findVars(null, param.getInt("sceneId"),null);
        boolean posted = service.postVaribale(list, param.getStr("value"),true);
        return GHResponse.ok(posted);

    }
    @PostMapping("/v1/scene/close")
    @ApiOperation("场景写值")
    public GHResponse sceneClose(@RequestBody JSONObject param)
    {

        List<SceneVarDto> list = sceneMapper.findVars(null, param.getInt("sceneId"),null);
        boolean posted = service.postVaribale(list, param.getStr("value"),false);
        return GHResponse.ok(posted);

    }



    @PostMapping("/v1/group/write")
    @ApiOperation("控制组写值")
    public GHResponse group(@RequestBody JSONObject param)
    {
        List<SceneVarDto> list = sceneMapper.findVars(null, null,param.getInt("groupId"));
        boolean posted = service.postVaribale(list, param.getStr("value"),null);
        return GHResponse.ok(posted);
    }


    @GetMapping("/v1/run/log")
    @ApiOperation("所有设备日志记录")
    public GHResponse<List<RunLog>> manualLogPage(String  keyword, Integer projectId,
                                                  @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date bt,
                                                  @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")Date et,
                                                  @RequestParam(defaultValue = "1") Integer page, @RequestParam(defaultValue = "10") Integer size, Integer menuId, Integer deviceId)
    {
        if(StrUtil.isNotEmpty(keyword))
        {
            keyword = StrUtil.format("%{}%", keyword);
        }
        List<RunLog> select = runLogService.getAllRunLogOptimized(keyword, bt, et, (page-1)*size, size,projectId,deviceId);

        long count=runLogService.countAllRunLogOptimized(projectId, keyword, bt, et, deviceId);
        return GHResponse.ok(select,count);
    }






}
