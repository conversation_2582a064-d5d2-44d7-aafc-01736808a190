package com.gh.ws.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gh.ws.mapper.RuleConfigMapper;
import com.gh.ws.model.entity.RuleConfig;
import com.gh.ws.service.RuleConfigService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;


@Service
@AllArgsConstructor
public class RuleConfigImpl extends ServiceImpl<RuleConfigMapper, RuleConfig> implements RuleConfigService {


    private final RuleConfigMapper ruleConfigMapper;



    @Override
    public RuleConfig getRuleConfig(Integer ruleId) {
      return   ruleConfigMapper.getRuleConfig(ruleId);
    }
}
