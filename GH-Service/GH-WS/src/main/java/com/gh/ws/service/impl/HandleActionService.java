package com.gh.ws.service.impl;


import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.gh.common.model.dto.WriteDataRequestV2;
import com.gh.common.model.dto.WriteDataV2;
import com.gh.scene.feign.client.SceneFeignClient;
import com.gh.ws.kafka.KafkaProduce;
import com.gh.ws.model.dto.RecordSnap;
import com.gh.ws.model.entity.AlarmConfig;
import com.gh.ws.model.entity.AlarmHistory;
import com.gh.ws.service.AlarmHistoryService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.util.ArrayList;
import java.util.List;

@Component
@Slf4j
public class HandleActionService {

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private AlarmHistoryService alarmHistoryService;


    @Autowired
    private KafkaProduce kafkaProduce;


    /**
     * 变量写值
     *
     * @param variable
     * @param value
     */
    @Async
    public void writeValue(String variable, String value) {
        String[] vars = variable.split(":");
        if (vars.length == 4) {
            String uuid = RandomUtil.randomString(32);
            WriteDataRequestV2 writeDataRequest = WriteDataRequestV2.builder()
                  .build();
            List<WriteDataV2> requestItems = new ArrayList<>();
            WriteDataV2 writeData = WriteDataV2.builder().chlKey(vars[1]).iosvrKey(vars[0])
                    .clientId("action")
                    .level(1)
                    .batchDefinitionId(uuid)
                    .ctrlKey(vars[2])
                    .varKey(vars[3])
                    .propName("Value")
                    .id(RandomUtil.randomString(32))
                    .value(value).build();
            requestItems.add(writeData);
            writeDataRequest.setData(requestItems);
            kafkaProduce.Produce("gh_mqtt_write", vars[0] + "&" + JSONUtil.toJsonStr(writeDataRequest));
        }else if(vars.length==3)
        {

        }
    }

    /**
     * 抓拍 保存到数据库
     * @param alarmConfig
     * @param history
     */
    public void processSnap(AlarmConfig alarmConfig, AlarmHistory history) {
        String server = alarmConfig.getAssociateVideo().split("#")[0];
        String cam = alarmConfig.getAssociateVideo().split("#")[1];
        String[] split = server.split("_");
        String url = "http://" + split[2] + ":" + split[3] + "/api/v1/Snapshot?token=" + cam.split("_")[0];
        if (StrUtil.isNotEmpty(url)) {
            ResponseEntity<RecordSnap> entity = restTemplate.getForEntity(url, RecordSnap.class);
            RecordSnap body = entity.getBody();
            if (StrUtil.isNotEmpty(body.getStrUrl())) {
                String snapUrl = "http://" + split[2] + ":" + split[3] + body.getStrUrl();
                alarmHistoryService.lambdaUpdate().set(AlarmHistory::getAlarmSnapUrl, snapUrl)
                        .eq(AlarmHistory::getAlarmGuid, history.getAlarmGuid())
                        .update();
            }
        }
    }

    /**
     * 录像 保存到数据库
     * @param alarmConfig
     * @param history
     */
    @Async
    public void processRecord(AlarmConfig alarmConfig, AlarmHistory history) {
        String server = alarmConfig.getAssociateVideo().split("#")[0];
        String cam = alarmConfig.getAssociateVideo().split("#")[1];
        String[] split = server.split("_");
        String url = "http://" + split[2] + ":" + split[3] + "/api/v1/ManualRecordStart?token=" + cam.split("_")[0] + "&alwayscreate=true&limittime=10";
        if (StrUtil.isNotEmpty(url)) {
            ResponseEntity<RecordSnap> entity = restTemplate.getForEntity(url, RecordSnap.class);
            RecordSnap body = entity.getBody();
            if (StrUtil.isNotEmpty(body.getStrUrl())) {
                String videoUrl = "http://" + split[2] + ":" + split[3] + body.getStrUrl();
                alarmHistoryService.lambdaUpdate().set(AlarmHistory::getAlarmVideoUrl, videoUrl)
                        .eq(AlarmHistory::getAlarmGuid, history.getAlarmGuid())
                        .update();

            }
        }
    }


}
