package com.gh.tdengine;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.context.annotation.ComponentScan;


@SpringBootApplication
@EnableDiscoveryClient
@ComponentScan(basePackages = "com.gh")
public class TdengineApplication {
    public static void main(String[] args) {
        SpringApplication.run(TdengineApplication.class,args);
    }
}
