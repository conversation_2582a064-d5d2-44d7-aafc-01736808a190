package com.gh.acs.controller;


import com.gh.acs.service.DFOAService;
import com.gh.common.utils.GHResponse;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Api("九江实验室德福OA接口")
@RestController
@RequestMapping("/df")
@Slf4j
public class DFOAController {

    @Autowired
    private DFOAService service;


    @GetMapping("/getRoomList")
    public GHResponse getRoomList(Long bt, Long et, String roomName){
        return GHResponse.ok(service.getRoomList(bt,et,roomName));
    }

    @GetMapping("/getRoomInfo")
    public GHResponse getRoomInfo(String roomId){
        return GHResponse.ok(service.getRoomInfo(roomId));
    }



}
