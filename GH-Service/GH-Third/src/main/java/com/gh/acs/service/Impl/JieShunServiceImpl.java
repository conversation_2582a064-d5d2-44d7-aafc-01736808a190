package com.gh.acs.service.Impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;
import cn.hutool.crypto.digest.DigestUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.gh.acs.service.JieShunService;
import com.gh.common.redis.RedisHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.net.HttpCookie;
import java.nio.charset.Charset;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * Description: 捷顺接口开发
 * User: zhangkeguang
 * Date: 2022-03-07-09:40
 */

@Service
@Slf4j
public class JieShunServiceImpl implements JieShunService {

    @Value("${jieshun.url}")
    private String url;

    @Value("${jieshun.token}")
    private String token;

    @Value("${jieshun.username}")
    private String username;
    @Value("${jieshun.password}")
    private String password;


    @Autowired
    private RedisHelper redisHelper;

    @Override
    public JSONObject parkIn(String bt, String et, Integer page, Integer size) {
        Map<String, Object> param = new HashMap<>();
        param.put("startDate", bt);
        param.put("endDate", et);
        param.put("page", page);
        param.put("rows", size);
        String body = HttpRequest.post(url + "/report/ParkInsideRecord/GetDataList?temp=0.990549791216534")
                .cookie(getCookie())
                .body(URLUtil.buildQuery(param, Charset.defaultCharset()))
                .execute()
                .body();
        if (StrUtil.isNotEmpty(body)) {
            return JSONUtil.parseObj(body);
        }
        return null;
    }

    @Override
    public JSONObject inRecord(String bt, String et, Integer page, Integer size) {
        Map<String, Object> param = new HashMap<>();
        param.put("startDate", bt);
        param.put("endDate", et);
        param.put("page", page);
        param.put("rows", size);
        String body = HttpRequest.post(url + "/report/ParkEnterRecord/GetDataList?temp=0.2695012031240549")
                .cookie(getCookie())
                .body(URLUtil.buildQuery(param, Charset.defaultCharset()))
                .execute()
                .body();
        if (StrUtil.isNotEmpty(body)) {
            return JSONUtil.parseObj(body);
        }
        return null;
    }

    @Override
    public JSONObject outRecord(String bt, String et, Integer page, Integer size) {
        Map<String, Object> param = new HashMap<>();
        param.put("startDate", bt);
        param.put("endDate", et);
        param.put("page", page);
        param.put("rows", size);
        String body = HttpRequest.post(url + "/report/ParkOutRecord/GetDataList?temp=0.3191626051062719")
                .cookie(getCookie())
                .body(URLUtil.buildQuery(param, Charset.defaultCharset()))
                .execute()
                .body();
        if (StrUtil.isNotEmpty(body)) {
            return JSONUtil.parseObj(body);
        }
        return null;
    }

    @Override
    public JSONObject doorRecord(String bt, String et, Integer page, Integer size) {
        Map<String, Object> param = new HashMap<>();
        param.put("startDate", bt);
        param.put("endDate", et);
        param.put("page", page);
        param.put("rows", size);
        param.put("type", 1);
        String body = HttpRequest.post(url + "/report/DoorRecord/GetDataList?temp=0.37817360703831926")
                .cookie(getCookie())
                .body(URLUtil.buildQuery(param, Charset.defaultCharset()))
                .execute()
                .body();
        if (StrUtil.isNotEmpty(body)) {
            return JSONUtil.parseObj(body);
        }
        return null;
    }

    @Override
    public JSONObject doorAlarmRecord(String bt, String et, Integer page, Integer size) {
        Map<String, Object> param = new HashMap<>();
        param.put("startDate", bt);
        param.put("endDate", et);
        param.put("page", page);
        param.put("rows", size);
        param.put("type", 3);
        String body = HttpRequest.post(url + "/report/DoorRecord/GetDataList?temp=0.2922213526194337")
                .cookie(getCookie())
                .body(URLUtil.buildQuery(param, Charset.defaultCharset()))
                .execute()
                .body();
        if (StrUtil.isNotEmpty(body)) {
            return JSONUtil.parseObj(body);
        }
        return null;
    }

    @Override
    public JSONObject doorNormalRecord(String bt, String et, Integer page, Integer size) {
        Map<String, Object> param = new HashMap<>();
        param.put("startDate", bt);
        param.put("endDate", et);
        param.put("page", page);
        param.put("rows", size);
        param.put("type", 2);
        String body = HttpRequest.post(url + "/report/DoorRecord/GetDataList?temp=0.6725256597034375")
                .cookie(getCookie())
                .body(URLUtil.buildQuery(param, Charset.defaultCharset()))
                .execute()
                .body();
        if (StrUtil.isNotEmpty(body)) {
            return JSONUtil.parseObj(body);
        }
        return null;
    }

    @Override
    public JSONObject doorUsers(String bt, String et, Integer page, Integer size) {
        Map<String, Object> param = new HashMap<>();
        param.put("startDate", bt);
        param.put("endDate", et);
        param.put("page", page);
        param.put("rows", size);
        String body = HttpRequest.post(url + "/report/UserBaseInfo/GetDataList?temp=0.5630571074019579")
                .cookie(getCookie())
                .body(URLUtil.buildQuery(param, Charset.defaultCharset()))
                .execute()
                .body();
        if (StrUtil.isNotEmpty(body)) {
            return JSONUtil.parseObj(body);
        }
        return null;
    }

    @Override
    public JSONArray doorStatus() {
        String body = HttpRequest.post(url + "/Monitors/MonitorDeviceTable/GetControlDevice")
                .cookie(getCookie())
                .execute()
                .body();
        if (StrUtil.isNotEmpty(body)) {
            return JSONUtil.parseArray(body);
        }
        return null;
    }

    @Override
    public JSONObject getSummaryInfo() {
        String body = HttpRequest.post(url + "/Report/UserWindow/GetSummaryInfo")
                .cookie(getCookie())
                .execute()
                .body();
        if (StrUtil.isNotEmpty(body)) {
            return JSONUtil.parseObj(body);
        }
        return null;
    }

    @Override
    public JSONObject getDeviceStatus() {
        String body = HttpRequest.post(url + "/Report/ProjectWindow/GetDeviceMonitor?temp=0.13292984619806414")
                .cookie(getCookie())
                .execute()
                .body();
        if (StrUtil.isNotEmpty(body)) {
            return JSONUtil.parseObj(body);
        }
        return null;
    }

    @Override
    public JSONObject fkEvents(String bt, String et, Integer page, Integer size) {
        Map<String, Object> param = new HashMap<>();
        param.put("VisitTimeBegin", bt);
        param.put("VisitTimeEnd", et);
        param.put("page", page);
        param.put("rows", size);
        String body = HttpRequest.post(url + "/person/VisitorCheckIn/GetBDVisitorList")
                .cookie(getCookie())
                .body(URLUtil.buildQuery(param, Charset.defaultCharset()))
                .execute()
                .body();
        if (StrUtil.isNotEmpty(body)) {
            return JSONUtil.parseObj(body);
        }
        return null;
    }

    @Override
    public JSONObject getAllPerson(String keyword, Integer page, Integer size) {
        String random=RandomUtil.randomNumbers(6);
        long time=DateUtil.currentSeconds();
        Map<String, Object> param = new HashMap<>();
        param.put("keyWord", keyword);
        param.put("pageIndex", page);
        param.put("pageSize", size);
        String body = HttpRequest.post("http://192.168.202.10:8091/api/base/personlist")
                .header("appId","app01")
                .header("v","1.0")
                .header("random",random)
                .header("timestamp", Convert.toStr(time))
                .header("sign",getMd5(random,time))
                .body(JSONUtil.toJsonStr(param))
                .execute()
                .body();
        if (StrUtil.isNotEmpty(body)) {
            return JSONUtil.parseObj(body);
        }
        return null;
    }




    @Override
    public String getDepts() {
        String body = HttpRequest.post(url + "/person/group/GetRoleGrouptree")
                .cookie(getCookie())
                .execute()
                .body();
//        log.error("#############url=="+url + "/person/group/GetRoleGrouptree");
//        log.error("#############cookie==="+getCookie());
//        log.error("#############body==="+body);
//        String body="[{\"children\":[{\"children\":[],\"text\":\"当升材料科技有限公司门禁\",\"id\":\"ac3bb7c4-b376-49c4-b4bd-3327d5bb8849\",\"attributes\":{\"ID\":56,\"RGGUID\":\"ac3bb7c4-b376-49c4-b4bd-3327d5bb8849\",\"RGName\":\"当升材料科技有限公司门禁\",\"RGCode\":\"024866858\",\"ParentId\":\"5a9dc528-e7ad-4952-9f21-a885b6564a1c\",\"RGType\":1,\"Status\":0,\"CreatedOnUtc\":\"2022-07-21 11:22:39\",\"Remark\":\"导入人事自动新增组织\",\"RGFullPaths\":\"当升材料科技有限公司|当升材料科技有限公司门禁;\",\"ParentName\":\"当升材料科技有限公司\",\"IsParentChange\":0},\"iconCls\":\"icon-system\",\"value\":\"024866858\",\"_parentId\":\"5a9dc528-e7ad-4952-9f21-a885b6564a1c\",\"created\":\"2022-07-21 11:22:39\",\"sortid\":0,\"target\":null,\"state\":null},{\"children\":[],\"text\":\"管理\",\"id\":\"579adb4b-51df-4909-b3f2-89fbe29f0c0e\",\"attributes\":{\"ID\":14,\"RGGUID\":\"579adb4b-51df-4909-b3f2-89fbe29f0c0e\",\"RGName\":\"管理\",\"RGCode\":\"02344193\",\"ParentId\":\"5a9dc528-e7ad-4952-9f21-a885b6564a1c\",\"RGType\":1,\"Status\":0,\"CreatedOnUtc\":\"2022-02-16 09:59:43\",\"Remark\":null,\"RGFullPaths\":\"当升材料科技有限公司|管理;\",\"ParentName\":\"当升材料科技有限公司\",\"IsParentChange\":0},\"iconCls\":\"icon-system\",\"value\":\"02344193\",\"_parentId\":\"5a9dc528-e7ad-4952-9f21-a885b6564a1c\",\"created\":\"2022-02-16 09:59:43\",\"sortid\":0,\"target\":null,\"state\":null},{\"children\":[{\"children\":[],\"text\":\"2022大学生\",\"id\":\"6c439ce4-**************-c4f2c4c54db2\",\"attributes\":{\"ID\":54,\"RGGUID\":\"6c439ce4-**************-c4f2c4c54db2\",\"RGName\":\"2022大学生\",\"RGCode\":\"13798118\",\"ParentId\":\"9249ddce-146b-422d-baca-2047b6e71fe3\",\"RGType\":1,\"Status\":0,\"CreatedOnUtc\":\"2022-07-07 18:16:01\",\"Remark\":null,\"RGFullPaths\":\"当升材料科技有限公司|门禁|2022大学生;\",\"ParentName\":\"门禁\",\"IsParentChange\":0},\"iconCls\":\"icon-system\",\"value\":\"13798118\",\"_parentId\":\"9249ddce-146b-422d-baca-2047b6e71fe3\",\"created\":\"2022-07-07 18:16:01\",\"sortid\":0,\"target\":null,\"state\":null},{\"children\":[],\"text\":\"安全环保部\",\"id\":\"b6e6729e-1645-4e93-a1b0-b7ebe4e30e3a\",\"attributes\":{\"ID\":23,\"RGGUID\":\"b6e6729e-1645-4e93-a1b0-b7ebe4e30e3a\",\"RGName\":\"安全环保部\",\"RGCode\":\"01573651\",\"ParentId\":\"9249ddce-146b-422d-baca-2047b6e71fe3\",\"RGType\":1,\"Status\":0,\"CreatedOnUtc\":\"2022-02-16 11:00:06\",\"Remark\":null,\"RGFullPaths\":\"当升材料科技有限公司|门禁|安全环保部;\",\"ParentName\":\"门禁\",\"IsParentChange\":0},\"iconCls\":\"icon-system\",\"value\":\"01573651\",\"_parentId\":\"9249ddce-146b-422d-baca-2047b6e71fe3\",\"created\":\"2022-02-16 11:00:06\",\"sortid\":0,\"target\":null,\"state\":null},{\"children\":[],\"text\":\"保安\",\"id\":\"a60f45e2-7b34-44f8-8321-140ac69da55b\",\"attributes\":{\"ID\":51,\"RGGUID\":\"a60f45e2-7b34-44f8-8321-140ac69da55b\",\"RGName\":\"保安\",\"RGCode\":\"01800362\",\"ParentId\":\"9249ddce-146b-422d-baca-2047b6e71fe3\",\"RGType\":1,\"Status\":0,\"CreatedOnUtc\":\"2022-03-22 15:34:25\",\"Remark\":null,\"RGFullPaths\":\"当升材料科技有限公司|门禁|保安;\",\"ParentName\":\"门禁\",\"IsParentChange\":0},\"iconCls\":\"icon-system\",\"value\":\"01800362\",\"_parentId\":\"9249ddce-146b-422d-baca-2047b6e71fe3\",\"created\":\"2022-03-22 15:34:25\",\"sortid\":0,\"target\":null,\"state\":null},{\"children\":[],\"text\":\"保洁\",\"id\":\"a47feb66-0be3-4504-82d5-18c8276a8eee\",\"attributes\":{\"ID\":46,\"RGGUID\":\"a47feb66-0be3-4504-82d5-18c8276a8eee\",\"RGName\":\"保洁\",\"RGCode\":\"51204121\",\"ParentId\":\"9249ddce-146b-422d-baca-2047b6e71fe3\",\"RGType\":1,\"Status\":0,\"CreatedOnUtc\":\"2022-03-09 13:40:44\",\"Remark\":null,\"RGFullPaths\":\"当升材料科技有限公司|门禁|保洁;\",\"ParentName\":\"门禁\",\"IsParentChange\":0},\"iconCls\":\"icon-system\",\"value\":\"51204121\",\"_parentId\":\"9249ddce-146b-422d-baca-2047b6e71fe3\",\"created\":\"2022-03-09 13:40:44\",\"sortid\":0,\"target\":null,\"state\":null},{\"children\":[],\"text\":\"财务部\",\"id\":\"1bf88700-8ec5-4479-9304-91a517d08d08\",\"attributes\":{\"ID\":26,\"RGGUID\":\"1bf88700-8ec5-4479-9304-91a517d08d08\",\"RGName\":\"财务部\",\"RGCode\":\"16752065\",\"ParentId\":\"9249ddce-146b-422d-baca-2047b6e71fe3\",\"RGType\":1,\"Status\":0,\"CreatedOnUtc\":\"2022-02-16 11:02:31\",\"Remark\":null,\"RGFullPaths\":\"当升材料科技有限公司|门禁|财务部;\",\"ParentName\":\"门禁\",\"IsParentChange\":0},\"iconCls\":\"icon-system\",\"value\":\"16752065\",\"_parentId\":\"9249ddce-146b-422d-baca-2047b6e71fe3\",\"created\":\"2022-02-16 11:02:31\",\"sortid\":0,\"target\":null,\"state\":null},{\"children\":[],\"text\":\"常州当升\",\"id\":\"192a903c-dd7d-4ff5-b676-baebd6c35b69\",\"attributes\":{\"ID\":58,\"RGGUID\":\"192a903c-dd7d-4ff5-b676-baebd6c35b69\",\"RGName\":\"常州当升\",\"RGCode\":\"15973608\",\"ParentId\":\"9249ddce-146b-422d-baca-2047b6e71fe3\",\"RGType\":1,\"Status\":0,\"CreatedOnUtc\":\"2022-08-01 09:20:21\",\"Remark\":null,\"RGFullPaths\":\"当升材料科技有限公司|门禁|常州当升;\",\"ParentName\":\"门禁\",\"IsParentChange\":0},\"iconCls\":\"icon-system\",\"value\":\"15973608\",\"_parentId\":\"9249ddce-146b-422d-baca-2047b6e71fe3\",\"created\":\"2022-08-01 09:20:21\",\"sortid\":0,\"target\":null,\"state\":null},{\"children\":[],\"text\":\"当升科技\",\"id\":\"9bfae837-224b-4811-862e-98880c288dc7\",\"attributes\":{\"ID\":47,\"RGGUID\":\"9bfae837-224b-4811-862e-98880c288dc7\",\"RGName\":\"当升科技\",\"RGCode\":\"95133263\",\"ParentId\":\"9249ddce-146b-422d-baca-2047b6e71fe3\",\"RGType\":1,\"Status\":0,\"CreatedOnUtc\":\"2022-03-13 10:36:41\",\"Remark\":null,\"RGFullPaths\":\"当升材料科技有限公司|门禁|当升科技;\",\"ParentName\":\"门禁\",\"IsParentChange\":0},\"iconCls\":\"icon-system\",\"value\":\"95133263\",\"_parentId\":\"9249ddce-146b-422d-baca-2047b6e71fe3\",\"created\":\"2022-03-13 10:36:41\",\"sortid\":0,\"target\":null,\"state\":null},{\"children\":[],\"text\":\"高管\",\"id\":\"274d893d-bea5-4beb-a172-8998b5831fe4\",\"attributes\":{\"ID\":18,\"RGGUID\":\"274d893d-bea5-4beb-a172-8998b5831fe4\",\"RGName\":\"高管\",\"RGCode\":\"92356840\",\"ParentId\":\"9249ddce-146b-422d-baca-2047b6e71fe3\",\"RGType\":1,\"Status\":0,\"CreatedOnUtc\":\"2022-02-16 10:59:03\",\"Remark\":null,\"RGFullPaths\":\"当升材料科技有限公司|门禁|高管;\",\"ParentName\":\"门禁\",\"IsParentChange\":0},\"iconCls\":\"icon-system\",\"value\":\"92356840\",\"_parentId\":\"9249ddce-146b-422d-baca-2047b6e71fe3\",\"created\":\"2022-02-16 10:59:03\",\"sortid\":0,\"target\":null,\"state\":null},{\"children\":[],\"text\":\"工程部\",\"id\":\"74180623-a3ea-42f4-b724-756abee46c15\",\"attributes\":{\"ID\":45,\"RGGUID\":\"74180623-a3ea-42f4-b724-756abee46c15\",\"RGName\":\"工程部\",\"RGCode\":\"62677093\",\"ParentId\":\"9249ddce-146b-422d-baca-2047b6e71fe3\",\"RGType\":1,\"Status\":0,\"CreatedOnUtc\":\"2022-02-28 13:31:54\",\"Remark\":null,\"RGFullPaths\":\"当升材料科技有限公司|门禁|工程部;\",\"ParentName\":\"门禁\",\"IsParentChange\":0},\"iconCls\":\"icon-system\",\"value\":\"62677093\",\"_parentId\":\"9249ddce-146b-422d-baca-2047b6e71fe3\",\"created\":\"2022-02-28 13:31:54\",\"sortid\":0,\"target\":null,\"state\":null},{\"children\":[],\"text\":\"工艺技术部\",\"id\":\"f39080ee-f4b2-4e84-98c3-474fc28e6661\",\"attributes\":{\"ID\":38,\"RGGUID\":\"f39080ee-f4b2-4e84-98c3-474fc28e6661\",\"RGName\":\"工艺技术部\",\"RGCode\":\"41931978\",\"ParentId\":\"9249ddce-146b-422d-baca-2047b6e71fe3\",\"RGType\":1,\"Status\":0,\"CreatedOnUtc\":\"2022-02-21 13:38:08\",\"Remark\":null,\"RGFullPaths\":\"当升材料科技有限公司|门禁|工艺技术部;\",\"ParentName\":\"门禁\",\"IsParentChange\":0},\"iconCls\":\"icon-system\",\"value\":\"41931978\",\"_parentId\":\"9249ddce-146b-422d-baca-2047b6e71fe3\",\"created\":\"2022-02-21 13:38:08\",\"sortid\":0,\"target\":null,\"state\":null},{\"children\":[],\"text\":\"后勤保障部\",\"id\":\"00583409-e326-4d25-9455-3f03240ac36e\",\"attributes\":{\"ID\":17,\"RGGUID\":\"00583409-e326-4d25-9455-3f03240ac36e\",\"RGName\":\"后勤保障部\",\"RGCode\":\"87961674\",\"ParentId\":\"9249ddce-146b-422d-baca-2047b6e71fe3\",\"RGType\":1,\"Status\":0,\"CreatedOnUtc\":\"2022-02-16 10:58:52\",\"Remark\":null,\"RGFullPaths\":\"当升材料科技有限公司|门禁|后勤保障部;\",\"ParentName\":\"门禁\",\"IsParentChange\":0},\"iconCls\":\"icon-system\",\"value\":\"87961674\",\"_parentId\":\"9249ddce-146b-422d-baca-2047b6e71fe3\",\"created\":\"2022-02-16 10:58:52\",\"sortid\":0,\"target\":null,\"state\":null},{\"children\":[],\"text\":\"精益小组\",\"id\":\"2392b6d1-e289-43f8-ae6f-b9bf236b14cd\",\"attributes\":{\"ID\":44,\"RGGUID\":\"2392b6d1-e289-43f8-ae6f-b9bf236b14cd\",\"RGName\":\"精益小组\",\"RGCode\":\"03722752\",\"ParentId\":\"9249ddce-146b-422d-baca-2047b6e71fe3\",\"RGType\":1,\"Status\":0,\"CreatedOnUtc\":\"2022-02-22 11:12:36\",\"Remark\":null,\"RGFullPaths\":\"当升材料科技有限公司|门禁|精益小组;\",\"ParentName\":\"门禁\",\"IsParentChange\":0},\"iconCls\":\"icon-system\",\"value\":\"03722752\",\"_parentId\":\"9249ddce-146b-422d-baca-2047b6e71fe3\",\"created\":\"2022-02-22 11:12:36\",\"sortid\":0,\"target\":null,\"state\":null},{\"children\":[],\"text\":\"人力资源部\",\"id\":\"87a13860-bd6d-4788-960d-3df566bd8f4a\",\"attributes\":{\"ID\":19,\"RGGUID\":\"87a13860-bd6d-4788-960d-3df566bd8f4a\",\"RGName\":\"人力资源部\",\"RGCode\":\"91024191\",\"ParentId\":\"9249ddce-146b-422d-baca-2047b6e71fe3\",\"RGType\":1,\"Status\":0,\"CreatedOnUtc\":\"2022-02-16 10:59:11\",\"Remark\":null,\"RGFullPaths\":\"当升材料科技有限公司|门禁|人力资源部;\",\"ParentName\":\"门禁\",\"IsParentChange\":0},\"iconCls\":\"icon-system\",\"value\":\"91024191\",\"_parentId\":\"9249ddce-146b-422d-baca-2047b6e71fe3\",\"created\":\"2022-02-16 10:59:11\",\"sortid\":0,\"target\":null,\"state\":null},{\"children\":[],\"text\":\"设备管理部\",\"id\":\"35cf8bbf-eb01-44a8-928f-9d94a167eb28\",\"attributes\":{\"ID\":24,\"RGGUID\":\"35cf8bbf-eb01-44a8-928f-9d94a167eb28\",\"RGName\":\"设备管理部\",\"RGCode\":\"22885768\",\"ParentId\":\"9249ddce-146b-422d-baca-2047b6e71fe3\",\"RGType\":1,\"Status\":0,\"CreatedOnUtc\":\"2022-02-16 11:01:47\",\"Remark\":null,\"RGFullPaths\":\"当升材料科技有限公司|门禁|设备管理部;\",\"ParentName\":\"门禁\",\"IsParentChange\":0},\"iconCls\":\"icon-system\",\"value\":\"22885768\",\"_parentId\":\"9249ddce-146b-422d-baca-2047b6e71fe3\",\"created\":\"2022-02-16 11:01:47\",\"sortid\":0,\"target\":null,\"state\":null},{\"children\":[{\"children\":[],\"text\":\"二部职能\",\"id\":\"161f6db1-1709-4e0a-832d-7de61ea6652a\",\"attributes\":{\"ID\":49,\"RGGUID\":\"161f6db1-1709-4e0a-832d-7de61ea6652a\",\"RGName\":\"二部职能\",\"RGCode\":\"92060103\",\"ParentId\":\"6b9496fb-9eb8-4c13-a953-69765e8f32e6\",\"RGType\":1,\"Status\":0,\"CreatedOnUtc\":\"2022-03-21 15:43:49\",\"Remark\":null,\"RGFullPaths\":\"当升材料科技有限公司|门禁|生产二部|二部职能;\",\"ParentName\":\"生产二部\",\"IsParentChange\":0},\"iconCls\":\"icon-system\",\"value\":\"92060103\",\"_parentId\":\"6b9496fb-9eb8-4c13-a953-69765e8f32e6\",\"created\":\"2022-03-21 15:43:49\",\"sortid\":0,\"target\":null,\"state\":null}],\"text\":\"生产二部\",\"id\":\"6b9496fb-9eb8-4c13-a953-69765e8f32e6\",\"attributes\":{\"ID\":21,\"RGGUID\":\"6b9496fb-9eb8-4c13-a953-69765e8f32e6\",\"RGName\":\"生产二部\",\"RGCode\":\"11043026\",\"ParentId\":\"9249ddce-146b-422d-baca-2047b6e71fe3\",\"RGType\":1,\"Status\":0,\"CreatedOnUtc\":\"2022-02-16 10:59:40\",\"Remark\":null,\"RGFullPaths\":\"当升材料科技有限公司|门禁|生产二部;\",\"ParentName\":\"门禁\",\"IsParentChange\":0},\"iconCls\":\"icon-system\",\"value\":\"11043026\",\"_parentId\":\"9249ddce-146b-422d-baca-2047b6e71fe3\",\"created\":\"2022-02-16 10:59:40\",\"sortid\":0,\"target\":null,\"state\":null},{\"children\":[],\"text\":\"生产管理部\",\"id\":\"84f07e39-fef1-42c5-ba93-48ece46da139\",\"attributes\":{\"ID\":22,\"RGGUID\":\"84f07e39-fef1-42c5-ba93-48ece46da139\",\"RGName\":\"生产管理部\",\"RGCode\":\"68040683\",\"ParentId\":\"9249ddce-146b-422d-baca-2047b6e71fe3\",\"RGType\":1,\"Status\":0,\"CreatedOnUtc\":\"2022-02-16 10:59:54\",\"Remark\":null,\"RGFullPaths\":\"当升材料科技有限公司|门禁|生产管理部;\",\"ParentName\":\"门禁\",\"IsParentChange\":0},\"iconCls\":\"icon-system\",\"value\":\"68040683\",\"_parentId\":\"9249ddce-146b-422d-baca-2047b6e71fe3\",\"created\":\"2022-02-16 10:59:54\",\"sortid\":0,\"target\":null,\"state\":null},{\"children\":[{\"children\":[],\"text\":\"一部职能\",\"id\":\"b1f5a8dc-ca4a-4bd9-9cc5-f1f2381fdf5f\",\"attributes\":{\"ID\":48,\"RGGUID\":\"b1f5a8dc-ca4a-4bd9-9cc5-f1f2381fdf5f\",\"RGName\":\"一部职能\",\"RGCode\":\"53601681\",\"ParentId\":\"45f26faa-9cf8-469c-8175-54a66eb4dfca\",\"RGType\":1,\"Status\":0,\"CreatedOnUtc\":\"2022-03-21 15:38:25\",\"Remark\":null,\"RGFullPaths\":\"当升材料科技有限公司|门禁|生产一部|一部职能;\",\"ParentName\":\"生产一部\",\"IsParentChange\":0},\"iconCls\":\"icon-system\",\"value\":\"53601681\",\"_parentId\":\"45f26faa-9cf8-469c-8175-54a66eb4dfca\",\"created\":\"2022-03-21 15:38:25\",\"sortid\":0,\"target\":null,\"state\":null}],\"text\":\"生产一部\",\"id\":\"45f26faa-9cf8-469c-8175-54a66eb4dfca\",\"attributes\":{\"ID\":20,\"RGGUID\":\"45f26faa-9cf8-469c-8175-54a66eb4dfca\",\"RGName\":\"生产一部\",\"RGCode\":\"37305120\",\"ParentId\":\"9249ddce-146b-422d-baca-2047b6e71fe3\",\"RGType\":1,\"Status\":0,\"CreatedOnUtc\":\"2022-02-16 10:59:24\",\"Remark\":null,\"RGFullPaths\":\"当升材料科技有限公司|门禁|生产一部;\",\"ParentName\":\"门禁\",\"IsParentChange\":0},\"iconCls\":\"icon-system\",\"value\":\"37305120\",\"_parentId\":\"9249ddce-146b-422d-baca-2047b6e71fe3\",\"created\":\"2022-02-16 10:59:24\",\"sortid\":0,\"target\":null,\"state\":null},{\"children\":[],\"text\":\"食堂\",\"id\":\"78238ea5-93c2-4d73-a718-be166b821a12\",\"attributes\":{\"ID\":53,\"RGGUID\":\"78238ea5-93c2-4d73-a718-be166b821a12\",\"RGName\":\"食堂\",\"RGCode\":\"70431756\",\"ParentId\":\"9249ddce-146b-422d-baca-2047b6e71fe3\",\"RGType\":1,\"Status\":0,\"CreatedOnUtc\":\"2022-05-19 18:25:21\",\"Remark\":null,\"RGFullPaths\":\"当升材料科技有限公司|门禁|食堂;\",\"ParentName\":\"门禁\",\"IsParentChange\":0},\"iconCls\":\"icon-system\",\"value\":\"70431756\",\"_parentId\":\"9249ddce-146b-422d-baca-2047b6e71fe3\",\"created\":\"2022-05-19 18:25:21\",\"sortid\":0,\"target\":null,\"state\":null},{\"children\":[],\"text\":\"外包单位\",\"id\":\"bfeee420-04cc-4f02-96ad-2f726cccb316\",\"attributes\":{\"ID\":55,\"RGGUID\":\"bfeee420-04cc-4f02-96ad-2f726cccb316\",\"RGName\":\"外包单位\",\"RGCode\":\"09728934\",\"ParentId\":\"9249ddce-146b-422d-baca-2047b6e71fe3\",\"RGType\":1,\"Status\":0,\"CreatedOnUtc\":\"2022-07-11 09:02:47\",\"Remark\":null,\"RGFullPaths\":\"当升材料科技有限公司|门禁|外包单位;\",\"ParentName\":\"门禁\",\"IsParentChange\":0},\"iconCls\":\"icon-system\",\"value\":\"09728934\",\"_parentId\":\"9249ddce-146b-422d-baca-2047b6e71fe3\",\"created\":\"2022-07-11 09:02:47\",\"sortid\":0,\"target\":null,\"state\":null},{\"children\":[{\"children\":[],\"text\":\"检测员\",\"id\":\"faed8476-013d-4bf3-ad6f-6889186248dd\",\"attributes\":{\"ID\":50,\"RGGUID\":\"faed8476-013d-4bf3-ad6f-6889186248dd\",\"RGName\":\"检测员\",\"RGCode\":\"95967496\",\"ParentId\":\"a1f24999-7a32-48e2-96b0-18daadf64595\",\"RGType\":1,\"Status\":0,\"CreatedOnUtc\":\"2022-03-21 16:47:22\",\"Remark\":null,\"RGFullPaths\":\"当升材料科技有限公司|门禁|质量管理部|检测员;\",\"ParentName\":\"质量管理部\",\"IsParentChange\":0},\"iconCls\":\"icon-system\",\"value\":\"95967496\",\"_parentId\":\"a1f24999-7a32-48e2-96b0-18daadf64595\",\"created\":\"2022-03-21 16:47:22\",\"sortid\":0,\"target\":null,\"state\":null}],\"text\":\"质量管理部\",\"id\":\"a1f24999-7a32-48e2-96b0-18daadf64595\",\"attributes\":{\"ID\":25,\"RGGUID\":\"a1f24999-7a32-48e2-96b0-18daadf64595\",\"RGName\":\"质量管理部\",\"RGCode\":\"19167513\",\"ParentId\":\"9249ddce-146b-422d-baca-2047b6e71fe3\",\"RGType\":1,\"Status\":0,\"CreatedOnUtc\":\"2022-02-16 11:02:14\",\"Remark\":null,\"RGFullPaths\":\"当升材料科技有限公司|门禁|质量管理部;\",\"ParentName\":\"门禁\",\"IsParentChange\":0},\"iconCls\":\"icon-system\",\"value\":\"19167513\",\"_parentId\":\"9249ddce-146b-422d-baca-2047b6e71fe3\",\"created\":\"2022-02-16 11:02:14\",\"sortid\":0,\"target\":null,\"state\":null}],\"text\":\"门禁\",\"id\":\"9249ddce-146b-422d-baca-2047b6e71fe3\",\"attributes\":{\"ID\":15,\"RGGUID\":\"9249ddce-146b-422d-baca-2047b6e71fe3\",\"RGName\":\"门禁\",\"RGCode\":\"94576283\",\"ParentId\":\"5a9dc528-e7ad-4952-9f21-a885b6564a1c\",\"RGType\":1,\"Status\":0,\"CreatedOnUtc\":\"2022-02-16 10:57:17\",\"Remark\":null,\"RGFullPaths\":\"当升材料科技有限公司|门禁;\",\"ParentName\":\"当升材料科技有限公司\",\"IsParentChange\":0},\"iconCls\":\"icon-system\",\"value\":\"94576283\",\"_parentId\":\"5a9dc528-e7ad-4952-9f21-a885b6564a1c\",\"created\":\"2022-02-16 10:57:17\",\"sortid\":0,\"target\":null,\"state\":null},{\"children\":[{\"children\":[],\"text\":\"车场-安全环保部\",\"id\":\"52d3cbb2-e336-4323-80af-87fcc22314e7\",\"attributes\":{\"ID\":36,\"RGGUID\":\"52d3cbb2-e336-4323-80af-87fcc22314e7\",\"RGName\":\"车场-安全环保部\",\"RGCode\":\"17473134\",\"ParentId\":\"77d468d5-684b-45da-a42f-d71b8a59d228\",\"RGType\":1,\"Status\":0,\"CreatedOnUtc\":\"2022-02-16 11:05:45\",\"Remark\":null,\"RGFullPaths\":\"当升材料科技有限公司|停车场|车场-安全环保部;\",\"ParentName\":\"停车场\",\"IsParentChange\":0},\"iconCls\":\"icon-system\",\"value\":\"17473134\",\"_parentId\":\"77d468d5-684b-45da-a42f-d71b8a59d228\",\"created\":\"2022-02-16 11:05:45\",\"sortid\":0,\"target\":null,\"state\":null},{\"children\":[],\"text\":\"车场-财务部\",\"id\":\"0d684412-a3bf-47b7-92cb-093e78c71370\",\"attributes\":{\"ID\":28,\"RGGUID\":\"0d684412-a3bf-47b7-92cb-093e78c71370\",\"RGName\":\"车场-财务部\",\"RGCode\":\"66840896\",\"ParentId\":\"77d468d5-684b-45da-a42f-d71b8a59d228\",\"RGType\":1,\"Status\":0,\"CreatedOnUtc\":\"2022-02-16 11:03:34\",\"Remark\":null,\"RGFullPaths\":\"当升材料科技有限公司|停车场|车场-财务部;\",\"ParentName\":\"停车场\",\"IsParentChange\":0},\"iconCls\":\"icon-system\",\"value\":\"66840896\",\"_parentId\":\"77d468d5-684b-45da-a42f-d71b8a59d228\",\"created\":\"2022-02-16 11:03:34\",\"sortid\":0,\"target\":null,\"state\":null},{\"children\":[],\"text\":\"车场-高管\",\"id\":\"bae6d2e6-6fec-4e72-8f50-23315ebf0862\",\"attributes\":{\"ID\":27,\"RGGUID\":\"bae6d2e6-6fec-4e72-8f50-23315ebf0862\",\"RGName\":\"车场-高管\",\"RGCode\":\"43783300\",\"ParentId\":\"77d468d5-684b-45da-a42f-d71b8a59d228\",\"RGType\":1,\"Status\":0,\"CreatedOnUtc\":\"2022-02-16 11:02:53\",\"Remark\":null,\"RGFullPaths\":\"当升材料科技有限公司|停车场|车场-高管;\",\"ParentName\":\"停车场\",\"IsParentChange\":0},\"iconCls\":\"icon-system\",\"value\":\"43783300\",\"_parentId\":\"77d468d5-684b-45da-a42f-d71b8a59d228\",\"created\":\"2022-02-16 11:02:53\",\"sortid\":0,\"target\":null,\"state\":null},{\"children\":[],\"text\":\"车场-工程\",\"id\":\"4caff07e-b335-44d8-957b-88c4e5f93b6e\",\"attributes\":{\"ID\":52,\"RGGUID\":\"4caff07e-b335-44d8-957b-88c4e5f93b6e\",\"RGName\":\"车场-工程\",\"RGCode\":\"32689175\",\"ParentId\":\"77d468d5-684b-45da-a42f-d71b8a59d228\",\"RGType\":1,\"Status\":0,\"CreatedOnUtc\":\"2022-04-26 15:02:29\",\"Remark\":null,\"RGFullPaths\":\"当升材料科技有限公司|停车场|车场-工程;\",\"ParentName\":\"停车场\",\"IsParentChange\":0},\"iconCls\":\"icon-system\",\"value\":\"32689175\",\"_parentId\":\"77d468d5-684b-45da-a42f-d71b8a59d228\",\"created\":\"2022-04-26 15:02:29\",\"sortid\":0,\"target\":null,\"state\":null},{\"children\":[],\"text\":\"车场-工艺技术部\",\"id\":\"83569f4e-e1f1-46db-93d9-343fd99fb203\",\"attributes\":{\"ID\":37,\"RGGUID\":\"83569f4e-e1f1-46db-93d9-343fd99fb203\",\"RGName\":\"车场-工艺技术部\",\"RGCode\":\"53614410\",\"ParentId\":\"77d468d5-684b-45da-a42f-d71b8a59d228\",\"RGType\":1,\"Status\":0,\"CreatedOnUtc\":\"2022-02-21 13:37:28\",\"Remark\":null,\"RGFullPaths\":\"当升材料科技有限公司|停车场|车场-工艺技术部;\",\"ParentName\":\"停车场\",\"IsParentChange\":0},\"iconCls\":\"icon-system\",\"value\":\"53614410\",\"_parentId\":\"77d468d5-684b-45da-a42f-d71b8a59d228\",\"created\":\"2022-02-21 13:37:28\",\"sortid\":0,\"target\":null,\"state\":null},{\"children\":[],\"text\":\"车场-公车\",\"id\":\"e85f77f3-df57-4d73-9381-67ef18f154d3\",\"attributes\":{\"ID\":41,\"RGGUID\":\"e85f77f3-df57-4d73-9381-67ef18f154d3\",\"RGName\":\"车场-公车\",\"RGCode\":\"23012187\",\"ParentId\":\"77d468d5-684b-45da-a42f-d71b8a59d228\",\"RGType\":1,\"Status\":0,\"CreatedOnUtc\":\"2022-02-21 18:11:00\",\"Remark\":null,\"RGFullPaths\":\"当升材料科技有限公司|停车场|车场-公车;\",\"ParentName\":\"停车场\",\"IsParentChange\":0},\"iconCls\":\"icon-system\",\"value\":\"23012187\",\"_parentId\":\"77d468d5-684b-45da-a42f-d71b8a59d228\",\"created\":\"2022-02-21 18:11:00\",\"sortid\":0,\"target\":null,\"state\":null},{\"children\":[],\"text\":\"车场-后勤保障部\",\"id\":\"312ec1e3-ace1-43f3-8a52-c3f0463a034e\",\"attributes\":{\"ID\":29,\"RGGUID\":\"312ec1e3-ace1-43f3-8a52-c3f0463a034e\",\"RGName\":\"车场-后勤保障部\",\"RGCode\":\"73880115\",\"ParentId\":\"77d468d5-684b-45da-a42f-d71b8a59d228\",\"RGType\":1,\"Status\":0,\"CreatedOnUtc\":\"2022-02-16 11:03:50\",\"Remark\":null,\"RGFullPaths\":\"当升材料科技有限公司|停车场|车场-后勤保障部;\",\"ParentName\":\"停车场\",\"IsParentChange\":0},\"iconCls\":\"icon-system\",\"value\":\"73880115\",\"_parentId\":\"77d468d5-684b-45da-a42f-d71b8a59d228\",\"created\":\"2022-02-16 11:03:50\",\"sortid\":0,\"target\":null,\"state\":null},{\"children\":[],\"text\":\"车场-精益小组\",\"id\":\"05d90fc8-0ae0-444a-9f7a-ee428d475f3f\",\"attributes\":{\"ID\":43,\"RGGUID\":\"05d90fc8-0ae0-444a-9f7a-ee428d475f3f\",\"RGName\":\"车场-精益小组\",\"RGCode\":\"80116252\",\"ParentId\":\"77d468d5-684b-45da-a42f-d71b8a59d228\",\"RGType\":1,\"Status\":0,\"CreatedOnUtc\":\"2022-02-22 11:08:55\",\"Remark\":null,\"RGFullPaths\":\"当升材料科技有限公司|停车场|车场-精益小组;\",\"ParentName\":\"停车场\",\"IsParentChange\":0},\"iconCls\":\"icon-system\",\"value\":\"80116252\",\"_parentId\":\"77d468d5-684b-45da-a42f-d71b8a59d228\",\"created\":\"2022-02-22 11:08:55\",\"sortid\":0,\"target\":null,\"state\":null},{\"children\":[],\"text\":\"车场-人力资源部\",\"id\":\"3ed66d59-384b-4ae5-9af4-725d30fff213\",\"attributes\":{\"ID\":30,\"RGGUID\":\"3ed66d59-384b-4ae5-9af4-725d30fff213\",\"RGName\":\"车场-人力资源部\",\"RGCode\":\"42978585\",\"ParentId\":\"77d468d5-684b-45da-a42f-d71b8a59d228\",\"RGType\":1,\"Status\":0,\"CreatedOnUtc\":\"2022-02-16 11:04:03\",\"Remark\":null,\"RGFullPaths\":\"当升材料科技有限公司|停车场|车场-人力资源部;\",\"ParentName\":\"停车场\",\"IsParentChange\":0},\"iconCls\":\"icon-system\",\"value\":\"42978585\",\"_parentId\":\"77d468d5-684b-45da-a42f-d71b8a59d228\",\"created\":\"2022-02-16 11:04:03\",\"sortid\":0,\"target\":null,\"state\":null},{\"children\":[],\"text\":\"车场-设备管理部\",\"id\":\"7d18428e-e2c3-40e2-996a-c0796427528c\",\"attributes\":{\"ID\":35,\"RGGUID\":\"7d18428e-e2c3-40e2-996a-c0796427528c\",\"RGName\":\"车场-设备管理部\",\"RGCode\":\"34631296\",\"ParentId\":\"77d468d5-684b-45da-a42f-d71b8a59d228\",\"RGType\":1,\"Status\":0,\"CreatedOnUtc\":\"2022-02-16 11:05:28\",\"Remark\":null,\"RGFullPaths\":\"当升材料科技有限公司|停车场|车场-设备管理部;\",\"ParentName\":\"停车场\",\"IsParentChange\":0},\"iconCls\":\"icon-system\",\"value\":\"34631296\",\"_parentId\":\"77d468d5-684b-45da-a42f-d71b8a59d228\",\"created\":\"2022-02-16 11:05:28\",\"sortid\":0,\"target\":null,\"state\":null},{\"children\":[],\"text\":\"车场-生产二部\",\"id\":\"029b25b0-3f05-4b1c-9bd6-c55873845648\",\"attributes\":{\"ID\":33,\"RGGUID\":\"029b25b0-3f05-4b1c-9bd6-c55873845648\",\"RGName\":\"车场-生产二部\",\"RGCode\":\"77404287\",\"ParentId\":\"77d468d5-684b-45da-a42f-d71b8a59d228\",\"RGType\":1,\"Status\":0,\"CreatedOnUtc\":\"2022-02-16 11:05:08\",\"Remark\":null,\"RGFullPaths\":\"当升材料科技有限公司|停车场|车场-生产二部;\",\"ParentName\":\"停车场\",\"IsParentChange\":0},\"iconCls\":\"icon-system\",\"value\":\"77404287\",\"_parentId\":\"77d468d5-684b-45da-a42f-d71b8a59d228\",\"created\":\"2022-02-16 11:05:08\",\"sortid\":0,\"target\":null,\"state\":null},{\"children\":[],\"text\":\"车场-生产管理部\",\"id\":\"c4e43870-927a-4bbb-92a5-90f386b0a96a\",\"attributes\":{\"ID\":31,\"RGGUID\":\"c4e43870-927a-4bbb-92a5-90f386b0a96a\",\"RGName\":\"车场-生产管理部\",\"RGCode\":\"78639259\",\"ParentId\":\"77d468d5-684b-45da-a42f-d71b8a59d228\",\"RGType\":1,\"Status\":0,\"CreatedOnUtc\":\"2022-02-16 11:04:16\",\"Remark\":null,\"RGFullPaths\":\"当升材料科技有限公司|停车场|车场-生产管理部;\",\"ParentName\":\"停车场\",\"IsParentChange\":0},\"iconCls\":\"icon-system\",\"value\":\"78639259\",\"_parentId\":\"77d468d5-684b-45da-a42f-d71b8a59d228\",\"created\":\"2022-02-16 11:04:16\",\"sortid\":0,\"target\":null,\"state\":null},{\"children\":[],\"text\":\"车场-生产一部\",\"id\":\"2651866f-f778-47dd-a7b3-8a76e3fb1a10\",\"attributes\":{\"ID\":32,\"RGGUID\":\"2651866f-f778-47dd-a7b3-8a76e3fb1a10\",\"RGName\":\"车场-生产一部\",\"RGCode\":\"12326821\",\"ParentId\":\"77d468d5-684b-45da-a42f-d71b8a59d228\",\"RGType\":1,\"Status\":0,\"CreatedOnUtc\":\"2022-02-16 11:04:58\",\"Remark\":null,\"RGFullPaths\":\"当升材料科技有限公司|停车场|车场-生产一部;\",\"ParentName\":\"停车场\",\"IsParentChange\":0},\"iconCls\":\"icon-system\",\"value\":\"12326821\",\"_parentId\":\"77d468d5-684b-45da-a42f-d71b8a59d228\",\"created\":\"2022-02-16 11:04:58\",\"sortid\":0,\"target\":null,\"state\":null},{\"children\":[],\"text\":\"车场-质量管理部\",\"id\":\"22ed78ad-b7ca-4a7f-8965-b2be6b595837\",\"attributes\":{\"ID\":34,\"RGGUID\":\"22ed78ad-b7ca-4a7f-8965-b2be6b595837\",\"RGName\":\"车场-质量管理部\",\"RGCode\":\"38621945\",\"ParentId\":\"77d468d5-684b-45da-a42f-d71b8a59d228\",\"RGType\":1,\"Status\":0,\"CreatedOnUtc\":\"2022-02-16 11:05:17\",\"Remark\":null,\"RGFullPaths\":\"当升材料科技有限公司|停车场|车场-质量管理部;\",\"ParentName\":\"停车场\",\"IsParentChange\":0},\"iconCls\":\"icon-system\",\"value\":\"38621945\",\"_parentId\":\"77d468d5-684b-45da-a42f-d71b8a59d228\",\"created\":\"2022-02-16 11:05:17\",\"sortid\":0,\"target\":null,\"state\":null}],\"text\":\"停车场\",\"id\":\"77d468d5-684b-45da-a42f-d71b8a59d228\",\"attributes\":{\"ID\":16,\"RGGUID\":\"77d468d5-684b-45da-a42f-d71b8a59d228\",\"RGName\":\"停车场\",\"RGCode\":\"72276349\",\"ParentId\":\"5a9dc528-e7ad-4952-9f21-a885b6564a1c\",\"RGType\":1,\"Status\":0,\"CreatedOnUtc\":\"2022-02-16 10:58:06\",\"Remark\":null,\"RGFullPaths\":\"当升材料科技有限公司|停车场;\",\"ParentName\":\"当升材料科技有限公司\",\"IsParentChange\":0},\"iconCls\":\"icon-system\",\"value\":\"72276349\",\"_parentId\":\"5a9dc528-e7ad-4952-9f21-a885b6564a1c\",\"created\":\"2022-02-16 10:58:06\",\"sortid\":0,\"target\":null,\"state\":null},{\"children\":[],\"text\":\"停车场-车场-高管\",\"id\":\"21b2bcf0-420c-4b9b-b708-c137640a5e68\",\"attributes\":{\"ID\":42,\"RGGUID\":\"21b2bcf0-420c-4b9b-b708-c137640a5e68\",\"RGName\":\"停车场-车场-高管\",\"RGCode\":\"024866858\",\"ParentId\":\"5a9dc528-e7ad-4952-9f21-a885b6564a1c\",\"RGType\":1,\"Status\":0,\"CreatedOnUtc\":\"2022-02-21 18:17:26\",\"Remark\":\"导入人事自动新增组织\",\"RGFullPaths\":\"当升材料科技有限公司|停车场-车场-高管;\",\"ParentName\":\"当升材料科技有限公司\",\"IsParentChange\":0},\"iconCls\":\"icon-system\",\"value\":\"024866858\",\"_parentId\":\"5a9dc528-e7ad-4952-9f21-a885b6564a1c\",\"created\":\"2022-02-21 18:17:26\",\"sortid\":0,\"target\":null,\"state\":null},{\"children\":[],\"text\":\"月卡车\",\"id\":\"747c8db8-e416-4191-8ae7-fbd115fae29b\",\"attributes\":{\"ID\":39,\"RGGUID\":\"747c8db8-e416-4191-8ae7-fbd115fae29b\",\"RGName\":\"月卡车\",\"RGCode\":\"024866858\",\"ParentId\":\"5a9dc528-e7ad-4952-9f21-a885b6564a1c\",\"RGType\":1,\"Status\":0,\"CreatedOnUtc\":\"2022-02-21 17:42:06\",\"Remark\":\"导入人事自动新增组织\",\"RGFullPaths\":\"当升材料科技有限公司|月卡车;\",\"ParentName\":\"当升材料科技有限公司\",\"IsParentChange\":0},\"iconCls\":\"icon-system\",\"value\":\"024866858\",\"_parentId\":\"5a9dc528-e7ad-4952-9f21-a885b6564a1c\",\"created\":\"2022-02-21 17:42:06\",\"sortid\":0,\"target\":null,\"state\":null}],\"text\":\"当升材料科技有限公司\",\"id\":\"5a9dc528-e7ad-4952-9f21-a885b6564a1c\",\"attributes\":{\"ID\":1,\"RGGUID\":\"5a9dc528-e7ad-4952-9f21-a885b6564a1c\",\"RGName\":\"当升材料科技有限公司\",\"RGCode\":\"2033559983\",\"ParentId\":\"00000000-0000-0000-0000-000000000000\",\"RGType\":1,\"Status\":0,\"CreatedOnUtc\":\"2018-01-12 11:47:33\",\"Remark\":\"保存项目信息\",\"RGFullPaths\":\"当升材料科技有限公司;\",\"ParentName\":\"\",\"IsParentChange\":0},\"iconCls\":\"icon-system\",\"value\":\"2033559983\",\"_parentId\":\"00000000-0000-0000-0000-000000000000\",\"created\":\"2018-01-12 11:47:33\",\"sortid\":0,\"target\":null,\"state\":null}]\n";
        if (StrUtil.isNotEmpty(body)) {
          return body;
        }
        return null;
    }

    @Override
    public boolean authPark(JSONObject object) {
        String random=RandomUtil.randomNumbers(6);
        long time=DateUtil.currentSeconds();
        String body = HttpRequest.post("http://192.168.202.10:8091/api/park/downparkcredential")
                .header("appId","app01")
                .header("v","1.0")
                .header("random",random)
                .header("timestamp", Convert.toStr(time))
                .header("sign",getMd5(random,time))
                .body(object.toString())
                .execute()
                .body();
        if (StrUtil.isNotEmpty(body)) {
            JSONObject object1 = JSONUtil.parseObj(body);
            Object code = object1.get("code");
            if(code!=null&&code.toString().equals("0"))
            {
                return true;
            }
        }
        return false;
    }

    @Override
    public boolean addPerson(JSONObject object) {
        String random=RandomUtil.randomNumbers(6);
        long time=DateUtil.currentSeconds();
        String body = HttpRequest.post("http://192.168.202.10:8091/api/base/addperson")
                .header("appId","app01")
                .header("v","1.0")
                .header("random",random)
                .header("timestamp", Convert.toStr(time))
                .header("sign",getMd5(random,time))
                .body(object.toString())
                .execute()
                .body();
        if (StrUtil.isNotEmpty(body)) {
            JSONObject object1 = JSONUtil.parseObj(body);
            Object code = object1.get("code");
            if(code!=null&&code.toString().equals("0"))
            {
                return true;
            }
        }
        return false;
    }

    public String login() {
        Map<String, Object> param = new HashMap<>();
        param.put("username", username);
        param.put("password", password);
        List<HttpCookie> cookies = HttpRequest.post(url + "/Auth/Signin")
                .body(JSONUtil.toJsonStr(param))
                .execute()
                .getCookies();
        log.error(JSONUtil.toJsonStr(cookies));
        String cookie = "DefaultSystem=JieLink";
        if (CollUtil.isNotEmpty(cookies)) {
            for (HttpCookie k : cookies) {
                cookie += ";";
                cookie += k.getName() + "=" + k.getValue();
            }
        }

        log.error(cookie);
        return cookie;

    }


    private String getCookie() {
        String js_token = redisHelper.StringGet(0, "js_token");
        if (StrUtil.isEmpty(js_token)) {
            String login = login();
            log.error("#####login"+login);
            if (StrUtil.isNotEmpty(login)) {
                redisHelper.StringSet(0, "js_token", login, 2, TimeUnit.HOURS);
                return login;
            }

        }
        return js_token;


    }


    private String getMd5(String random,long time)
    {
        String code="random" + random + "timestamp" + time+ "key" + "78a26764-b231-11eb-a735-fa73635175fa";
        return  DigestUtil.md5Hex(code).toUpperCase();

    }


}
