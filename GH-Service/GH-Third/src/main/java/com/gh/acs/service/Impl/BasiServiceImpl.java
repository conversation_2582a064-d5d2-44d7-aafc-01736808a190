package com.gh.acs.service.Impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.Mode;
import cn.hutool.crypto.Padding;
import cn.hutool.crypto.symmetric.AES;
import cn.hutool.http.HttpRequest;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gh.acs.mapper.basiMapper;
import com.gh.acs.model.vo.DeviceSsuVO;
import com.gh.acs.service.BasiService;
import com.gh.common.redis.RedisHelper;
import com.gh.common.utils.PageResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * Description:
 * User: zhangkeguang
 * Date: 2023-07-13-11:13
 */

@Service
@Slf4j
public class BasiServiceImpl implements BasiService {

    @Value("${basi.appid}")
    private String appid;

    @Value("${basi.secret}")
    private String secret;


    @Value("${basi.key}")
    private String key;



    @Value("${basi.url}")
    private String url;


    @Autowired
    private RedisHelper redisHelper;

    @Autowired
    private basiMapper mapper;

    @Override
    public String getBuildings() {
        String path= url + "/basapi/building/v1.0.0/buildinglist";
        String token = redisHelper.StringGet(0, "basi");
        if(StrUtil.isBlank(token))
        {
            token=getToken();
        }
        if(StrUtil.isNotEmpty(token))
        {
            Map<String,Object> param=new HashMap<>();
            param.put("appid",appid);
            param.put("accesstoken",getEncryptKey(token));
            String body = HttpRequest.post(path).body(JSONUtil.toJsonStr(param))
                    .execute().body();
            return body;
        }
        return null;

    }


    @Override
    public String userssuinfo(String buildingid, String usersn) {
        String path= url + "/basapi/user/v1.0.0/userssuinfo";
        String token = redisHelper.StringGet(0, "basi");
        if(StrUtil.isBlank(token))
        {
            token=getToken();
        }
        if(StrUtil.isNotEmpty(token))
        {
            Map<String,Object> param=new HashMap<>();
            param.put("appid",appid);
            param.put("accesstoken",getEncryptKey(token));
            if(StrUtil.isNotEmpty(buildingid))
            {
                param.put("buildingid",buildingid);
            }
            if(StrUtil.isNotEmpty(usersn))
            {
                param.put("usersn",usersn);
            }
            String body = HttpRequest.post(path).body(JSONUtil.toJsonStr(param))
                    .execute().body();
            return body;
        }
        return null;
    }

    @Override
    public String userlist(String buildingid, Integer pagesize, Integer pageindex) {
        String path= url + "/basapi/building/v1.0.0/userlist";
        String token = redisHelper.StringGet(0, "basi");
        if(StrUtil.isBlank(token))
        {
            token=getToken();
        }
        if(StrUtil.isNotEmpty(token))
        {
            Map<String,Object> param=new HashMap<>();
            param.put("appid",appid);
            param.put("accesstoken",getEncryptKey(token));
            if(StrUtil.isNotEmpty(buildingid))
            {
                param.put("buildingid",buildingid);
            }
            if(pagesize!=null)
            {
                param.put("pagesize",pagesize);
            }
            if(pageindex!=null)
            {
                param.put("pageindex",pageindex);
            }
            String body = HttpRequest.post(path).body(JSONUtil.toJsonStr(param))
                    .execute().body();
            return body;
        }
        return null;
    }

    @Override
    public String currentdata(List<String> ids) {
        String path= url + "/basapi/ssu/v1.0.0/currentdata";
        String token = redisHelper.StringGet(0, "basi");
        if(StrUtil.isBlank(token))
        {
            token=getToken();
        }
        if(StrUtil.isNotEmpty(token))
        {
            Map<String,Object> param=new HashMap<>();
            param.put("appid",appid);
            param.put("accesstoken",getEncryptKey(token));
            if(CollUtil.isNotEmpty(ids))
            {
                JSONArray ssuidlist=new JSONArray();
                for(String id:ids)
                {
                    JSONObject ssuid=new JSONObject();
                    ssuid.set("ssuid",id);
                    ssuidlist.add(ssuid);
                }
                param.put("ssuidlist",ssuidlist);

            }
            String body = HttpRequest.post(path).body(JSONUtil.toJsonStr(param))
                    .execute().body();
            return body;
        }
        return null;
    }

    @Override
    public String historydata(List<String> ids, String daydt) {
        String path= url + "/basapi/ssu/v1.0.0/historydata";
        String token = redisHelper.StringGet(0, "basi");
        if(StrUtil.isBlank(token))
        {
            token=getToken();
        }
        if(StrUtil.isNotEmpty(token))
        {
            Map<String,Object> param=new HashMap<>();
            param.put("appid",appid);
            param.put("accesstoken",getEncryptKey(token));
            if(CollUtil.isNotEmpty(ids))
            {
                JSONArray ssuidlist=new JSONArray();
                for(String id:ids)
                {
                    JSONObject ssuid=new JSONObject();
                    ssuid.set("ssuid",id);
                    ssuidlist.add(ssuid);
                }
                param.put("ssuidlist",ssuidlist);

            }
            if(StrUtil.isNotEmpty(daydt))
            {
                param.put("daydt",daydt);
            }
            String body = HttpRequest.post(path).body(JSONUtil.toJsonStr(param))
                    .execute().body();
            return body;
        }
        return null;
    }

    @Override
    public String fmulistinfo(String buildingid, String fmuid) {
        String path= url + "/basapi/fmu/v1.0.0/fmulistinfo";
        String token = redisHelper.StringGet(0, "basi");
        if(StrUtil.isBlank(token))
        {
            token=getToken();
        }
        if(StrUtil.isNotEmpty(token))
        {
            Map<String,Object> param=new HashMap<>();
            param.put("appid",appid);
            param.put("accesstoken",getEncryptKey(token));
            if(StrUtil.isNotEmpty(buildingid))
            {
                param.put("buildingid",buildingid);
            }
            if(StrUtil.isNotEmpty(fmuid))
            {
                param.put("fmuid",fmuid);
            }
            String body = HttpRequest.post(path).body(JSONUtil.toJsonStr(param))
                    .execute().body();
            return body;
        }
        return null;
    }

    @Override
    public String ssulistdata(String buildingid, String fmuid, String startssuaddr, String endssuaddr) {
        String path= url + "/basapi/fmu/v1.0.0/ssulistdata";
        String token = redisHelper.StringGet(0, "basi");
        if(StrUtil.isBlank(token))
        {
            token=getToken();
        }
        if(StrUtil.isNotEmpty(token))
        {
            Map<String,Object> param=new HashMap<>();
            param.put("appid",appid);
            param.put("accesstoken",getEncryptKey(token));
            if(StrUtil.isNotEmpty(buildingid))
            {
                param.put("buildingid",buildingid);
            }
            if(StrUtil.isNotEmpty(fmuid))
            {
                param.put("fmuid",fmuid);
            }
            if(StrUtil.isNotEmpty(startssuaddr))
            {
                param.put("startssuaddr",startssuaddr);
            }
            if(StrUtil.isNotEmpty(endssuaddr))
            {
                param.put("endssuaddr",endssuaddr);
            }
            String body = HttpRequest.post(path).body(JSONUtil.toJsonStr(param))
                    .execute().body();
            return body;
        }
        return null;
    }

    @Override
    public String usermeterlist(String buildingsn, Integer pagesize, Integer pageindex) {
        String path= url + "/basapi/building/v1.0.0/usermeterlist";
        String token = redisHelper.StringGet(0, "basi");
        if(StrUtil.isBlank(token))
        {
            token=getToken();
        }
        if(StrUtil.isNotEmpty(token))
        {
            Map<String,Object> param=new HashMap<>();
            param.put("appid",appid);
            param.put("accesstoken",getEncryptKey(token));
            if(StrUtil.isNotEmpty(buildingsn))
            {
                param.put("buildingsn",buildingsn);
            }
            if(pagesize!=null)
            {
                param.put("pagesize",pagesize);
            }
            if(pageindex!=null)
            {
                param.put("pageindex",pageindex);
            }
            String body = HttpRequest.post(path).body(JSONUtil.toJsonStr(param))
                    .execute().body();
            return body;
        }
        return null;
    }

    @Override
    public String readssustate(List<String> ids) {
        String path= url + "/basapi/ssu/v1.0.0/readssustate";
        String token = redisHelper.StringGet(0, "basi");
        if(StrUtil.isBlank(token))
        {
            token=getToken();
        }
        if(StrUtil.isNotEmpty(token))
        {
            Map<String,Object> param=new HashMap<>();
            param.put("appid",appid);
            param.put("accesstoken",getEncryptKey(token));
            if(CollUtil.isNotEmpty(ids))
            {
                JSONArray ssuidlist=new JSONArray();
                for(String id:ids)
                {
                    JSONObject ssuid=new JSONObject();
                    ssuid.set("metersn",id);
                    ssuidlist.add(ssuid);
                }
                param.put("ssuidlist",ssuidlist);

            }
            String body = HttpRequest.post(path).body(JSONUtil.toJsonStr(param))
                    .execute().body();
            return body;
        }
        return null;
    }

    @Override
    public String tcehour(String ssuid, String metersn, String sdate) {
        String path= url + "/basapi/ssu/v1.0.0/tcehour";
        String token = redisHelper.StringGet(0, "basi");
        if(StrUtil.isBlank(token))
        {
            token=getToken();
        }
        if(StrUtil.isNotEmpty(token))
        {
            Map<String,Object> param=new HashMap<>();
            param.put("appid",appid);
            param.put("accesstoken",getEncryptKey(token));
            if(StrUtil.isNotEmpty(ssuid))
            {
                param.put("ssuid",ssuid);
            }
            if(StrUtil.isNotEmpty(metersn))
            {
                param.put("metersn",metersn);
            }
            if(StrUtil.isNotEmpty(sdate))
            {
                param.put("sdate",sdate);
            }
            String body = HttpRequest.post(path).body(JSONUtil.toJsonStr(param))
                    .execute().body();
            return body;
        }
        return null;
    }

    @Override
    public String tceday(String ssuid, String metersn, String year, String month) {
        String path= url + "/basapi/ssu/v1.0.0/tceday";
        String token = redisHelper.StringGet(0, "basi");
        if(StrUtil.isBlank(token))
        {
            token=getToken();
        }
        if(StrUtil.isNotEmpty(token))
        {
            Map<String,Object> param=new HashMap<>();
            param.put("appid",appid);
            param.put("accesstoken",getEncryptKey(token));
            if(StrUtil.isNotEmpty(ssuid))
            {
                param.put("ssuid",ssuid);
            }
            if(StrUtil.isNotEmpty(metersn))
            {
                param.put("metersn",metersn);
            }
            if(StrUtil.isNotEmpty(year))
            {
                param.put("year",year);
            }
            if(StrUtil.isNotEmpty(month))
            {
                param.put("month",month);
            }
            String body = HttpRequest.post(path).body(JSONUtil.toJsonStr(param))
                    .execute().body();
            return body;
        }
        return null;
    }

    @Override
    public String tceyear(String ssuid, String metersn, String year) {
        String path= url + "/basapi/ssu/v1.0.0/tceyear";
        String token = redisHelper.StringGet(0, "basi");
        if(StrUtil.isBlank(token))
        {
            token=getToken();
        }
        if(StrUtil.isNotEmpty(token))
        {
            Map<String,Object> param=new HashMap<>();
            param.put("appid",appid);
            param.put("accesstoken",getEncryptKey(token));
            if(StrUtil.isNotEmpty(ssuid))
            {
                param.put("ssuid",ssuid);
            }
            if(StrUtil.isNotEmpty(metersn))
            {
                param.put("metersn",metersn);
            }
            if(StrUtil.isNotEmpty(year))
            {
                param.put("year",year);
            }
            String body = HttpRequest.post(path).body(JSONUtil.toJsonStr(param))
                    .execute().body();
            return body;
        }
        return null;
    }


    @Override
    public PageResult<List<DeviceSsuVO>> getDevice(String usersn,String username,String path,Integer page,Integer size) {
        Page page1=null;
        if(PageResult.isPage(page,size))
        {
          page1=  new Page<DeviceSsuVO>(page,size);
        }
        List<DeviceSsuVO> list=  mapper.getDevice(page1,usersn,username,path);
        return PageResult.<List<DeviceSsuVO>>builder()
                .data(list)
                .total(page1==null?list.size():page1.getTotal())
                .build();

    }

    private String getToken() {
        String path= url + "/basapi/v1.0.0/accesstoken";

        Map<String,Object> param=new HashMap<>();
        param.put("appid",appid);
        param.put("secret",getEncryptKey(secret));
        String body = HttpRequest.post(path).body(JSONUtil.toJsonStr(param))
                .execute().body();
        log.info("获取token返回结果：{}",body);
        if(StrUtil.isNotEmpty(body))
        {
            JSONObject entries = JSONUtil.parseObj(body);
            if(entries.getInt("errcode")==0)
            {
                String token= entries.getStr("accesstoken");
                redisHelper.StringSet(0,"basi",token,3000, TimeUnit.SECONDS);
                return token;
            }
        }
        return null;
    }

    private String getEncryptKey(String secret) {
        AES aes = new AES(Mode.CBC, Padding.PKCS5Padding, key.getBytes());
        aes.setIv(key.getBytes());
        String hex = aes.encryptHex(secret+ DateUtil.date().toString("yyyyMMddHHmmssSSS"));
        log.info("加密后的字符串：{}",hex);
        return hex;
    }



}
