package com.gh.acs.broadcast.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gh.acs.broadcast.mapper.ITCGroupMapper;
import com.gh.acs.broadcast.model.entity.ITCGroup;
import com.gh.acs.broadcast.model.entity.ITCTerminal;
import com.gh.acs.broadcast.service.ITCGroupService;
import com.gh.common.utils.PageResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;



@Service
public class ITCGroupServiceImpl extends ServiceImpl<ITCGroupMapper, ITCGroup> implements ITCGroupService {
    @Autowired
    private ITCGroupMapper mapper;
    @Override
    public PageResult<List<ITCTerminal>> getTerminalByGroup(Integer groupId, Integer page, Integer size) {
        Page page1=null;
        if(PageResult.isPage(page,size))
        {
            page1=new Page(page,size);
        }
        List<ITCTerminal> terminalByGroup = mapper.getTerminalByGroup(page1, groupId);
        return PageResult.<List<ITCTerminal>>builder()
                .data(terminalByGroup)
                .total(page1==null?terminalByGroup.size():page1.getTotal())
                .build();
    }
}
