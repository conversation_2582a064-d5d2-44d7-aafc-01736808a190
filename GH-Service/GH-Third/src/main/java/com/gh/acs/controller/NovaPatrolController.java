package com.gh.acs.controller;


import com.gh.acs.service.NovaService;
import com.gh.common.utils.GHResponse;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Api("九江实验室巡检")
@RestController
@RequestMapping("/nova")
public class NovaPatrolController {

    @Autowired
    private NovaService service;

    @GetMapping("point")
    public GHResponse getPointList(Integer page, Integer size, String search) {
        return GHResponse.ok(service.getPointList(page, size, search)) ;
    }

    @GetMapping("line")
    public GHResponse getLineList(Integer page, Integer size, String search) {
        return GHResponse.ok(service.getLineList(page, size, search)) ;
    }

    @GetMapping("plan")
    public GHResponse getPlanList(Integer page, Integer size, String search) {
        return GHResponse.ok(service.getPlanList(page, size, search)) ;
    }

    @GetMapping("task")
    public GHResponse getTaskList(Integer page, Integer size, String search) {
        return GHResponse.ok(service.getTaskList(page, size, search));
    }

    @GetMapping("problem")
    public GHResponse getProblemList(Integer page, Integer size, String search) {
        return GHResponse.ok(service.getProblemList(page, size, search));
    }

    @GetMapping("problem/count")
    public GHResponse getProblemCount() {
        return GHResponse.ok(service.getProblemCount());
    }

    @GetMapping("task/count")
    public GHResponse getTaskCount() {
        return GHResponse.ok(service.getTaskCount());
    }

    @GetMapping("plan/count")
    public GHResponse getPlanCount() {
        return GHResponse.ok(service.getPlanCount());
    }

    @GetMapping("task/detail")
    public GHResponse getTaskDetail(String taskId) {
        return GHResponse.ok(service.getTaskDetail(taskId));
    }

    @GetMapping("task/history")
    public GHResponse getTaskHistory(String taskId) {
        return GHResponse.ok(service.getTaskHistory(taskId));
    }

    @GetMapping("task/point")
    public GHResponse getTaskPoint(String taskId) {
        return GHResponse.ok(service.getTaskPoint(taskId));
    }










}
