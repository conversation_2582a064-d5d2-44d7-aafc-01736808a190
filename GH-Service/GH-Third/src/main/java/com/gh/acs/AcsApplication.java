package com.gh.acs;


import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableScheduling;

@SpringBootApplication
@EnableDiscoveryClient
@ComponentScan("com.gh")
@EnableScheduling
public class AcsApplication {
    public static void main(String[] args) {
        SpringApplication.run(AcsApplication.class,args);
    }
}
