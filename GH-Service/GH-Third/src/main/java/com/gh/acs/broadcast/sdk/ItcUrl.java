package com.gh.acs.broadcast.sdk;

public interface ItcUrl {
    String Login="/api/v29+/auth";
    String Terminal="/api/v29+/ws/forwarder";
    String Group="/api/v29+/terminals-groups/all";
    String Terminal_Volume="/api/v29+/ws/forwarder";
    String Task_Volume="/api/v29%2B/ws/forwarder";
    String API="/api/v29+/ws/forwarder";
    String Terminal_Log="/api/v29+/logs/terminals?withTerminal=true";
    String Task_Log="/api/v29+/logs/terminals?withUser=true";
    String Upload="/api/v29+/medias/upload/";
    String addMusicCategory="/api/v29+/medias-groups";
}
