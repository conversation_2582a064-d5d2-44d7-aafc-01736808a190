package com.gh.acs.broadcast.model.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("GHITC_Task")
@Builder
public class ITCTask {
    @TableId(type = IdType.AUTO)
    private Integer id;
    private String TaskID;
    private String TaskName;
    private String TaskShowInfo;
    private String TaskStatus;
    private Integer TaskVolume;
    private String TextContent;
    private Integer TTSSpeed;
    private Integer RepeatTime;
    private String tids;
    private String mids;
    private Integer priority;
    @TableField(exist = false)
    private Integer TaskType;

    private String status;

}
