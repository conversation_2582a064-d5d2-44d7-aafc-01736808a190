package com.gh.acs.controller;


import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.gh.acs.model.entity.EventList;
import com.gh.acs.model.entity.HisData;
import com.gh.acs.model.entity.devPropertyName;
import com.gh.acs.service.EventService;
import com.gh.acs.service.HisDataService;
import com.gh.acs.service.JiFangService;
import com.gh.common.utils.GHResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RequestMapping("/jifang")
@RestController
@Api(tags = "园林五号机房管理")
public class JiFangController {

    @Autowired
    private JiFangService service;
    @Autowired
    private EventService eventService;

    @Autowired
    private HisDataService hisDataService;

    @GetMapping
    @ApiOperation("机房数据")
    @DS("park1")
    public GHResponse plate(String keyword,String code)
    {
        List<devPropertyName> list = service.lambdaQuery().eq(StrUtil.isNotEmpty(keyword), devPropertyName::getDevicename, keyword)
                .eq(StrUtil.isNotEmpty(code), devPropertyName::getDeviceid, code)
                .list();

        return GHResponse.ok(list);
    }


    @GetMapping("event")
    @ApiOperation("报警数据")
    @DS("park1")
    public GHResponse event()
    {
        List<EventList> list = eventService.lambdaQuery()
                .isNotNull(EventList::getUpdatedate)
                .eq(EventList::getValuedescript, " 报警")
                .list();
        return GHResponse.ok(list);
    }

    @GetMapping("hisdata")
    @ApiOperation("历史数据")
    @DS("park1")
    public GHResponse hisdata()
    {
        List<HisData> list = hisDataService.lambdaQuery()
                .orderByDesc(HisData::getHisdate)
                .eq(HisData::getHisdate, DateUtil.date().toString("yyyy-MM-dd HH:00:00"))
                .list();
        return GHResponse.ok(list);
    }
}
