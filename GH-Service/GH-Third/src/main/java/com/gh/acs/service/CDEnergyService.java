package com.gh.acs.service;


import com.gh.acs.model.dto.EnergyHisDataDTO;
import com.gh.acs.model.dto.EnergyRealDataDTO;
import com.gh.common.utils.PageResult;

import java.util.Date;
import java.util.List;

public interface CDEnergyService {



    PageResult<List<EnergyRealDataDTO>> getEnergyRealData(Integer page, Integer size, String keyword);

    PageResult<List<EnergyHisDataDTO>> getEnergyHistory(Integer page, Integer size, String keyword, Date bt, Date et);


}
