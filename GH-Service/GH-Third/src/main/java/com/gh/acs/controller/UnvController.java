package com.gh.acs.controller;


import cn.hutool.json.JSONObject;
import com.gh.acs.service.UnvService;
import com.gh.common.utils.GHResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;

@RequestMapping("/unv")
@RestController
@Api(tags = "宇视接口")
public class UnvController {

    @Autowired
    private UnvService service;




    @PostMapping("cross")
    @ApiOperation("停车场-查询过车记录")
    public GHResponse cross(@RequestBody JSONObject param) throws IOException {
        String carIn = service.cross(param,"/LAPI/V1.0/ParkingLots/Vehicles/PassRecords?randomKey=1698133977436");
        return GHResponse.ok(carIn);
    }

    @PostMapping("parkin")
    @ApiOperation("停车场-查询过车记录")
    public GHResponse parkin(@RequestBody JSONObject param) throws IOException {
        String carIn = service.cross(param,"/LAPI/V1.0/ParkingLots/Vehicles/InParkingLot?randomKey=1698136820892");
        return GHResponse.ok(carIn);
    }


    @GetMapping("spaceinfo")
    @ApiOperation("车位系统-车位信息")
    public GHResponse spaceinfo(Integer startIndex,Integer pageSize,String no,String plateNo,Integer status,Integer regionalId)
    {
        String body = service.spaceinfo(startIndex, pageSize, no, plateNo, status,regionalId);
        return GHResponse.ok(body);
    }

    @GetMapping("record")
    @ApiOperation("车位系统-停车记录")
    public GHResponse record(String arriveTime,String leaveTime,String plateNo,String spaceNo,Integer startIndex,Integer pageSize)
    {
        String body = service.record(arriveTime, leaveTime, plateNo, spaceNo, startIndex, pageSize);
        return GHResponse.ok(body);
    }

    @GetMapping("area")
    @ApiOperation("车位系统-区域信息")
    public GHResponse area()
    {
        String body = service.allArea();
        return GHResponse.ok(body);
    }

}
