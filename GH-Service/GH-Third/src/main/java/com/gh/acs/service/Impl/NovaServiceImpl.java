package com.gh.acs.service.Impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.gh.acs.service.NovaService;
import com.gh.common.redis.RedisHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * Description:
 * User: zhangkeguang
 * Date: 2024-09-02-13:51
 */

@Service
public class NovaServiceImpl implements NovaService {

    @Value("${nova.url}")
    private String url;
    @Value("${nova.client_id}")
    private String client_id;
    @Value("${nova.client_secret}")
    private String client_secret;

    @Autowired
    private RedisHelper redisHelper;

    @Override
    public JSONObject getPointList(Integer page, Integer size, String search) {
        Map<String, Object> query = new HashMap<>();
        query.put("page", page);
        query.put("pageSize", size);
        if (StrUtil.isNotEmpty(search)) {
            query.put("search", search);
        }
        query.put("showDisabled", false);
        String path = url + "/logistics/v1/420/patrol-points/page";
        String token = getToken();

        String body = HttpRequest.post(path)
                .header("Authorization", "Bearer " + token)
                .body(JSONUtil.toJsonStr(query))
                .execute().body();
        return JSONUtil.parseObj(body,true);
    }

    @Override
    public JSONObject getLineList(Integer page, Integer size, String search) {
        Map<String, Object> query = new HashMap<>();
        query.put("page", page);
        query.put("pageSize", size);
        if (StrUtil.isNotEmpty(search)) {
            query.put("search", search);
        }
        query.put("showDisabled", false);
        String path = url + "/logistics/v1/420/patrol-lines/page";
        String token = getToken();
        String body = HttpRequest.post(path)
                .header("Authorization", "Bearer " + token)
                .body(JSONUtil.toJsonStr(query))
                .execute().body();
        return JSONUtil.parseObj(body,true);
    }

    @Override
    public JSONObject getPlanList(Integer page, Integer size, String search) {
        Map<String, Object> query = new HashMap<>();
        query.put("page", page-1);
        query.put("size", size);
        if (StrUtil.isNotEmpty(search)) {
            query.put("name",search);
        }
        query.put("logisticsModule", "patrol");
        String path = url + "/logistics/v1/420/plans/patrol?" + URLUtil.buildQuery(query, StandardCharsets.UTF_8);
        String token = getToken();
        String body = HttpRequest.get(path)
                .header("Authorization", "Bearer " + token)
                .execute().body();
        return JSONUtil.parseObj(body,true);
    }

    @Override
    public JSONObject getTaskList(Integer page, Integer size, String search) {

        Map<String, Object> query = new HashMap<>();
        query.put("page", page-1);
        query.put("size", size);
        if (StrUtil.isNotEmpty(search)) {
            query.put("quickQuery", search);
        }
        query.put("logisticsModule", "patrol");
        String path = url + "/logistics/v1/420/tasks/patrol?" + URLUtil.buildQuery(query, StandardCharsets.UTF_8);
        String token = getToken();
        String body = HttpRequest.get(path)
                .header("Authorization", "Bearer " + token)
                .execute().body();
        return JSONUtil.parseObj(body,true);
    }

    @Override
    public JSONObject getTaskDetail(String taskId) {
        String path = url + "/logistics/v1/420/tasks/patrol/detail/"+taskId+"?isApp=false";
        String token = getToken();
        String body = HttpRequest.get(path)
                .header("Authorization", "Bearer " + token)
                .execute().body();
        return JSONUtil.parseObj(body,true);
    }

    @Override
    public JSONArray getTaskHistory(String taskId) {
        String path = url + "/logistics/v1/420/tasks/patrol/detail/history/"+taskId;
        String token = getToken();
        String body = HttpRequest.get(path)
                .header("Authorization", "Bearer " + token)
                .execute().body();
        return JSONUtil.parseArray(body,true);
    }

    @Override
    public JSONArray getTaskPoint(String taskId) {
        String path = url + "/logistics/v1/420/tasks/patrol/detail/tab/"+taskId;
        String token = getToken();
        String body = HttpRequest.get(path)
                .header("Authorization", "Bearer " + token)
                .execute().body();
        return JSONUtil.parseArray(body,true);
    }

    @Override
    public JSONObject getProblemList(Integer page, Integer size, String search) {
        Map<String, Object> query = new HashMap<>();
        query.put("page", page-1);
        query.put("size", size);
        if (StrUtil.isNotEmpty(search)) {
            query.put("patrolQuickQuery", search);
        }
        query.put("logisticsModule", "patrol");
        String path = url + "/logistics/v1/4/task-problems/patrol?" + URLUtil.buildQuery(query, StandardCharsets.UTF_8);
        String token = getToken();
        String body = HttpRequest.get(path)
                .header("Authorization", "Bearer " + token)
                .execute().body();
        return JSONUtil.parseObj(body,true);
    }

    @Override
    public JSONArray getProblemCount() {
        String path = url + "/logistics/v1/420/task-problems/patrol/count?logisticsModule=patrol";
        String token = getToken();
        String body = HttpRequest.get(path)
                .header("Authorization", "Bearer " + token)
                .execute().body();
        return JSONUtil.parseArray(body,true);
    }

    @Override
    public JSONArray getTaskCount() {
        String path = url + "/logistics/v1/420/tasks/patrol/count?logisticsModule=patrol&lineId=";
        String token = getToken();
        String body = HttpRequest.get(path)
                .header("Authorization", "Bearer " + token)
                .execute().body();
        return JSONUtil.parseArray(body,true);
    }


    @Override
    public JSONArray getPlanCount() {
        String path = url + "/logistics/v1/420/plans/patrol/count?logisticsModule=patrol";
        String token = getToken();
        String body = HttpRequest.get(path)
                .header("Authorization", "Bearer " + token)
                .execute().body();
        return JSONUtil.parseArray(body,true);
    }

    private String login() {
        String path = url + "/oauth/oauth/token?grant_type=client_credentials&client_id=" + client_id + "&client_secret=" + client_secret;

        String body = HttpRequest.post(path)
                .execute()
                .body();
        if (StrUtil.isNotEmpty(body)) {
            JSONObject obj = JSONUtil.parseObj(body);
            redisHelper.StringSet(0, "nova_token", obj.getStr("access_token"), 60, TimeUnit.MINUTES);
            return obj.getStr("access_token");

        }
        return null;
    }

    private String getToken() {
        String token = redisHelper.StringGet(0, "nova_token");
        if (StrUtil.isEmpty(token)) {
            return login();
        }
        return token;
    }


}
