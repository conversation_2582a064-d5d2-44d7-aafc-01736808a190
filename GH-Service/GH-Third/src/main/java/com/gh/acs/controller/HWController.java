package com.gh.acs.controller;


import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.gh.acs.service.HWService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;

@Api("华为机房")
@RestController
@RequestMapping("/hw")
@Slf4j
public class HWController {

    @Autowired
    private HWService service;

    @GetMapping("group")
    @ApiOperation("获取分组id")
    public Object getDeviceDoor(String serverCode) throws Exception {
        String data = service.getGroup(serverCode);
        if(StrUtil.isNotEmpty(data))
        {
           return   JSONUtil.parseArray(data);
        }
        return Arrays.asList();
    }

    @GetMapping("data")
    @ApiOperation("获取设备实时数据")
    public Object getDoor(String serverCode,Integer pageId) throws Exception {
        String data = service.getRealData(serverCode,pageId);
        if(StrUtil.isNotEmpty(data))
        {
            return JSONUtil.parseArray(data);
        }
        return Arrays.asList();

    }







}
