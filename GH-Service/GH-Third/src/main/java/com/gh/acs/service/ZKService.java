package com.gh.acs.service;

import cn.hutool.json.JSONObject;

public interface ZKService {
   JSONObject getDoor(String name,Integer page,Integer size);

   JSONObject getDoorDevice(String name,Integer page,Integer size);

   JSONObject getDoorRecord(String startTime,String endTime,Integer page,Integer size,String keyword);

   JSONObject getChannel();

   JSONObject getParkRecord(String timeBegin,String timeEnd,String carNumber,String username,Integer page,Integer size);

   Boolean openChannel(String id);

   JSONObject getVisRecord(String startEnterTime,String endEnterTime,String visEmpPin,Integer page,Integer size,String visitedLikeName);

   JSONObject getMonthRecord(String pin,String monthStart,String monthEnd,String deptName,Integer page,Integer size,String likeName);

   //考勤原始数据
   JSONObject atttransaction(String beginDate,String endDate,String likeName,String areaName,String deptName,Integer page,Integer size);


   //在场车辆
   JSONObject parkrecordin(String timeBegin,String timeEnd,String carNumber,String userName,Integer page,Integer size);



   //出停车记录
   JSONObject parkrecordout(String timeBegin,String timeEnd,String carNumber,String userName,Integer page,Integer size);



   //请假详情
   JSONObject leave(String personPin,String likeName,String deptName,String startApplyDateTime,String endApplyDateTime,Integer page,Integer size);




   JSONObject getParkStatistics();
   JSONObject getAccEvent();
   JSONObject getVisReservationData();



}
