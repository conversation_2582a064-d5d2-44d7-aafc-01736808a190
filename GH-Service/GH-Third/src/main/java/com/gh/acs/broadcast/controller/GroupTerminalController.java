//package com.gh.acs.broadcast.controller;
//
//import cn.hutool.core.collection.CollUtil;
//import cn.hutool.core.util.ArrayUtil;
//import com.baomidou.mybatisplus.core.toolkit.Wrappers;
//import com.gh.acs.broadcast.model.dto.GroupTerminalAdd;
//import com.gh.acs.broadcast.model.entity.GroupTerminal;
//import com.gh.acs.broadcast.service.GroupTerminalService;
//import com.gh.common.exception.SystemEnumMsg;
//import com.gh.common.utils.GHResponse;
//import io.swagger.annotations.Api;
//import io.swagger.annotations.ApiOperation;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.web.bind.annotation.*;
//
//import java.util.Arrays;
//
//@RequestMapping("/itc/group-terminal")
//@RestController
//@Api(tags = "分组--终端")
//public class GroupTerminalController {
//    @Autowired
//    private GroupTerminalService service;
//
//
//    @PostMapping
//    @ApiOperation("分组中添加终端")
//    public GHResponse Add(@RequestBody GroupTerminalAdd group)
//    {
//        if(CollUtil.isNotEmpty(group.getTid()))
//        {
//            group.getTid().forEach(id->{
//                GroupTerminal groupTerminal = GroupTerminal.builder()
//                        .groupId(group.getGroupId())
//                        .tid(id).build();
//                service.saveOrUpdate(groupTerminal,
//                        Wrappers.<GroupTerminal>lambdaUpdate()
//                .eq(GroupTerminal::getGroupId,group.getGroupId())
//                .eq(GroupTerminal::getTid,id));
//            });
//        }
//        return GHResponse.ok(null, SystemEnumMsg.CREATE_SUCCESS.msg());
//    }
//
//    @DeleteMapping
//    @ApiOperation("从分组中删除终端")
//    public GHResponse Delete(@RequestParam Integer[] ids)
//    {
//
//        if(ArrayUtil.isNotEmpty(ids))
//        {
//            boolean delete = service.removeByIds(Arrays.asList(ids));
//            if(delete)
//            {
//                return GHResponse.ok();
//            }
//        }
//
//        return GHResponse.failed(SystemEnumMsg.Delete_ERROR.msg());
//    }
//
//
//
//
//
//
//
//
//}
