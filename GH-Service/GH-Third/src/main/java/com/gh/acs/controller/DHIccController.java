package com.gh.acs.controller;


import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.gh.acs.model.dto.NodeInfo;
import com.gh.acs.service.DHIccService;
import com.gh.common.utils.GHResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;

@Api("大华icc平台管理")
@RestController
@RequestMapping("/icc")
public class DHIccController {

    @Autowired
    private DHIccService service;


    //门禁设备
    @GetMapping("control")
    public GHResponse<String> getDoor(@RequestParam(required = false) String[] types,
                                      @RequestParam(required = false) Integer category,
                                      @RequestParam(required = false) Integer onLine,
                                      @RequestParam(required = false) Integer page,
                                      @RequestParam(required = false) Integer size,
                                      @RequestParam(required = false) String[] deviceIps) throws Exception {
        if(category == null){
            category = 8;
        }
        String doorDevice = service.getDoorControl(page,size, category,null,Arrays.asList(deviceIps),onLine);
        return GHResponse.ok(doorDevice);
    }

    @GetMapping("door")
    public GHResponse<String> getDoorStatus(Integer page,Integer size)  {
        String doorDevice = service.getDoor(page,size);
        return GHResponse.ok(doorDevice);
    }

    @GetMapping("status")
    public GHResponse getDoor(@RequestParam(required = false) String[] channelCodes)  {
        JSONArray data = service.getDoorStatus(Arrays.asList(channelCodes));
        return GHResponse.ok(data);
    }

    @GetMapping("cmd")
    public GHResponse cmd(@RequestParam(required = false) String[] channelCodeList, @RequestParam(required = false) String cmd)  {
        boolean success = service.doorControl(Arrays.asList(channelCodeList), cmd);
        return GHResponse.ok(success);
    }



    @GetMapping("doorRecord")
    public GHResponse<Object> getRecord(String keyword,String bt,String et ,Integer page, Integer size)
    {
        JSONArray doorLog = service.getDoorLog(bt, et, page, size, keyword);

        return GHResponse.ok(doorLog);
    }

    @GetMapping("doorRecordCount")
    public GHResponse<Object> getRecordCount(String keyword,String bt,String et ,Integer page, Integer size)
    {
        Integer count = service.getDoorLogCount(bt, et, page, size, keyword);
        return GHResponse.ok(count);
    }
    //人员信息
    @GetMapping("parkPerson")
    public GHResponse<Object> parkPerson(Integer page, Integer size, String ownerLikeStr)
    {
        JSONArray parkPerson = service.parkPerson(page, size, ownerLikeStr);
        return GHResponse.ok(parkPerson);
    }

    @GetMapping("parkPersonCount")
    public GHResponse<Object> parkPersonCount(Integer page, Integer size, String ownerLikeStr)
    {
        Integer count = service.parkPersonCount(page, size, ownerLikeStr);
        return GHResponse.ok(count);
    }
    //车辆信息
    @GetMapping("parkCar")
    public GHResponse<Object> parkCar(Integer page, Integer size, String ownerNameLikeStr, String carNumLikeStr)
    {
        JSONArray parkCar = service.parCar(page, size, ownerNameLikeStr, carNumLikeStr);
        return GHResponse.ok(parkCar);
    }

    @GetMapping("parkCarCount")
    public GHResponse<Object> parkCarCount(Integer page, Integer size, String ownerNameLikeStr, String carNumLikeStr)
    {
        Integer count = service.parkCarCount(page, size, ownerNameLikeStr, carNumLikeStr);
        return GHResponse.ok(count);
    }
    //车位剩余信息和停车场信息
    @GetMapping("parklot")
    public GHResponse<Object> parklot()
    {
        String parklot = service.parklot();
        return GHResponse.ok(parklot);
    }
    //车位实时在停信息
    @GetMapping("carport")
    public GHResponse<Object> carport(Integer size, Integer page, String ownerNameLikeStr, String carNumLikeStr,String carportCode)
    {
        Object carport = service.carport(size, page, ownerNameLikeStr, carNumLikeStr,carportCode);
        return GHResponse.ok(carport);
    }
    //手动闸机记录
    @GetMapping("handSluice")
    public GHResponse<Object> handSluice(Integer size, Integer page, String ownerName, String capDateStartStr, String capDateEndStr)
    {
        Object handSluice = service.handSluice(size, page, ownerName, capDateStartStr, capDateEndStr);
        return GHResponse.ok(handSluice);
    }

    //车位告警
    @GetMapping("carportAlarm")
    public GHResponse<Object> carportAlarm(Integer size, Integer page, String carNum, String carportCode, String alarmStart, String alarmEnd)
    {
        Object carportAlarm = service.carportAlarm(size, page, carNum, carportCode, alarmStart, alarmEnd);
        return GHResponse.ok(carportAlarm);
    }

    //场内车辆
    @GetMapping("parkInCar")
    public GHResponse<Object> parkInCar(Integer size, Integer page, String ownerLikeStr, String carNum)
    {
        Object parkInCar = service.parkInCar(size, page, ownerLikeStr, carNum);
        return GHResponse.ok(parkInCar);
    }

    //车辆出入记录
    @GetMapping("crossCarLog")
    public GHResponse<Object> crossCarLog(Integer size, Integer page, String ownerName, String carNumLikeStr, String queryTimeBegin, String queryTimeEnd)
    {
        JSONArray crossCarLog = service.crossCarLog(size, page, ownerName, carNumLikeStr, queryTimeBegin, queryTimeEnd);
        return GHResponse.ok(crossCarLog);
    }

    @GetMapping("crossCarLogCount")
    public GHResponse<Object> crossCarLogCount(String ownerName, String carNumLikeStr, String queryTimeBegin, String queryTimeEnd)
    {
        Integer count = service.crossCarLogCount(ownerName, carNumLikeStr, queryTimeBegin, queryTimeEnd);
        return GHResponse.ok(count);
    }

    //车流统计
    @ApiOperation("车流统计")
    @GetMapping("carInOutSurvey")
    public GHResponse<Object> carInOutSurvey(Integer type)
    {
        Object carInOutSurvey = service.carInOutSurvey(type);
        return GHResponse.ok(carInOutSurvey);
    }






    @GetMapping("token")
    public GHResponse<Object> test(String password,String publicKey) throws Exception {

        String token = service.getToken();

        return GHResponse.ok(token);
    }


    @GetMapping("alarmhosts")
    public GHResponse<Object> alarmhosts(String nodeCode)  {

        Object alarmhosts = service.alarmhosts(nodeCode);

        return GHResponse.ok(alarmhosts);
    }

    @GetMapping("subSyatem")
    public GHResponse<Object> subSyatem(String deviceCode) throws Exception {

        Object subSyatem = service.subSyatem(deviceCode);

        return GHResponse.ok(subSyatem);
    }

    @PostMapping("alarmControl")
    public GHResponse<Object> alarmControl(@RequestBody  JSONObject data)  {

        JSONArray infos = data.getJSONArray("nodeInfos");
        boolean alarmControl = service.alarmControl(infos.toList(NodeInfo.class), data.getInt("operate") );

        return GHResponse.ok(alarmControl);
    }

    @GetMapping("alarmEvent")
    public GHResponse<Object> alarmEvent(String alarmStartDateString,String alarmEndDateString,Integer alarmType,Integer pageNum,Integer pageSize)  {

        JSONArray alarmEvent = service.alarmEvent(alarmStartDateString, alarmEndDateString, alarmType, pageNum, pageSize);

        return GHResponse.ok(alarmEvent);
    }

    @GetMapping("alarmEventCount")
    public GHResponse<Object> alarmEventCount(String alarmStartDateString,String alarmEndDateString,Integer alarmType)  {

        Integer alarmEventCount = service.alarmEventCount(alarmStartDateString, alarmEndDateString, alarmType);

        return GHResponse.ok(alarmEventCount);
    }

    @GetMapping("parkDeviceList")
    public GHResponse<Object> parkDeviceList()  {

        Object parkDeviceList = service.parkDeviceList();

        return GHResponse.ok(parkDeviceList);
    }

    @GetMapping("channel")
    public GHResponse<Object> channel(@RequestParam(required = false) Integer category,
                                      @RequestParam(required = false) Integer onLine,
                                      @RequestParam(required = false) Integer page,
                                      @RequestParam(required = false) Integer size) throws Exception {

        String channel = service.getChannel(page,size, category,onLine);

        return GHResponse.ok(channel);
    }








}
