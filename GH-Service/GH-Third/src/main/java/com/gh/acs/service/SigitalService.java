package com.gh.acs.service;

import cn.hutool.json.JSONArray;
import com.gh.acs.model.dto.*;

import java.util.List;

public interface SigitalService {
    List<Terminal> getTerminal();

    boolean cmd(List<String>ids,String cmd);

    boolean setVolume(List<String> ids,Integer volume);

    boolean playReserve(List<TerminalProgram> terminalPrograms);

    boolean setSwitchTime(List<String> tids, List<PlayTime> playTimes);

    Shot screenShot(String id);

    boolean sendText(SendText sendText);

    JSONArray getProgramList();

}
