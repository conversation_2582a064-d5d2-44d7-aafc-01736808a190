package com.gh.acs.controller;

import cn.hutool.json.JSONObject;
import com.gh.acs.service.YLAEnergy;
import com.gh.common.utils.GHResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;

@Api("蜀山为名得易安能耗")
@RestController
@RequestMapping("/dya")
@Slf4j
public class DYAController {

    @Autowired
    private YLAEnergy service;


    @GetMapping("report")
    @ApiOperation("总体报表")
    public GHResponse<Object> report(String flag, String dateType, String date, @RequestParam Integer[] objIds)  {
        JSONObject item = service.report(flag, dateType, date, Arrays.asList(objIds));
        return GHResponse.ok(item);
    }

    @GetMapping("meter")
    @ApiOperation("查找电表")
    public GHResponse<Object> meter(Integer buildingId)  {
        JSONObject item = service.meter(buildingId);
        return GHResponse.ok(item);
    }

    @GetMapping("device")
    @ApiOperation("设备报表")
    public GHResponse<Object> device(String meterCode, String dateType, String date)  {
        JSONObject item = service.device(meterCode, dateType, date);
        return GHResponse.ok(item);
    }



    @GetMapping("subItem")
    @ApiOperation("分项能耗")
    public GHResponse<Object> subItem(Integer buildingId,String date,String dateType)  {
        JSONObject item = service.subItem(buildingId, date, dateType);
        return GHResponse.ok(item);
    }


    @GetMapping("area")
    @ApiOperation("区域能耗")
    public GHResponse<Object> area(Integer buildingId,String date,String dateType)  {
        JSONObject item = service.area(buildingId, date, dateType);
        return GHResponse.ok(item);
    }

    @GetMapping("buildingList")
    @ApiOperation("区域")
    public GHResponse<Object> buildingList()  {
        JSONObject item = service.buildingList();
        return GHResponse.ok(item);
    }

    @GetMapping("intime")
    @ApiOperation("实时数据")
    public GHResponse<Object> intime(Integer buildingId,Integer page,Integer limit)  {
        JSONObject item = service.intime(buildingId, page, limit);
        return GHResponse.ok(item);
    }


}
