package com.gh.acs.controller;


import cn.hutool.json.JSONObject;
import com.gh.acs.service.PecService;
import com.gh.common.utils.GHResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Api("淮北动环")
@RestController
@RequestMapping("/pec")
public class PecController {

    @Autowired
    private PecService pecService;


    @GetMapping("data")
    @ApiOperation("获取动环数据")
    public GHResponse getData(String id)  {
        JSONObject data = pecService.getData(id);
        return GHResponse.ok(data);
    }

    @GetMapping("device")
    @ApiOperation("获取设备")
    public GHResponse getDevice()  {
        JSONObject data = pecService.getDevice();
        return GHResponse.ok(data);
    }









}
