package com.gh.acs.service.Impl;


import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.gh.acs.service.ZKService;
import com.gh.common.redis.RedisHelper;
import com.gh.common.utils.PageResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;


@Service
@Slf4j
public class ZKServiceImpl implements ZKService {
    @Value("${zk.url}")
    private String url;

//    @Value("${zk.session}")
//    private String session;

    @Value("${zk.username}")
    private String username;

    @Value("${zk.password}")
    private String password;

    @Autowired
    private RedisHelper redisHelper;


    private JSONObject request(String path, Map<String,Object>params) {
        String session= getSession();
        log.info("---------session----"+session);
        if(StrUtil.isNotEmpty(session))
        {
            log.info("---------url----"+url +path);
            log.info("---------params----"+params);
            log.info("---------params----"+JSONUtil.toJsonStr(params));
            String body = HttpRequest.post(url +path )
                    .header("Cookie", "menuType=icon-only; SESSION=" + session)
                    .contentType("application/x-www-form-urlencoded")
                    .body(URLUtil.buildQuery(params, CharsetUtil.CHARSET_UTF_8))
                    .execute()
                    .body();
            log.info("---------body----"+body);
            if(StrUtil.isNotEmpty(body))
            {
                return JSONUtil.parseObj(body);
            }
        }
        return new JSONObject();

    }

    //门禁
    @Override
    public JSONObject getDoor(String name,Integer page,Integer size) {

        Map<String,Object> param=new HashMap<>();
        if(StrUtil.isNotEmpty(name))
        {
            param.put("name",name);
        }
        if(PageResult.isPage(page,size))
        {
            param.put("pageSize",size);
            param.put("posStart",(page-1)*size);
        }
        return  request("/accDoor.do?list", param);
    }


    //道闸
    @Override
    public JSONObject getDoorDevice(String name,Integer page,Integer size) {
        Map<String,Object> param=new HashMap<>();
        if(StrUtil.isNotEmpty(name))
        {
            param.put("alias",name);
        }
        if(PageResult.isPage(page,size))
        {
            param.put("pageSize",size);
            param.put("posStart",(page-1)*size);
        }

        return  request("/accDevice.do?list", param);
    }


    //进出记录
    @Override
    public JSONObject getDoorRecord(String startTime, String endTime,Integer page,Integer size,String keyword) {
        Map<String,Object> param=new HashMap<>();
        if(StrUtil.isNotEmpty(startTime))
        {
            param.put("startTime",startTime);
        }
        if(StrUtil.isNotEmpty(endTime))
        {
            param.put("endTime",endTime);
        }
        if(PageResult.isPage(page,size))
        {
            param.put("pageSize",size);
            param.put("posStart",(page-1)*size);
        }
        param.put("likeName",keyword);

        return  request("/accFirstInLastOut.do?list", param);
    }

    @Override
    public JSONObject getChannel() {
        Map<String,Object> param=new HashMap<>();
        return  request("/parkChannel.do?list", param);
    }

    @Override
    public JSONObject getParkRecord(String timeBegin, String timeEnd, String carNumber,String username, Integer page, Integer size) {
        Map<String,Object> param=new HashMap<>();
        if(StrUtil.isNotEmpty(carNumber))
        {
            param.put("carNumber",carNumber);
        }
        if(StrUtil.isNotEmpty(username))
        {
            param.put("userName",username);
        }
        param.put("timeBegin",timeBegin);
        param.put("timeEnd",timeEnd);
        if(PageResult.isPage(page,size))
        {
            param.put("pageSize",size);
            param.put("posStart",(page-1)*size);
        }
        return  request("/parkRecordinSide.do?list", param);
    }

    @Override
    public Boolean openChannel(String id) {
        Map<String,Object> param=new HashMap<>();
        param.put("parkChannelId",id);
        JSONObject data = request("/parkRecordout.do?openGateByChannelId", param);
        Object ret = data.get("ret");
        if(ret!=null&&ret.toString().equalsIgnoreCase("ok"))
        {
            return true;
        }
        return false;
    }


    /**
     *
     * @param startEnterTime
     * @param endEnterTime
     * @param username 访问人员姓名
     * @param page
     * @param size
     * @param visitedLikeName 被访人员姓名
     * @return
     */
    @Override
    public JSONObject getVisRecord(String startEnterTime, String endEnterTime, String username, Integer page, Integer size,String visitedLikeName) {
        Map<String,Object> param=new HashMap<>();
        if(StrUtil.isNotEmpty(username))
        {
            param.put("likeName",username);
        }
        if(StrUtil.isNotEmpty(visitedLikeName))
        {
            param.put("visitedLikeName",visitedLikeName);
        }
        param.put("startEnterTime",startEnterTime);
        param.put("endEnterTime",endEnterTime);
        if(PageResult.isPage(page,size))
        {
            param.put("pageSize",size);
            param.put("posStart",(page-1)*size);
        }
        return  request("/visTransaction.do?list", param);
    }

    @Override
    public JSONObject getMonthRecord(String pin, String monthStart, String monthEnd, String deptName, Integer page, Integer size,String likeName) {
        Map<String,Object> param=new HashMap<>();
        if(StrUtil.isNotEmpty(pin))
        {
            param.put("pin",pin);
        }
        if(StrUtil.isNotEmpty(deptName))
        {
            param.put("deptName",deptName);
        }
        if(StrUtil.isNotEmpty(likeName))
        {
            param.put("likeName",likeName);
        }
        param.put("monthStart",monthStart);
        param.put("monthEnd",monthEnd);
        if(PageResult.isPage(page,size))
        {
            param.put("pageSize",size);
            param.put("posStart",(page-1)*size);
        }
        return  request("/attMonthDetailReport.do?list", param);
    }

    /**
     * 考勤原始数据
     * @param beginDate
     * @param endDate
     * @param likeName
     * @param areaName
     * @param deptName
     * @return
     */
    @Override
    public JSONObject atttransaction(String beginDate, String endDate, String likeName, String areaName, String deptName,Integer page,Integer size) {
        Map<String,Object> param=new HashMap<>();
        param.put("beginDate",beginDate);
        param.put("endDate",endDate);
        if(StrUtil.isNotEmpty(likeName))
        {
            param.put("likeName",likeName);
        }
        if(StrUtil.isNotEmpty(areaName))
        {
            param.put("areaName",areaName);
        }
        if(StrUtil.isNotEmpty(deptName))
        {
            param.put("deptName",deptName);
        }
        if(PageResult.isPage(page,size))
        {
            param.put("pageSize",size);
            param.put("posStart",(page-1)*size);
        }
        return  request("/attTransaction.do?list", param);
    }

    @Override
    public JSONObject parkrecordin(String timeBegin, String timeEnd, String carNumber, String userName,Integer page,Integer size) {
        Map<String,Object> param=new HashMap<>();
        param.put("timeBegin",timeBegin);
        param.put("timeEnd",timeEnd);
        if(StrUtil.isNotEmpty(carNumber))
        {
            param.put("carNumber",carNumber);
        }
        if(StrUtil.isNotEmpty(userName))
        {
            param.put("userName",userName);
        }
        if(PageResult.isPage(page,size))
        {
            param.put("pageSize",size);
            param.put("posStart",(page-1)*size);
        }
        return  request("/parkRecordin.do?list", param);
    }


    /**
     * 出场记录
     * @param timeBegin
     * @param timeEnd
     * @param carNumber
     * @param userName
     * @return
     */
    @Override
    public JSONObject parkrecordout(String timeBegin, String timeEnd, String carNumber, String userName,Integer page,Integer size) {
        Map<String,Object> param=new HashMap<>();
        param.put("timeBegin",timeBegin);
        param.put("timeEnd",timeEnd);
        if(StrUtil.isNotEmpty(carNumber))
        {
            param.put("carNumber",carNumber);
        }
        if(StrUtil.isNotEmpty(userName))
        {
            param.put("userName",userName);
        }
        if(PageResult.isPage(page,size))
        {
            param.put("pageSize",size);
            param.put("posStart",(page-1)*size);
        }
        return  request("/parkRecordout.do?list", param);
    }

    @Override
    public JSONObject leave(String personPin, String likeName, String deptName, String startApplyDateTime, String endApplyDateTime, Integer page, Integer size) {
        Map<String,Object> param=new HashMap<>();
        param.put("startApplyDateTime",startApplyDateTime);
        param.put("endApplyDateTime",endApplyDateTime);
        if(StrUtil.isNotEmpty(likeName))
        {
            param.put("likeName",likeName);
        }
        if(StrUtil.isNotEmpty(deptName))
        {
            param.put("deptName",deptName);
        }
        if(StrUtil.isNotEmpty(personPin))
        {
            param.put("personPin",personPin);
        }
        if(PageResult.isPage(page,size))
        {
            param.put("pageSize",size);
            param.put("posStart",(page-1)*size);
        }
        return  request("/attLeaveReport.do?list", param);
    }



    private String login() {
        Map<String,Object> param=new HashMap<>();
        param.put("username",username);
        param.put("password",password);
        param.put("loginType","NORMAL");
        String session = HttpRequest.post(url + "/login.do")
                .body(URLUtil.buildQuery(param, CharsetUtil.CHARSET_UTF_8))
                .execute()
                .getCookie("SESSION")
                .getValue();
        log.info("---------"+session);
        if(StrUtil.isNotEmpty(session))
        {
//            redisHelper.StringSet(0,"zk_session",session,15, TimeUnit.MINUTES);
            return session;
        }
        return "";

    }

    private String getSession()
    {
//        String session = redisHelper.StringGet(0, "zk_session");
//        if(StrUtil.isEmpty(session))
//        {
            //登录两次
            login();
          String  session=login();
//        }
        return session;
    }
    @Override
    public JSONObject getParkStatistics() {
        Map<String,Object> param=new HashMap<>();
        return  request("/dashboard.do?getGardenParkSpaceUseRateData", param);
    }

    @Override
    public JSONObject getAccEvent() {
        Map<String,Object> param=new HashMap<>();
        return  request("/dashboard.do?getAccEventTrendsData&timeType=day", param);
    }

    @Override
    public JSONObject getVisReservationData() {
        Map<String,Object> param=new HashMap<>();
        return  request("/dashboard.do?getVisReservationData", param);
    }



}
