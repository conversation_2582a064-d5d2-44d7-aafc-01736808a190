package com.gh.acs.controller;

import cn.hutool.core.io.FileUtil;
import com.gh.common.utils.GHResponse;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import net.sourceforge.tess4j.ITesseract;
import net.sourceforge.tess4j.Tesseract;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.File;

/**
 * Description:
 * User: zhangkeguang
 * Date: 2024-02-29-11:04
 */

@Api(tags = "TEST")
@RestController
@RequestMapping("/test")
@Slf4j
public class TestController {

    @PostMapping("/verifyCaptcha")
    public GHResponse verifyCaptcha() {







        // 保存上传的验证码图片
        File tempFile = FileUtil.file("/Users/<USER>/downloads/code.png");
//            captchaImage.transferTo(tempFile);

        // 调用Tess4J进行识别
        String recognizedDigits =recognizeCaptcha(tempFile);



        return GHResponse.ok(recognizedDigits);
    }


    public String recognizeCaptcha(File captchaImage) {
        ITesseract tesseract = new Tesseract();

        try {
//            // 设置Tesseract数据文件的路径（可选）
            tesseract.setDatapath("/opt/homebrew/Cellar/tesseract/5.3.4_1/share/tessdata");
            tesseract.setTessVariable("user_defined_dpi", "300");
//            tesseract.setTessVariable("tessedit_cmd", "/opt/homebrew/Cellar/tesseract/5.3.4_1/bin");
            // 识别验证码图片
            String result = tesseract.doOCR(captchaImage);

            // 过滤出数字部分
            result = result.replaceAll("[^0-9]", "");
            log.info("识别结果：" + result);
            result.replace("\n", "");
            log.info("识别结果：" + result);

            return result;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }


}
