package com.gh.acs.broadcast.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gh.acs.broadcast.model.entity.ITCGroup;
import com.gh.acs.broadcast.model.entity.ITCTerminal;


import java.util.List;

public interface ITCGroupMapper extends BaseMapper<ITCGroup> {
    List<ITCTerminal> getTerminalByGroup(Page page, Integer groupId);
}
