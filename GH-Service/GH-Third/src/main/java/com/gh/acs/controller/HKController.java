package com.gh.acs.controller;


import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import com.gh.acs.model.dto.DoorState1DTO;
import com.gh.acs.model.dto.DoorStateDTO;
import com.gh.acs.model.vo.DoorControlVO;
import com.gh.acs.model.vo.HKTerminalCmdVO;
import com.gh.acs.service.HKService;
import com.gh.common.utils.GHResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Api("海康平台管理")
@RestController
@RequestMapping("/hk")
@Slf4j
public class HKController {

    @Autowired
    private HKService service;


    @GetMapping("device")
    @ApiOperation("门禁控制器")
    public GHResponse<Object> getDeviceDoor(String ip, Integer page, Integer size,String region) throws Exception {
//        if(StrUtil.isEmpty(region))
//        {
//             region="e644345da777420ca81c17c2b65f44e9";
//        }
        String doorDevice = service.getDoorDeviceState(page,size,ip,region);
        return GHResponse.ok(JSONObject.parseObject(doorDevice));
    }

    @GetMapping("door")
    @ApiOperation("门禁点状态")
    public GHResponse<Object> getDoor(String doorName, Integer page, Integer size,String doorIndexCode,String region) throws Exception {
//        if(StrUtil.isEmpty(region))
//        {
//            region="e644345da777420ca81c17c2b65f44e9";
//        }
        String resp = service.getDoorState(page, size, doorName,region);
        List<DoorStateDTO> doorState=new ArrayList<>();
        long total = 0;
        if (StrUtil.isNotEmpty(resp)) {
            JSONObject parseObject = JSONObject.parseObject(resp);
            JSONObject data = JSONObject.parseObject(parseObject.getString("data"));
            JSONArray list = JSONObject.parseArray(data.getString("list"));
            total=  data.getLong("total");
            if (null != list) {
                 doorState = JSONObject.parseArray(list.toJSONString(), DoorStateDTO.class);
            }
        }


        List<DoorState1DTO> doorState11 = service.getDoorState1(page, size, null);
        for (DoorStateDTO door : doorState)
        {
            List<DoorState1DTO> collect = doorState11.stream().filter(state -> state.getDoorIndexCode().equals(door.getIndexCode()))
                    .collect(Collectors.toList());
            if(collect.size() > 0)
            {
                door.setDoorState(collect.get(0).getDoorState());
                door.setDoorName(door.getName());
            }
        }
        if(StrUtil.isNotEmpty(doorIndexCode))
        {
            doorState = doorState.stream().filter(door -> door.getIndexCode().equals(doorIndexCode)).collect(Collectors.toList());
        }
        return GHResponse.ok(doorState,total);

    }
    @GetMapping("door1")
    @ApiOperation("平顶山门禁点状态")
    public GHResponse<Object> getDoor1(String ip, Integer page, Integer size) throws Exception {
        List<DoorState1DTO> state1 = service.getDoorState1(page, size, ip);
        return GHResponse.ok(state1);
    }

    @PostMapping("doorControl")
    public GHResponse<Object> doorControl( @RequestBody DoorControlVO doorControlVO) throws Exception {
        String data = service.doorControl(doorControlVO);
        log.info("-----"+data);
        JSONObject object = JSONObject.parseObject(data);
        String code = object.getString("code");
        if(StrUtil.isNotEmpty(code)&&code.equals("0"))
        {
            return GHResponse.ok();
        }
        return GHResponse.failed();
    }


    @GetMapping("event")
    public GHResponse<Object> getDoorEvent(String personName,String bt,String et, Integer page, Integer size,String doorName,String reginCode ) throws Exception {

//        if(StrUtil.isEmpty(reginCode))
//        {
//            reginCode="e644345da777420ca81c17c2b65f44e9";
//        }

        if(StrUtil.isNotEmpty(bt)&&!bt.contains("T"))
        {
            DateTime now= DateUtil.parse(bt);

            bt = DateUtil.format(now, "YYYY-MM-dd'T'HH:mm:ss'+08:00'");
        }else{
            GHResponse.failed("开始时间bt不能为空");
        }
        if(StrUtil.isNotEmpty(et)&&!et.contains("T"))
        {
            DateTime now= DateUtil.parse(et);

            et = DateUtil.format(now, "YYYY-MM-dd'T'HH:mm:ss'+08:00'");
        }else{
            GHResponse.failed("结束时间et不能为空");
        }
        String doorDevice = service.getDoorEvent(page,size,bt,et,personName,doorName,reginCode);

        return  getGHResponse(doorDevice);

    }


    @GetMapping("vehicleList")
    public GHResponse<Object> vehicleList(String plateNo, Integer page, Integer size) throws Exception {
        String data = service.vehicleList(plateNo,page,size);
        JSONObject object = JSONObject.parseObject(data);
        JSONObject resp = object.getObject("data", JSONObject.class);
        return GHResponse.ok(resp);
    }


    @GetMapping("crossRecords")
    public GHResponse<Object> crossRecords(String plateNo, Integer page, Integer size,String bt,String et) throws Exception {
        if(StrUtil.isNotEmpty(bt)&&!bt.contains("T"))
        {
            DateTime now= DateUtil.parse(bt);

            bt = DateUtil.format(now, "YYYY-MM-dd'T'HH:mm:ss'+08:00'");
        }else{
            GHResponse.failed("开始时间bt不能为空");
        }
        if(StrUtil.isNotEmpty(et)&&!et.contains("T"))
        {
            DateTime now= DateUtil.parse(et);

            et = DateUtil.format(now, "YYYY-MM-dd'T'HH:mm:ss'+08:00'");
        }else{
            GHResponse.failed("结束时间et不能为空");
        }
        String data = service.crossRecords(plateNo,page,size,bt,et);
        JSONObject object = JSONObject.parseObject(data);
        JSONObject resp = object.getObject("data", JSONObject.class);
        return GHResponse.ok(resp);

    }


    @GetMapping("tempCarInRecords")
    public GHResponse<Object> tempCarInRecords(String plateNo, Integer page, Integer size,String bt,String et) throws Exception {
        String data = service.tempCarInRecords(plateNo,page,size,bt,et);
        JSONObject object = JSONObject.parseObject(data);
        JSONObject resp = object.getObject("data", JSONObject.class);
        return GHResponse.ok(resp);
    }

    @GetMapping("terminal")
    public GHResponse<Object> terminal(String keyword,Integer page,Integer size) throws Exception {
        String data = service.getTerminal(keyword,page,size);
        JSONObject object = JSONObject.parseObject(data);
        JSONObject resp = object.getObject("data", JSONObject.class);
        return GHResponse.ok(resp);
    }

    @GetMapping("program")
    public GHResponse<Object> program(String keyword,Integer page,Integer size) throws Exception {
        String data = service.getPrograms(keyword,page,size);
        JSONObject object = JSONObject.parseObject(data);
        JSONObject resp = object.getObject("data", JSONObject.class);
        return GHResponse.ok(resp);
    }




    @PostMapping("cmd")
    public GHResponse<Object> cmd( @RequestBody HKTerminalCmdVO hkTerminalCmdVO) throws Exception {
        String data = service.terminalCmd(hkTerminalCmdVO);
        JSONObject object = JSONObject.parseObject(data);
        log.error(data);
        String code = object.getString("code");
        if(StrUtil.isNotEmpty(code)&&code.equals("0"))
        {
            return GHResponse.ok();
        }
        return GHResponse.failed();
    }


    @GetMapping("event-log")
    public GHResponse<Object> event(String keyword, Integer page, Integer size,String bt,String et) throws Exception {
        String data = service.eventLogs(keyword,page,size,bt,et);
        JSONObject object = JSONObject.parseObject(data);
        JSONObject resp = object.getObject("data", JSONObject.class);
        return GHResponse.ok(resp);

    }

    @GetMapping("monitoringPoint")
    @ApiOperation("门禁控制器")
    public GHResponse<Object> monitoringPoint(String keyword, Integer page, Integer size) throws Exception {
        String point = service.monitoringPoint(keyword,page,size);
        return  getGHResponse(point);
    }

    @GetMapping("appointment")
    @ApiOperation("访客预约记录")
    public GHResponse<Object> appointment(String keyword, Integer page, Integer size,String bt,String et) throws Exception {
        String point = service.appointment(keyword,page,size,bt,et);
        return  getGHResponse(point);
    }

    private GHResponse getGHResponse(String data)
    {
        if(StrUtil.isNotEmpty(data))
        {
            try {
                JSONObject object = JSONObject.parseObject(data);
                JSONObject resp = object.getObject("data", JSONObject.class);
                Integer total = resp.getInteger("total");
                JSONArray list = resp.getJSONArray("list");
                return GHResponse.ok(list,total);
            }catch (JSONException e) {
                return GHResponse.failed(e.getMessage());
            }

        }
        return GHResponse.failed("Response data empty");

    }



}
