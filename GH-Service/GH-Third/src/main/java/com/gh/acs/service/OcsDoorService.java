package com.gh.acs.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.gh.acs.model.dto.OperatorRecordDTO;
import com.gh.acs.model.dto.UserCrdTmDTO;
import com.gh.acs.model.entity.OcsDoor;
import com.gh.common.utils.PageResult;

import java.util.List;

public interface OcsDoorService extends IService<OcsDoor> {

    PageResult<List<UserCrdTmDTO>> getParkRecord(String doorName, Integer page, Integer size, String bt, String et);
    PageResult<List<UserCrdTmDTO>> getParkRecordIn(String doorName, Integer page, Integer size, String bt, String et);
    PageResult<List<UserCrdTmDTO>> getParkRecordAno(String doorName, Integer page, Integer size, String bt, String et);

    PageResult<List<OperatorRecordDTO>> getOperatorRecord(String doorName, Integer page, Integer size, String bt, String et);
    String  getCarStatistics();
}
