package com.gh.acs.controller;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.gh.acs.model.dto.OperatorRecordDTO;
import com.gh.acs.model.dto.UserCrdTmDTO;
import com.gh.acs.service.OcsDoorService;
import com.gh.acs.service.OcsService;
import com.gh.common.utils.GHResponse;
import com.gh.common.utils.PageResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Api("立方门禁")
@RestController
@RequestMapping("/ocs")
@Slf4j
public class OcsController {

    @Autowired
    private OcsService service;

    @Autowired
    private OcsDoorService ocsDoorService;


    @GetMapping("door")
    @ApiOperation("获取门禁列表")
    public GHResponse<Object> door(String doorName,Integer page,Integer size)  {
        JSONObject item = service.getDoor(doorName,page,size);
        return GHResponse.ok(item);
    }


    @GetMapping("status")
    @ApiOperation("获取门禁状态")
    public GHResponse<Object> status(Integer doorId)  {
        JSONObject item = service.getDoorStatus(doorId);
        return GHResponse.ok(item);
    }

    @GetMapping("record")
    @ApiOperation("获取门禁记录")
    public GHResponse<Object> record(String bt,String et,Integer page,Integer size)  {
        JSONObject item = service.getRecord(bt,et,page,size);
        return GHResponse.ok(item);
    }

    @GetMapping("alarm")
    @ApiOperation("获取门禁报警")
    public GHResponse<Object> alarm(String bt,String et,Integer page,Integer size)  {
        JSONObject item = service.getAlarm(bt,et,page,size);
        return GHResponse.ok(item);
    }

    @GetMapping("opt")
    @ApiOperation("门禁操作")
    public GHResponse<Object> opt(String action,Integer doorId)  {
        JSONObject item = service.opt(action,doorId);
        return GHResponse.ok(item);
    }



    @GetMapping("park-record")
    @ApiOperation("停车记录")
    @DS("ocs")
    public GHResponse parkRecord(String keyword,String bt,String et,Integer page,Integer size)  {
        if(StrUtil.isNotEmpty(keyword))
        {
            keyword = "%"+keyword+"%";
        }
        PageResult<List<UserCrdTmDTO>> record = ocsDoorService.getParkRecord(keyword, page, size, bt, et);
        return GHResponse.ok(record.getData(),record.getTotal());
    }

    @GetMapping("park-record-ano")
    @ApiOperation("场内异常停车记录")
    @DS("ocs")
    public GHResponse parkRecordAno(String keyword,String bt,String et,Integer page,Integer size)  {
        if(StrUtil.isNotEmpty(keyword))
        {
            keyword = "%"+keyword+"%";
        }
        PageResult<List<UserCrdTmDTO>> record = ocsDoorService.getParkRecordAno(keyword, page, size, bt, et);
        return GHResponse.ok(record.getData(),record.getTotal());
    }

    @GetMapping("park-record-in")
    @ApiOperation("场内车辆")
    @DS("ocs")
    public GHResponse parkRecordIn(String keyword,String bt,String et,Integer page,Integer size)  {
        if(StrUtil.isNotEmpty(keyword))
        {
            keyword = "%"+keyword+"%";
        }
        PageResult<List<UserCrdTmDTO>> record = ocsDoorService.getParkRecordIn(keyword, page, size, bt, et);
        return GHResponse.ok(record.getData(),record.getTotal());
    }


    @GetMapping("park-operator-record")
    @ApiOperation("开闸记录")
    @DS("ocs")
    public GHResponse operatorRecord(String keyword,String bt,String et,Integer page,Integer size)  {
        if(StrUtil.isNotEmpty(keyword))
        {
            keyword = "%"+keyword+"%";
        }
        PageResult<List<OperatorRecordDTO>> record = ocsDoorService.getOperatorRecord(keyword, page, size, bt, et);
        return GHResponse.ok(record.getData(),record.getTotal());
    }



    @GetMapping("car-statistics")
    @ApiOperation("车辆统计")
    @DS("ocs")
    public GHResponse carStatistics()  {
        String carStatistics = ocsDoorService.getCarStatistics();
        return GHResponse.ok(carStatistics);
    }


}
