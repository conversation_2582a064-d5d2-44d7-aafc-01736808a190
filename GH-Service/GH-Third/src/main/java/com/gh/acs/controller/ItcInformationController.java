package com.gh.acs.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.gh.acs.model.vo.ITCInfoCmd;
import com.gh.acs.service.ItcInformationService;
import com.gh.common.utils.GHResponse;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * Description: itc信息发布
 * User: zhangkeguang
 * Date: 2022-01-11-16:08
 */
@Api("itc信息发布")
@RestController
@RequestMapping("/itc-info")
public class ItcInformationController {
    @Autowired
    private ItcInformationService itcInformationService;


    @GetMapping("status")
    public GHResponse<Object> getDeviceStatus()
    {
        Object status = itcInformationService.getDeviceStatus();
        return GHResponse.ok(JSONUtil.toJsonStr(status));

    }

    @GetMapping("device")
    public GHResponse getDevice(String name,Integer page)
    {
        Object status = itcInformationService.getDevice(name,page);

        return GHResponse.ok(JSONUtil.toJsonStr(status));

    }

    @GetMapping("show")
    public GHResponse getShow(String name,Integer page)
    {
        Object status = itcInformationService.listShow(name,page);
        return GHResponse.ok(JSONUtil.toJsonStr(status));

    }

    @PostMapping("cmd")
    public GHResponse cmd(@RequestBody ITCInfoCmd cmd)
    {
        boolean success = itcInformationService.cmd(CollUtil.join(cmd.getIds(),","),cmd.getType());
        if(success)
        {
            return GHResponse.ok();
        }
        return GHResponse.failed();

    }

    @PostMapping("voice")
    public GHResponse voice(@RequestBody  ITCInfoCmd cmd)
    {
        boolean success = itcInformationService.setVoice(CollUtil.join(cmd.getIds(), ","), cmd.getType());
        if(success)
        {
            return GHResponse.ok();
        }
        return GHResponse.failed();

    }
}
