package com.gh.acs.broadcast.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.gh.acs.broadcast.model.dto.MusicTask;
import com.gh.acs.broadcast.model.dto.PlayCmd;
import com.gh.acs.broadcast.model.dto.TTSTask;
import com.gh.acs.broadcast.model.dto.TaskState;
import com.gh.acs.broadcast.model.entity.ITCTask;
import com.gh.acs.broadcast.service.ITCService;
import com.gh.acs.broadcast.service.ITCTaskService;
import com.gh.common.utils.GHResponse;
import com.gh.log.annotation.OperLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RequestMapping("/itc/task")
@RestController
@Api(tags = "任务管理")
public class TaskController {
    @Autowired
    private ITCTaskService itcTaskService;
    @Autowired
    private ITCService itcService;


    @GetMapping
    @ApiOperation("查询任务列表")
    public GHResponse<List<ITCTask>> Selects( )
    {
        List<ITCTask> list = itcService.getTaskList();
//        List<ITCTask> itcTasks = itcTaskService.list();
//        if(list!=null)
//        {
//            list.forEach(itcTask -> {
//                List<ITCTask> tasks = itcTasks.stream().filter(itcTask1 -> itcTask.getTaskID().contains(itcTask1.getTaskID()))
//                        .collect(Collectors.toList());
//                if(CollUtil.isNotEmpty(tasks))
//                {
//                    itcTask.setTaskStatus(tasks.get(0).getStatus());
//                    itcTask.setTids(tasks.get(0).getTids());
//                    itcTask.setMids(tasks.get(0).getMids());
//                    itcTask.setPriority(tasks.get(0).getPriority());
//                }
//            });
//        }

        return GHResponse.ok(list);
    }

    //pause	暂停播放
    //resume	继续播放
    //previous_play	上一曲
    //next_play	下一曲
    //play_index	指定序号歌曲播放
    //progress	指定进度	当前歌曲进度的百分比(0-100)
    //play_mode	播放模式	[参考播放模式表]

    @PostMapping("cmd")
    @ApiOperation("任务远程控制")
    @OperLog(category = "ITC任务管理",description = "任务远程控制")
    public GHResponse<List<ITCTask>> cmd(@RequestBody @Valid PlayCmd playCmd)
    {
        boolean b=  itcService.CmdTask(playCmd.getTaskId(),playCmd.getCode());
        if(b)
        {
//            if(playCmd.getCode().equalsIgnoreCase("pause"))
//            {
//                itcTaskService.lambdaUpdate().set(ITCTask::getStatus,"task_process_stop")
//                        .eq(ITCTask::getTaskID,playCmd.getTaskId())
//                        .update();
//            }else if(playCmd.getCode().equalsIgnoreCase("resume"))
//            {
//                itcTaskService.lambdaUpdate().set(ITCTask::getStatus,"task_process_work")
//                        .eq(ITCTask::getTaskID,playCmd.getTaskId())
//                        .update();
//            }

            return GHResponse.ok();
        }
        return GHResponse.failed();
    }

    @PostMapping("stop")
    @ApiOperation("停止任务")
    @OperLog(category = "ITC任务管理",description = "停止任务")
    public GHResponse<List<ITCTask>> stop(@RequestParam String taskId)
    {
        boolean b=  itcService.stopTask(taskId);
        if(b)
        {
            return GHResponse.ok();
        }
        return GHResponse.failed();
    }

    //    normal_mode	常规播放(列表播放1次)
    //    list_cycle_mode	列表循环播放
    //    random_mode	随机模式
    //    single_cycle_mode	单曲循环
    @PostMapping("musicTask")
    @ApiOperation("创建点播任务")
    @OperLog(category = "ITC任务管理",description = "创建点播任务")
    public GHResponse<List<ITCTask>> musicTask(@RequestBody MusicTask task) throws Exception {

        List<ITCTask> taskList = itcService.getTaskList();
        if(CollUtil.isNotEmpty(taskList))
        {
           for (ITCTask itcTask:taskList)
           {
               JSONArray array = JSONUtil.parseArray(itcTask.getTids());
               List<Integer> list = JSONUtil.toList(array, Integer.class);
               int size = CollUtil.intersection(list, task.getMids()).size();
               if(size>0)
               {
                   throw  new Exception("终端正在被任务："+itcTask.getTaskName()+"使用");
               }
           }
        }
        boolean b=  itcService.createMusicTask(task.getTids(),task.getMids(),task.getPriority(),
                task.getVolume(),task.getMode(),task.getTaskName());
        if(b)
        {
            return GHResponse.ok();
        }
        return GHResponse.failed();
    }

    @PostMapping("ttsTask")
    @ApiOperation("创建tts任务")
    @OperLog(category = "ITC任务管理",description = "创建tts任务")
    public GHResponse<List<ITCTask>> ttsTask(@RequestBody TTSTask task)
    {
        boolean b=  itcService.createTTSTask(task.getTids(),task.getPriority(),task.getVolume(),
                task.getSpeed(),task.getTimes(),task.getText(),task.getTaskName());
        if(b)
        {
            return GHResponse.ok();
        }
        return GHResponse.failed();
    }


    @GetMapping("statistics")
    @ApiOperation("任务统计")
    public GHResponse statistics()
    {
        List<ITCTask> list = itcTaskService.list();
        Map<Integer,Integer> map=new HashMap<Integer,Integer>(){
            {
                put(1,0);//点播
                put(2,0);//tts
            }
        };
        list.forEach(itcTask -> {
            if(StrUtil.isNotEmpty(itcTask.getTextContent()))//tts
            {
                 map.put(2,map.get(2)+1);
            }else
            {
                 map.put(1,map.get(1)+1);
            }
        });
        return GHResponse.ok(map);
    }




    @GetMapping("status")
    @ApiOperation("根据id获取任务状态")
    public GHResponse status(String taskId)
    {
        TaskState info = itcService.getTaskInfo(taskId);

        return GHResponse.ok(info);
    }







}
