package com.gh.acs.controller;

import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.gh.acs.model.dto.HaiNanInRecord;
import com.gh.acs.model.dto.HaiNanParkRecord;
import com.gh.acs.model.dto.HaiNanParkRecordOpt;
import com.gh.acs.service.HaiNanParkService;
import com.gh.common.utils.GHResponse;
import com.gh.common.utils.PageResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * Description:
 * User: zhangkeguang
 * Date: 2024-01-30-15:07
 */

@Api("海南停车场")
@RestController
@RequestMapping("/hainan")

public class HaiNanController {

    @Autowired
    private HaiNanParkService service;

    @GetMapping("park-record")
    @ApiOperation("停车记录")
    @DS("park")
    public GHResponse<Object> getRecord(String keyword,Integer page,Integer size,String bt,String et)  {
        if(StrUtil.isNotEmpty(keyword)){
            keyword = "%"+keyword+"%";
        }
        PageResult<List<HaiNanParkRecord>> record = service.getRecord(keyword, bt, et, page, size);
        return GHResponse.ok(record.getData(),record.getTotal());
    }


    @GetMapping("park-record-opt")
    @ApiOperation("停车记录开闸")
    @DS("park")
    public GHResponse<Object> getRecordOpt(String keyword,Integer page,Integer size,String bt,String et)  {
        if(StrUtil.isNotEmpty(keyword)){
            keyword = "%"+keyword+"%";
        }
        PageResult<List<HaiNanParkRecordOpt>> record = service.getOptRecord(keyword, bt, et, page, size);
        return GHResponse.ok(record.getData(),record.getTotal());
    }

    @GetMapping("park-record-in")
    @ApiOperation("场内车辆")
    @DS("park")
    public GHResponse<Object> getRecordIn(String keyword,Integer page,Integer size,String bt,String et)  {
        if(StrUtil.isNotEmpty(keyword)){
            keyword = "%"+keyword+"%";
        }
        PageResult<List<HaiNanInRecord>> record = service.getInRecord(keyword, bt, et, page, size);
        return GHResponse.ok(record.getData(),record.getTotal());
    }

}
