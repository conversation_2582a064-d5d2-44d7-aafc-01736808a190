package com.gh.acs.controller;


import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gh.acs.model.dto.CarPlaceDTO;
import com.gh.acs.model.entity.ParkRecord;
import com.gh.acs.model.entity.Record;
import com.gh.acs.service.ParkRecordService;
import com.gh.acs.service.ParkService;
import com.gh.common.utils.GHResponse;
import com.gh.common.utils.PageResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RequestMapping("/park")
@RestController
@Api(tags = "钟山风景区停车系统")
public class ParkController {

    @Autowired
    private ParkService service;

    @Autowired
    private ParkRecordService recordService;


    @GetMapping("test")
    @ApiOperation("查询记录")
    public GHResponse record(String license,long bt,long et,Integer page,Integer size)
    {
        Page page1=null;
        if(PageResult.isPage(page,size))
        {
            page1=new Page(page,size);
        }
        LambdaQueryChainWrapper<Record> wrapper = service.lambdaQuery().like(StrUtil.isNotEmpty(license), Record::getLicense, license)
                .between(Record::getEventTime, bt, et);
        if(page1!=null)
        {
            Page page2 = wrapper.page(page1);
            return GHResponse.ok(page2.getRecords(),page2.getTotal());
        }
        List<Record> list = wrapper.list();
        return  GHResponse.ok(list,list.size());
    }


    @PostMapping("event")
    @ApiOperation("上报事件")
    public JSONObject set(@RequestBody JSONObject data)
    {
        Record record = JSONUtil.toBean(data, Record.class);
        int count = service.saveRecord(record, "record" + DateUtil.date().toString("yyyyMM"));
        JSONObject resp=new JSONObject();
        resp.set("code",200);
        resp.set("msg","success");
        return resp;
    }


    @GetMapping("car")
    @ApiOperation("车位状态")
    public GHResponse car()
    {
        List<CarPlaceDTO> car = service.getCar();
        return GHResponse.ok(car);
    }
    @GetMapping("record")
    @ApiOperation("进出记录")
    @DS("park1")
    public GHResponse record(String keyword,String bt,String et,Integer page,Integer size)
    {
        Page<ParkRecord> recordPage = recordService.lambdaQuery().like(StrUtil.isNotEmpty(keyword), ParkRecord::getRemark, keyword)
                .between(ParkRecord::getInTime,DateUtil.parse(bt),DateUtil.parse(et))
                .page(new Page<>(page, size));

        return GHResponse.ok(recordPage.getRecords(),recordPage.getTotal());
    }


    @GetMapping("plate")
    @ApiOperation("最新一条进出记录")
    @DS("park1")
    public GHResponse plate(String keyword)
    {
        ParkRecord record = recordService.lambdaQuery().like(StrUtil.isNotEmpty(keyword), ParkRecord::getRemark, keyword)
                .orderByDesc(ParkRecord::getInTime)
                .page(new Page<ParkRecord>(1,1)).getRecords().get(0);
        return GHResponse.ok(record);
    }




}
