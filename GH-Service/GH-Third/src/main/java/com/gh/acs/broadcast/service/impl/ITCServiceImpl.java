package com.gh.acs.broadcast.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.gh.acs.broadcast.model.dto.ITCDir;
import com.gh.acs.broadcast.model.dto.ITCMusic;
import com.gh.acs.broadcast.model.dto.TTSEngineInfo;
import com.gh.acs.broadcast.model.dto.TaskState;
import com.gh.acs.broadcast.model.entity.ITCGroup;
import com.gh.acs.broadcast.model.entity.ITCTask;
import com.gh.acs.broadcast.model.entity.ITCTerminal;
import com.gh.acs.broadcast.sdk.ActionCode;
import com.gh.acs.broadcast.sdk.ItcUrl;
import com.gh.acs.broadcast.service.ITCService;
import com.gh.acs.broadcast.service.ITCTaskService;
import com.gh.common.redis.RedisHelper;
import com.gh.common.utils.PageResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;



@Service
@Slf4j
public class ITCServiceImpl implements ITCService {
    @Value("${itc.url}")
    private String url;
    @Value("${itc.username}")
    private String username;
    @Value("${itc.password}")
    private String password;
    @Autowired
    private RedisHelper redisHelper;
    @Autowired
    private ITCTaskService itcTaskService;
    @Override
    public List<ITCTerminal> getTerminal() {
        String token=login();
        if(StrUtil.isNotEmpty(token))
        {
            JSONObject param=new JSONObject();
            param.set("token",token);
            param.set("actioncode", ActionCode.Terminal);
            String path=url+ ItcUrl.Terminal;
            String body = HttpRequest.put(path)
                    .body(param.toString())
                    .execute().body();
            if(StrUtil.isNotEmpty(body))
            {
                JSONObject bodyJson = JSONUtil.parseObj(body);
                if(bodyJson!=null&&bodyJson.get("data")!=null)
                {
                    JSONObject data = JSONUtil.parseObj(bodyJson.get("data"));
                    if(data!=null&&data.get("EndPointsArray")!=null)
                    {
                        return JSONUtil.toList(JSONUtil.parseArray(data.get("EndPointsArray")),ITCTerminal.class);
                    }
                }
            }
        }
        return null;
    }

    @Override
    public List<ITCGroup> getGroup() {
        String token=login();
        if(StrUtil.isNotEmpty(token))
        {
            JSONObject param=new JSONObject();
            String path=url+ ItcUrl.Group;
            String body = HttpRequest.get(path)
                    .header("Authorization",token)
                    .execute().body();
            if(StrUtil.isNotEmpty(body))
            {
                JSONObject bodyJson = JSONUtil.parseObj(body);
                if(bodyJson!=null&&bodyJson.get("data")!=null)
                {
                    return JSONUtil.toList(JSONUtil.parseArray(bodyJson.get("data")),ITCGroup.class);
                }
            }
        }
        return null;

    }

    @Override
    public String getToken() {
       String path=url+ ItcUrl.Login+"?name="+username+"&password="+password;

        String body = HttpRequest.get(path)
                .execute().body();
        log.info("----login--------------------"+body);
        if(StrUtil.isNotEmpty(body))
        {
            JSONObject bodyObj = JSONUtil.parseObj(body);
            if(bodyObj!=null&&bodyObj.get("result")!=null)
            {
                if(bodyObj.get("token")!=null)
                {
                    redisHelper.StringSet(0,"itc_token",bodyObj.get("token").toString(),110, TimeUnit.MINUTES);
                    return bodyObj.get("token").toString();
                }
            }
        }
        return null;
    }

    @Override
    public Boolean setTerminalVolume(Integer volume,List<Integer> tid) {
        String token=login();
        if(StrUtil.isNotEmpty(token)&&CollUtil.isNotEmpty(tid)&&volume!=null)
        {
            JSONObject param=new JSONObject();
            param.set("token",token);
            param.set("actioncode", ActionCode.TerminalVolume);
            JSONObject data = JSONUtil.createObj().set("TerminalID",CollUtil.join(tid,","))
                    .set("Volume", volume);
            param.set("data",data);
            String path=url+ ItcUrl.Terminal_Volume;
            String body = HttpRequest.put(path)
                    .body(param.toString())
                    .execute().body();
            if(StrUtil.isNotEmpty(body))
            {
                JSONObject bodyJson = JSONUtil.parseObj(body);
                Integer result = bodyJson.getInt("result");
                if(result.equals(200))
                {
                    return true;
                }

            }
        }
        return false;
    }

    @Override
    public void setTaskVolume(Integer volume,String taskId) {
        String token=login();
        if(StrUtil.isNotEmpty(token))
        {
            JSONObject param=new JSONObject();
            param.set("token",token);
            param.set("actioncode", ActionCode.TaskVolume);
            JSONObject data = JSONUtil.createObj().set("TaskID", taskId)
                    .set("Volume", volume);
            param.set("data",data);
            String path=url+ ItcUrl.API;
            String body = HttpRequest.put(path)
                    .body(param.toString())
                    .execute().body();
        }
    }

    @Override
    public JSONObject getTerminalLog(String keyword, Integer page, Integer size, String bt, String et) {
        String param="";
        String path=url+ ItcUrl.Terminal_Log;
        if(StrUtil.isNotEmpty(keyword))
        {
            param= param+ "&search_terminals="+keyword;
        }
        if(PageResult.isPage(page,size))
        {
            if(StrUtil.isNotEmpty(param))
            {
                param+="&";
            }
            param= param+"page="+page+"&limit="+size;
        }
        if(StrUtil.isNotEmpty(bt))
        {
            if(StrUtil.isNotEmpty(param))
            {
                param+="&";
            }
            param= param+"start_date="+bt;
        }
        if(StrUtil.isNotEmpty(et))
        {
            if(StrUtil.isNotEmpty(param))
            {
                param+="&";
            }
            param= param+"end_date="+et;
        }
//        if(StrUtil.isNotEmpty(param))
//        {
//            path=path+"?"+param;
//        }
        String body = HttpRequest
                .get(path)
                .header("Authorization",login())
                .execute().body();
        if(StrUtil.isNotEmpty(body))
        {
            return JSONUtil.parseObj(body);
        }
        return  null;
    }

    @Override
    public JSONObject getTaskLog(String keyword, Integer page, Integer size, String bt, String et) {
        String param="";
        String path=url+ ItcUrl.Task_Log;

        if(PageResult.isPage(page,size))
        {
            if(StrUtil.isNotEmpty(param))
            {
                param+="&";
            }
            param= param+"page="+page+"&limit="+size;
        }
        if(StrUtil.isNotEmpty(bt))
        {
            if(StrUtil.isNotEmpty(param))
            {
                param+="&";
            }
            param= param+"start_date="+bt;
        }
        if(StrUtil.isNotEmpty(et))
        {
            if(StrUtil.isNotEmpty(param))
            {
                param+="&";
            }
            param= param+"end_date="+et;
        }
//        if(StrUtil.isNotEmpty(param))
//        {
//            path=path+"?"+param;
//        }
        String body = HttpRequest
                .get(path)
                .header("Authorization",login())
                .execute().body();
        if(StrUtil.isNotEmpty(body))
        {
            return JSONUtil.parseObj(body);
        }
        return  null;
    }

    @Override
    public String getTTSEngine() {
        String token=login();
        if(StrUtil.isNotEmpty(token))
        {
            JSONObject param=new JSONObject();
            param.set("token",token);
            param.set("actioncode", ActionCode.TTS_Engine);
            String path=url+ ItcUrl.API;
            String body = HttpRequest.put(path)
                    .body(param.toString())
                    .execute().body();
            if(StrUtil.isNotEmpty(body))
            {
                JSONObject bodyJson = JSONUtil.parseObj(body);
                if(bodyJson!=null&&bodyJson.get("data")!=null)
                {
                    JSONObject data = JSONUtil.parseObj(bodyJson.get("data"));
                    if(data!=null&&data.get("TTSEngineInfo")!=null)
                    {
                        List<TTSEngineInfo> engineInfo = JSONUtil.toList(JSONUtil.parseArray(data.get("TTSEngineInfo")), TTSEngineInfo.class);
                        if(CollUtil.isNotEmpty(engineInfo))
                        {
                            return engineInfo.get(0).getEngineName();
                        }
                    }
                }
            }
        }
        return null;
    }

    @Override
    public List<ITCDir> getMusic() {
        String token=login();
        if(StrUtil.isNotEmpty(token))
        {
            JSONObject param=new JSONObject();
            param.set("token",token);
            param.set("actioncode", ActionCode.Music_List);
            String path=url+ ItcUrl.API;
            String body = HttpRequest.put(path)
                    .body(param.toString())
                    .execute().body();
            if(StrUtil.isNotEmpty(body))
            {
                JSONObject bodyJson = JSONUtil.parseObj(body);
                if(bodyJson!=null&&bodyJson.get("data")!=null)
                {
                    JSONObject data = JSONUtil.parseObj(bodyJson.get("data"));
                    if(data!=null&&data.get("MusicInfo")!=null)
                    {
                        JSONObject musicInfo = JSONUtil.parseObj(data.get("MusicInfo"));
                        List<ITCDir> dirs=new ArrayList<>();
                        List<ITCMusic> musics=new ArrayList<>();
                        if(musicInfo.get("DirArray")!=null)
                        {
                            dirs = JSONUtil.toList(JSONUtil.parseArray(musicInfo.get("DirArray")), ITCDir.class);
                        }
                        if(musicInfo.get("MusicArray")!=null)
                        {
                            musics = JSONUtil.toList(JSONUtil.parseArray(musicInfo.get("MusicArray")), ITCMusic.class);

                        }
                        for (ITCDir itcDir : dirs)
                        {
                            List<ITCMusic> list = musics.stream()
                                    .filter(itcMusic -> itcMusic.getDirID().equals(itcDir.getDirID()))
                                    .collect(Collectors.toList());
                            itcDir.setMusics(list);
                        }
                        return dirs;
                    }
                }
            }
        }
        return null;
    }

    @Override
    public boolean upload(Integer id, byte[] file,String name) {
        String token=login();
        if(StrUtil.isNotEmpty(token))
        {
            String body = HttpRequest.post(url + ItcUrl.Upload + id)
                    .form("file", file, name)
                    .header("Authorization",token)
                    .timeout(1000*20)
                    .execute().body();
            if(StrUtil.isNotEmpty(body))
            {
                JSONObject jsonObject = JSONUtil.parseObj(body);
                if(jsonObject!=null&&jsonObject.get("result")!=null)
                {
                        return true;
                }
            }
        }

        return false;
    }

//    normal_mode	常规播放(列表播放1次)
//    list_cycle_mode	列表循环播放
//    random_mode	随机模式
//    single_cycle_mode	单曲循环
    @Override
    public boolean createMusicTask(List<Integer> tids, List<Integer> mids, Integer priority, Integer volume, String mode, String taskName) {
        String token=login();
        if(StrUtil.isNotEmpty(token))
        {
            String taskId=IdUtil.randomUUID().replace("-","").toUpperCase();
            JSONObject param=new JSONObject();
            param.set("token",token);
            param.set("actioncode", ActionCode.Damand_Music);
            JSONObject data=new JSONObject();
            data.set("EndPointsAdditionalProp","");
            data.set("EndPointGroupIDs",new JSONArray());
            data.set("MusicGroupIDs",new JSONArray());
            data.set("EndPointIDs",tids);
            data.set("MusicIDs",mids);
            data.set("TaskID","{"+taskId +"}");
            data.set("TaskName",taskName);
            data.set("Priority",priority);
            data.set("Volume",volume);
            data.set("PlayMode",mode);
            param.set("data",data);
            String path=url+ ItcUrl.API;
            String body = HttpRequest.put(path)
                    .body(param.toString())
                    .execute().body();
            if(StrUtil.isNotEmpty(body))
            {
                JSONObject bodyJson = JSONUtil.parseObj(body);
                if(bodyJson!=null&&bodyJson.get("data")!=null)
                {
                    JSONObject dataJson = JSONUtil.parseObj(bodyJson.get("data"));
                    if(dataJson.get("RemoteID")!=null)
                    {
                        ITCTask task = ITCTask.builder().TaskID(taskId)
                                .tids(JSONUtil.toJsonStr(tids))
                                .mids(JSONUtil.toJsonStr(mids))
                                .TaskName(taskName)
                                .priority(priority)
                                .status("task_process_work")
                                .TaskVolume(volume).build();
                        itcTaskService.save(task);
                        return true;
                    }

                }
            }
        }
        return false;
    }

    @Override
    public boolean createTTSTask(List<Integer> tids,  Integer priority, Integer volume, Integer speed, Integer times, String text,String taskName) {
        String token=login();
        String engine=getTTSEngine();
        if(StrUtil.isNotEmpty(token)&&StrUtil.isNotEmpty(engine))
        {
            String taskId=IdUtil.randomUUID().replace("-","").toUpperCase();
            JSONObject param=new JSONObject();
            param.set("token",token);
            param.set("actioncode", ActionCode.TTS_Music);
            JSONObject data=new JSONObject();
            data.set("EndPointsAdditionalProp","");
            data.set("EndPointIDs",tids);
            data.set("TTSEngineName",engine);
            data.set("TaskID","{"+taskId +"}");
            data.set("TaskName",taskName);
            data.set("Priority",priority);
            data.set("Volume",volume);
            data.set("TTSSpeed",speed);
            data.set("RepeatTime",times);
            data.set("TextContent",text);
            param.set("data",data);
            String path=url+ ItcUrl.API;
            String body = HttpRequest.put(path)
                    .body(param.toString())
                    .execute().body();
            if(StrUtil.isNotEmpty(body))
            {
                JSONObject bodyJson = JSONUtil.parseObj(body);
                if(bodyJson!=null&&bodyJson.get("data")!=null)
                {
                    JSONObject dataJson = JSONUtil.parseObj(bodyJson.get("data"));
                    if(dataJson.get("RemoteID")!=null)
                    {
                        ITCTask task = ITCTask.builder().TaskID(taskId)
                                .TaskName(taskName)
                                .TextContent(text)
                                .TTSSpeed(speed)
                                .tids(JSONUtil.toJsonStr(tids))
                                .RepeatTime(times)
                                .priority(priority)
                                .TaskVolume(volume).build();
                        itcTaskService.save(task);
                        return true;
                    }

                }
            }
        }
        return false;
    }

    @Override
    public boolean CmdTask(String taskId, String code) {
        String token=login();
        if(!taskId.contains("{"))
        {
            taskId="{"+taskId+"}";
        }
        if(StrUtil.isNotEmpty(token))
        {

            JSONObject param=new JSONObject();
            param.set("token",token);
            param.set("actioncode", ActionCode.Remote_Play);
            JSONObject data=new JSONObject();
            data.set("TaskID",taskId);
            data.set("ControlCode",code);

            param.set("data",data);
            String path=url+ ItcUrl.API;
            String body = HttpRequest.put(path)
                    .body(param.toString())
                    .execute().body();
            if(StrUtil.isNotEmpty(body))
            {
                JSONObject bodyJson = JSONUtil.parseObj(body);
                if(bodyJson!=null&&bodyJson.get("data")!=null)
                {
                   return true;
                }
            }
        }
        return false;
    }

    @Override
    public List<ITCTask> getTaskList() {
        String token=login();
        if(StrUtil.isNotEmpty(token))
        {
            JSONObject param=new JSONObject();
            param.set("token",token);
            param.set("actioncode", ActionCode.Task_List);
            String path=url+ ItcUrl.API;
            String body = HttpRequest.put(path)
                    .body(param.toString())
                    .execute().body();
            if(StrUtil.isNotEmpty(body))
            {
                JSONObject bodyJson = JSONUtil.parseObj(body);
                if(bodyJson!=null&&bodyJson.get("data")!=null)
                {
                    JSONObject data = JSONUtil.parseObj(bodyJson.get("data"));
                    if(data!=null&&data.get("TaskInfoArray")!=null)
                    {
                        return JSONUtil.toList(JSONUtil.parseArray(data.get("TaskInfoArray")),ITCTask.class);
                    }
                }
            }
        }
        return null;
    }

    @Override
    public boolean stopTask(String taskId) {
        String token=login();
        if(StrUtil.isNotEmpty(token)&&StrUtil.isNotEmpty(taskId))
        {
            if(!taskId.contains("{"))
            {
                taskId="{"+taskId+"}";
            }
            JSONObject param=new JSONObject();
            param.set("token",token);
            param.set("actioncode", ActionCode.Stop_Task);
            JSONObject data=new JSONObject();
            data.set("TaskID",taskId);
            param.set("data",data);
            String path=url+ ItcUrl.API;
            String body = HttpRequest.put(path)
                    .body(param.toString())
                    .execute().body();
            if(StrUtil.isNotEmpty(body))
            {
                JSONObject bodyJson = JSONUtil.parseObj(body);
                if(bodyJson!=null&&bodyJson.get("data")!=null)
                {
                    itcTaskService.remove(Wrappers.<ITCTask>lambdaQuery()
                    .eq(ITCTask::getTaskID,taskId));
                    return true;
                }
            }
        }
        return false;
    }

    @Override
    public boolean addMusicCategory(String name, Integer isPublic) {
        String token=login();
        if(StrUtil.isNotEmpty(token))
        {

            JSONObject param=new JSONObject();
            param.set("name",name);
            param.set("is_public", isPublic);

            String path=url+ ItcUrl.addMusicCategory;
            String body = HttpRequest.post(path)
                    .header("Authorization",token)
                    .body(param.toString())
                    .execute().body();
            if(StrUtil.isNotEmpty(body))
            {
                JSONObject bodyJson = JSONUtil.parseObj(body);
                if(bodyJson!=null&&bodyJson.get("result")!=null)
                {
                    JSONObject result = JSONUtil.parseObj(bodyJson.get("result"));
                    if(result!=null&&result.get("name")!=null)
                    {
                       return true;
                    }
                }
            }
        }
        return false;
    }


    @Override
    public TaskState getTaskInfo(String taskId) {
        String token=login();
        if(StrUtil.isNotEmpty(token))
        {
            JSONObject param=new JSONObject();
            param.set("token",token);
            param.set("actioncode", ActionCode.Task_State);
            JSONObject data=new JSONObject();
            data.set("TaskID",taskId);
            param.set("data",data);
            String path=url+ ItcUrl.API;
            String body = HttpRequest.put(path)
                    .body(param.toString())
                    .execute().body();
            if(StrUtil.isNotEmpty(body))
            {
                JSONObject bodyJson = JSONUtil.parseObj(body);
                if(bodyJson!=null&&bodyJson.get("data")!=null)
                {
                    TaskState state = JSONUtil.toBean(bodyJson.getJSONObject("data"), TaskState.class);
                    return state;
                }
            }
        }
        return null;
    }

    private String login()
    {
        String itcToken = redisHelper.StringGet(0, "itc_token");
        if(StrUtil.isNotEmpty(itcToken))
        {
            return itcToken;
        }else
        {
            return getToken();
        }
    }


}
