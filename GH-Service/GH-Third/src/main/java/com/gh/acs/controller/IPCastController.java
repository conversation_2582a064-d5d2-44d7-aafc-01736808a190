package com.gh.acs.controller;


import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.gh.acs.model.dto.FileDTO;
import com.gh.acs.model.dto.SessionDTO;
import com.gh.acs.model.dto.TerminalDTO;
import com.gh.acs.model.vo.CtrlVo;
import com.gh.acs.model.vo.FileVo;
import com.gh.acs.model.vo.PlayVo;
import com.gh.acs.service.IPCastService;
import com.gh.common.utils.GHResponse;
import com.sun.jna.Memory;
import com.sun.jna.Pointer;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.List;

@Api("IPCAST广播")
@RestController
@RequestMapping("/ipcast")
@Slf4j
public class IPCastController {

    @GetMapping("session")
    public GHResponse getSession() {
        List<SessionDTO> sessionRefs = new ArrayList<>();
        int count = IPCastService.IP_CAST_SERVICE.IPCAST_GetSessionList(new Pointer(0), 0);
        if (count > 0) {
            Pointer pointer = new Memory(8 * count);
            int sessionList = IPCastService.IP_CAST_SERVICE.IPCAST_GetSessionList(pointer, count);
            int[] sessionIds = pointer.getIntArray(0, count);

            if (ArrayUtil.isNotEmpty(sessionIds)) {
                pointer.clear(8 * count);
                for (int id : sessionIds) {
                    IPCastService.Session.SessionRef sessionRef = new IPCastService.Session.SessionRef();
                    sessionRef.name = new byte[255];
                    IPCastService.IP_CAST_SERVICE.IPCAST_GetSessionStatus(id, sessionRef);
                    sessionRefs.add(SessionDTO.builder()
                            .status(sessionRef.status)
                            .sid(sessionRef.sid)
                            .grade(sessionRef.grade)
                            .type(sessionRef.type)
                            .iFile(sessionRef.iFile)
                            .name(StrUtil.str(sessionRef.name, Charset.defaultCharset()).trim())
                            .t_play(sessionRef.t_play)
                            .t_total(sessionRef.t_total)
                            .build());
                }
            }
        }

        return GHResponse.ok(sessionRefs);
    }

    @GetMapping("terminal")
    public GHResponse terminal() {
        int count = IPCastService.IP_CAST_SERVICE.IPCAST_GetTermList(new Pointer(0), 0);
        Pointer pointer = new Memory(8 * count);
        int sessionList = IPCastService.IP_CAST_SERVICE.IPCAST_GetTermList(pointer, count);
        int[] sessionIds = pointer.getIntArray(0, count);
        List<TerminalDTO> terminalRefs = new ArrayList<>();
        if (ArrayUtil.isNotEmpty(sessionIds)) {
            pointer.clear(8 * count);
            for (int id : sessionIds) {
                IPCastService.Terminal1.TerminalRef terminalRef = new IPCastService.Terminal1.TerminalRef();
                terminalRef.name = new byte[255];
                terminalRef.fwdaddr = new byte[40];
                terminalRef.ipaddr = new byte[40];
                IPCastService.IP_CAST_SERVICE.IPCAST_GetTermStatusEx(id, terminalRef);
                terminalRefs.add(TerminalDTO.builder()
                        .a_sid(terminalRef.a_sid)
                        .call_status(terminalRef.call_status)
                        .status(terminalRef.status)
                        .fwdaddr(StrUtil.str(terminalRef.fwdaddr, Charset.defaultCharset()).trim())
                        .ipaddr(StrUtil.str(terminalRef.ipaddr, Charset.defaultCharset()).trim())
                        .name(StrUtil.str(terminalRef.name, Charset.defaultCharset()).trim())
                        .work_status(terminalRef.work_status)
                        .vol(terminalRef.vol)
                        .tid(terminalRef.tid)
                        .build());
            }
        }
        return GHResponse.ok(terminalRefs);
    }


    @GetMapping("file")
    public GHResponse file() {
        List<FileDTO> fileAttrRefs = new ArrayList<>();
        int count = IPCastService.IP_CAST_SERVICE.IPCAST_FileGetListAll(new Pointer(0), 0);
        if (count > 0) {
            Pointer pointer = new Memory(8 * count);
            int sessionList = IPCastService.IP_CAST_SERVICE.IPCAST_FileGetListAll(pointer, count);
            int[] sessionIds = pointer.getIntArray(0, count);
            if (ArrayUtil.isNotEmpty(sessionIds)) {
                pointer.clear(8 * count);

                for (int id : sessionIds) {
                    IPCastService.FileAttr.FileAttrRef fileAttrRef = new IPCastService.FileAttr.FileAttrRef();
                    fileAttrRef.name = new byte[60];
                    IPCastService.IP_CAST_SERVICE.IPCAST_FileGetInfo(id, fileAttrRef);
                    log.info("$$"+ JSONUtil.toJsonStr(fileAttrRef.name));
                    fileAttrRefs.add(FileDTO.builder()
                            .name(StrUtil.trim(StrUtil.str(fileAttrRef.name, CharsetUtil.defaultCharset()), 0))
                            .fid(fileAttrRef.fid)
                            .attr(fileAttrRef.attr)
                            .build());
                }
            }
        }
        return GHResponse.ok(fileAttrRefs);
    }


    @PostMapping("play")
    public GHResponse play(@RequestBody PlayVo playVo) {
        IPCastService.PlayFile.PlayFileRef[] playFileRefs = new IPCastService.PlayFile.PlayFileRef[playVo.getFiles().size()];
        int index=0;
        for (FileVo fileVo: playVo.getFiles()) {
            IPCastService.PlayFile.PlayFileRef ref = new IPCastService.PlayFile.PlayFileRef();
            ref.fid = fileVo.getId();
            ref.fname = fileVo.getName();
            ref.fvol = fileVo.getVol();
            playFileRefs[index]=ref;
            index++;
        }
        Pointer pointer = new Memory(playVo.getTer().length * 8);
        pointer.write(0, playVo.getTer(), 0, playVo.getTer().length);
        int id = IPCastService.IP_CAST_SERVICE.IPCAST_FilePlayStart(playFileRefs, playFileRefs.length, pointer, playVo.getTer().length, playVo.getGrade(), playVo.getMode(), 0, 0);
        if (id > 0) {
            return GHResponse.ok();
        }
        return GHResponse.failed();
    }

    @GetMapping("stop")
    public GHResponse stop(int id) {
        boolean ctrl = IPCastService.IP_CAST_SERVICE.IPCAST_FilePlayCtrl(id, 1, 0);
        if (ctrl) {
            return GHResponse.ok();
        }
        return GHResponse.failed();
    }

    @PostMapping("ctrl")
    public GHResponse ctrl(@RequestBody CtrlVo ctrlVo) {
        boolean ctrl = IPCastService.IP_CAST_SERVICE.IPCAST_FilePlayCtrl(ctrlVo.getId(), ctrlVo.getMode(), 2);
        if (ctrl) {
            return GHResponse.ok();
        }
        return GHResponse.failed();
    }

    @GetMapping("vol")
    public GHResponse vol(int tid, int vol) {
        boolean ctrl = IPCastService.IP_CAST_SERVICE.IPCAST_SetTermVolume(tid, vol);
        if (ctrl) {
            return GHResponse.ok();
        }
        return GHResponse.failed();
    }

    @GetMapping("id")
    public GHResponse getTerminalById(int id) {
        int count = IPCastService.IP_CAST_SERVICE.IPCAST_GetSessionTermList(id, new Pointer(0), 0);
        if (count > 0) {
            Memory memory = new Memory(8 * count);
            int size = IPCastService.IP_CAST_SERVICE.IPCAST_GetSessionTermList(id, memory, count);
            long[] longArray = memory.getLongArray(0, count);
            return GHResponse.ok(longArray);
        }
        return GHResponse.ok(new long[0]);
    }


}
