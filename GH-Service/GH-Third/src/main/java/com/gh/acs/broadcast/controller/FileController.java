package com.gh.acs.broadcast.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.gh.acs.broadcast.model.dto.Category;
import com.gh.acs.broadcast.model.dto.ITCDir;
import com.gh.acs.broadcast.service.ITCService;
import com.gh.common.utils.GHResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

@RestController
@RequestMapping("/itc")
@Api(tags = "文件上传")
public class FileController {

   @Autowired
   private ITCService itcService;

    @PostMapping(value = "upload/{id}",consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @ApiOperation("文件上传")
    public GHResponse Upload(@RequestParam("file") MultipartFile file,@PathVariable("id") Integer id) throws IOException {
        if(null!=file&&!file.isEmpty())
        {
            String fileName = file.getOriginalFilename();
            String type = StrUtil.subAfter(fileName, ".", true);
            if(type.equalsIgnoreCase("mp3"))
            {
               boolean b= itcService.upload(id,file.getBytes(),fileName);
                if(b)
                {
                    return GHResponse.ok();
                }
            }
        }
        return GHResponse.failed();
    }

    @PostMapping("music")
    @ApiOperation("获取远程文件列表")
    public GHResponse<List<ITCDir>> getMusicList()
    {
        List<ITCDir> musics = itcService.getMusic();
        if(CollUtil.isNotEmpty(musics))
        {
            musics.forEach(music->{
                music.setName(music.getDirName());
                if(CollUtil.isNotEmpty(music.getMusics()));
                {
                    music.getMusics().forEach(m->{
                        m.setName(m.getAudioName());
                    });
                }
            });
        }
        return GHResponse.ok(musics);
    }

    @PostMapping("music/add")
    @ApiOperation("创建媒体库")
    public GHResponse add(Category category)
    {
        boolean b = itcService.addMusicCategory(category.getName(), category.getIs_public());
        if(b)
        {
            return GHResponse.ok();
        }
        return GHResponse.failed();
    }






}
