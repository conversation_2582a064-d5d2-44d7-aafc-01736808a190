package com.gh.acs.controller;


import cn.hutool.core.convert.Convert;
import cn.hutool.json.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.gh.acs.model.dto.Cmd;
import com.gh.acs.model.entity.LwLogin;
import com.gh.acs.service.JinHuInfoService;
import com.gh.acs.service.LwLoginService;
import com.gh.common.utils.GHResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RequestMapping("/info")
@RestController
@Api(tags = "金湖信息发布")
public class InfoController {

    @Autowired
    private JinHuInfoService apiService;

    @Autowired
    private LwLoginService lwLoginService;


    @GetMapping("terminal")
    @ApiOperation("查询终端信息")
    public GHResponse getTerminal( )
    {
        JSONObject list =apiService.getTerminal();
        return GHResponse.ok(list);
    }


    @PostMapping("cmd")
    @ApiOperation("终端操作")
    public GHResponse set(@RequestBody  Cmd cmd)
    {
        boolean send=apiService.cmd(cmd.getIds(),cmd.getNos(), Convert.toInt(cmd.getCmd()));
        if(send)
        {
            return GHResponse.ok();

        }
        return GHResponse.failed();
    }

    @PostMapping("vol")
    @ApiOperation("终端设置音量")
    public GHResponse setVol(@RequestBody  Cmd cmd)
    {
        boolean send=apiService.setVolume(cmd.getIds(),cmd.getVol());
        if(send)
        {
            return GHResponse.ok();

        }
        return GHResponse.failed();
    }




    @PostMapping("tt")
    @ApiOperation("终端设置音量")
    @DS("patrol")
    public GHResponse tt(@RequestBody  Cmd cmd)
    {
        List<LwLogin> list = lwLoginService.list();
        return GHResponse.failed();
    }


}
