package com.gh.acs.controller;


import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.gh.acs.model.dto.ZKRespData;
import com.gh.acs.model.dto.excel.*;
import com.gh.acs.service.ZKService;
import com.gh.common.utils.GHResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Api("中控接口")
@RestController
@RequestMapping("/zk")
public class ZkController {

    @Autowired
    private ZKService service;

    @Autowired
    private HttpServletResponse response;



    @GetMapping("door")
    @ApiOperation("门禁")
    public GHResponse<Object> getDoor(String keyword,Integer page,Integer size)
    {
        JSONObject object = service.getDoor(keyword,page,size);
        return GHResponse.ok(object.get("rows"), Convert.toLong(object.get("total_count"),0l));
    }

    @GetMapping("door-down")
    @ApiOperation("门禁下载")
    public void getDoorDown(String keyword,Integer page,Integer size) throws IOException {
        JSONObject object = service.getDoor(keyword,page,size);
        List<ZKRespData> rows = JSONUtil.toList(object.get("rows").toString(), ZKRespData.class);
        List<List<Object>> lists = rows.stream().map(zkRespData -> zkRespData.getData()).collect(Collectors.toList());
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        String name="门禁信息";
        String fileName = URLUtil.encode(name, CharsetUtil.CHARSET_UTF_8).replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
        EasyExcel.write(response.getOutputStream(), Door.class).sheet(name).doWrite(lists);
    }

    @GetMapping("device")
    @ApiOperation("门禁道闸")
    public GHResponse<Object> getDoorDevice(String keyword,Integer page,Integer size)
    {
        JSONObject object = service.getDoorDevice(keyword,page,size);
        return GHResponse.ok(object.get("rows"), Convert.toLong(object.get("total_count"),0l));
    }

    @GetMapping("device-down")
    @ApiOperation("导出门禁道闸")
    public void getDoorDeviceDown(String keyword) throws IOException {
        JSONObject object = service.getDoorDevice(keyword,1,10000);
        List<ZKRespData> rows = JSONUtil.toList(object.get("rows").toString(), ZKRespData.class);
        List<List<Object>> lists = rows.stream().map(zkRespData -> zkRespData.getData()).collect(Collectors.toList());
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        String name="门禁道闸";
        String fileName = URLUtil.encode(name, CharsetUtil.CHARSET_UTF_8).replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
        EasyExcel.write(response.getOutputStream(), DoorDevice.class).sheet(name).doWrite(lists);
    }

    @GetMapping("doorRecord")
    @ApiOperation("门禁进出记录")
    public GHResponse<Object> getDoorRecord(String bt,String et,Integer page,Integer size,String keyword)
    {
        JSONObject object = service.getDoorRecord(bt,et,page,size,keyword);
        return GHResponse.ok(object.get("rows"), Convert.toLong(object.get("total_count"),0l));
    }

    @GetMapping("doorRecord-down")
    @ApiOperation("导出门禁进出记录")
    public void getDoorRecordDown(String bt,String et,String keyword) throws IOException {
        JSONObject object = service.getDoorRecord(bt,et,1,10000,keyword);
        List<ZKRespData> rows = JSONUtil.toList(object.get("rows").toString(), ZKRespData.class);
        List<List<Object>> lists = rows.stream().map(zkRespData -> zkRespData.getData()).collect(Collectors.toList());
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        String name="门禁记录";
        String fileName = URLUtil.encode(name, CharsetUtil.CHARSET_UTF_8).replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
        EasyExcel.write(response.getOutputStream(), DoorRecord.class).sheet(name).doWrite(lists);
    }



    @GetMapping("parkRecord")
    @ApiOperation("停车进记录")
    public GHResponse<Object> getParkRecord(String username,String bt,String et,Integer page,Integer size,String carNumber)
    {
        JSONObject object = service.getParkRecord(bt,et,carNumber,username,page,size);
        return GHResponse.ok(object.get("rows"), Convert.toLong(object.get("total_count"),0l));
    }

    @GetMapping("parkRecord-down")
    @ApiOperation("导出停车进记录")
    public void getParkRecordDown(String bt,String et,String carNumber,String username) throws IOException {
        JSONObject object = service.getParkRecord(bt,et,carNumber,username,1,10000);
        List<ZKRespData> rows = JSONUtil.toList(object.get("rows").toString(), ZKRespData.class);
        List<List<Object>> lists = rows.stream().map(zkRespData -> zkRespData.getData()).collect(Collectors.toList());
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        String name="停车记录";
        String fileName = URLUtil.encode(name, CharsetUtil.CHARSET_UTF_8).replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
        EasyExcel.write(response.getOutputStream(), ParkRecord.class).sheet(name).doWrite(lists);
    }


    @GetMapping("visRecord")
    @ApiOperation("访客历史记录")
    public GHResponse<Object> getVisRecord(String bt,String et,Integer page,Integer size,String pin,String visitedLikeName)
    {
        JSONObject object = service.getVisRecord(bt,et,pin,page,size,visitedLikeName);
        return GHResponse.ok(object.get("rows"), Convert.toLong(object.get("total_count"),0l));
    }

    @GetMapping("leave")
    @ApiOperation("请假详情")
    public GHResponse<Object> leave(String bt,String et,Integer page,Integer size,String pin,String likeName,String deptName)
    {
        JSONObject object = service.leave(pin,likeName,deptName,bt,et,page,size);
        return GHResponse.ok(object.get("rows"), Convert.toLong(object.get("total_count"),0l));
    }

    @GetMapping("leave-down")
    @ApiOperation("请假详情导出")
    public void leaveDown(String bt,String et,String pin,String likeName,String deptName) throws IOException {
        JSONObject object = service.leave(pin,likeName,deptName,bt,et,null,null);
        List<ZKRespData> rows = JSONUtil.toList(object.get("rows").toString(), ZKRespData.class);
        List<List<Object>> lists = rows.stream().map(zkRespData -> zkRespData.getData()).collect(Collectors.toList());
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        String name="请假详情";
        String fileName = URLUtil.encode(name, CharsetUtil.CHARSET_UTF_8).replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
        EasyExcel.write(response.getOutputStream(), Leave.class).sheet(name).doWrite(lists);
    }

    @GetMapping("visRecord-down")
    @ApiOperation("导出访客历史记录")
    public void getVisRecordDown(String bt,String et,String pin,String visitedLikeName) throws IOException {
        JSONObject object = service.getVisRecord(bt,et,pin,1,10000,visitedLikeName);
        List<ZKRespData> rows = JSONUtil.toList(object.get("rows").toString(), ZKRespData.class);
        List<List<Object>> lists = rows.stream().map(zkRespData -> zkRespData.getData()).collect(Collectors.toList());
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        String name="访客记录";
        String fileName = URLUtil.encode(name, CharsetUtil.CHARSET_UTF_8).replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
        EasyExcel.write(response.getOutputStream(), VistRecord.class).sheet(name).doWrite(lists);
    }




    @GetMapping("open")
    @ApiOperation("停车场开闸")
    public GHResponse openChannel(@RequestParam String id)
    {
        Boolean success=service.openChannel(id);
        if(success)
        {
            return GHResponse.ok();
        }
        return GHResponse.failed();
    }


    @GetMapping("month")
    @ApiOperation("考勤月明细")
    public GHResponse<Object> getMonthData(String bt,String et,Integer page,Integer size,String pin,String deptName,String likeName)
    {
        JSONObject object = service.getMonthRecord(pin,bt,et,deptName,page,size,likeName);
        return GHResponse.ok(object.get("rows"), Convert.toLong(object.get("total_count"),0l));
    }

    @GetMapping("month-down")
    @ApiOperation("导出考勤月明细")
    public void getMonthDataDown(String bt,String et,String pin,String deptName,String likeName) throws IOException {
//        List<List<String>> head = head(bt, et);
        JSONObject object = service.getMonthRecord(pin,bt,et,deptName,1,10000,likeName);
        List<ZKRespData> rows = JSONUtil.toList(object.get("rows").toString(), ZKRespData.class);
        List<List<Object>> lists = rows.stream().map(zkRespData -> zkRespData.getData()).collect(Collectors.toList());
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        String name="考勤记录";
        String fileName = URLUtil.encode(name, CharsetUtil.CHARSET_UTF_8).replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
        EasyExcel.write(response.getOutputStream())
                .head(head(bt,et))
                .sheet(name).doWrite(lists);
    }

    @GetMapping("channel")
    @ApiOperation("查询所有停车场通道")
    public GHResponse<Object> getChannel()
    {
        JSONObject object = service.getChannel();
        return GHResponse.ok(object.get("rows"));
    }

    @GetMapping("parkSpace")
    @ApiOperation("车辆利用情况")
    public GHResponse<Object> getParkSpace()
    {
        JSONObject object = service.getParkStatistics();
        return GHResponse.ok(object.get("data"));
    }

    @GetMapping("doorEvent")
    @ApiOperation("门禁事件")
    public GHResponse<Object> getEvent()
    {
        JSONObject object = service.getAccEvent();
        return GHResponse.ok(object.get("data"));
    }

    @GetMapping("visReserva")
    @ApiOperation("访客统计")
    public GHResponse<Object> getVisReservationData()
    {
        JSONObject object = service.getVisReservationData();
        return GHResponse.ok(object.get("data"));
    }


    @GetMapping("atttransaction")
    @ApiOperation("考勤原始数据")
    public GHResponse<Object> atttransaction(String beginDate, String endDate,String likeName ,String areaName,String deptName,Integer page,Integer size)
    {
        JSONObject object = service.atttransaction(beginDate,endDate,likeName,areaName,deptName,page,size);
        return GHResponse.ok(object.get("rows"), Convert.toLong(object.get("total_count"),0l));
    }

    @GetMapping("atttransaction-down")
    @ApiOperation("考勤原始数据")
    public void atttransactionDown(String beginDate, String endDate,String likeName ,String areaName,String deptName) throws IOException {
        JSONObject object = service.atttransaction(beginDate,endDate,likeName,areaName,deptName,1,10000);
        List<ZKRespData> rows = JSONUtil.toList(object.get("rows").toString(), ZKRespData.class);
        List<List<Object>> lists = rows.stream().map(zkRespData -> zkRespData.getData()).collect(Collectors.toList());
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        String name="考勤原始数据";
        String fileName = URLUtil.encode(name, CharsetUtil.CHARSET_UTF_8).replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
        EasyExcel.write(response.getOutputStream(), AttRecord.class).sheet(name).doWrite(lists);
    }





    @GetMapping("parkrecordin")
    @ApiOperation("场内停车记录")
    public GHResponse<Object> parkrecordin(String timeBegin,String timeEnd,String carNumber,String userName,Integer page,Integer size )
    {
        JSONObject object = service.parkrecordin(timeBegin,timeEnd,carNumber,userName,page,size);
        return GHResponse.ok(object.get("rows"), Convert.toLong(object.get("total_count"),0l));
    }

    @GetMapping("parkrecordin-down")
    @ApiOperation("场内停车记录")
    public void parkrecordindown(String timeBegin,String timeEnd,String carNumber,String userName ) throws IOException {
        JSONObject object = service.parkrecordin(timeBegin,timeEnd,carNumber,userName,1,10000);
        List<ZKRespData> rows = JSONUtil.toList(object.get("rows").toString(), ZKRespData.class);
        List<List<Object>> lists = rows.stream().map(zkRespData -> zkRespData.getData()).collect(Collectors.toList());
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        String name="在场车辆";
        String fileName = URLUtil.encode(name, CharsetUtil.CHARSET_UTF_8).replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
        EasyExcel.write(response.getOutputStream(), ParkRecordIn.class).sheet(name).doWrite(lists);
    }



    @GetMapping("parkrecordout")
    @ApiOperation("出场停车记录")
    public GHResponse<Object> parkrecordout(String timeBegin,String timeEnd,String carNumber,String userName,Integer page,Integer size)
    {
        JSONObject object = service.parkrecordout(timeBegin,timeEnd,carNumber,userName,page,size);
        return GHResponse.ok(object.get("rows"), Convert.toLong(object.get("total_count"),0l));
    }

    @GetMapping("parkrecordout-down")
    @ApiOperation("出场停车记录")
    public void parkrecordoutdown(String timeBegin,String timeEnd,String carNumber,String userName) throws IOException {
        JSONObject object = service.parkrecordout(timeBegin,timeEnd,carNumber,userName,1,10000);
        List<ZKRespData> rows = JSONUtil.toList(object.get("rows").toString(), ZKRespData.class);
        List<List<Object>> lists = rows.stream().map(zkRespData -> zkRespData.getData()).collect(Collectors.toList());
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        String name="出场记录";
        String fileName = URLUtil.encode(name, CharsetUtil.CHARSET_UTF_8).replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
        EasyExcel.write(response.getOutputStream(), ParkRecordOut.class).sheet(name).doWrite(lists);
    }

    private List<List<String>> head(String bt,String et) {
        List<List<String>> list = new ArrayList<List<String>>();
        list.add(Arrays.asList("编号"));
        list.add(Arrays.asList("姓名"));
        list.add(Arrays.asList("部门"));
        if(StrUtil.isNotEmpty(bt)&&StrUtil.isNotEmpty(et))
        {
            while (DateUtil.parse(bt).isBeforeOrEquals(DateUtil.parse(et)))
            {
                list.add(Arrays.asList(DateUtil.parse(bt).toString("yyyy-MM-dd")));
                bt= DateUtil.offset(DateUtil.parse(bt), DateField.DAY_OF_MONTH,1).toString("yyyy-MM-dd 00:00:00");
            }
        }
        list.add(Arrays.asList("应该"));
        list.add(Arrays.asList("实际"));
        list.add(Arrays.asList("有效"));
        return list;
    }




}
