package com.gh.acs.controller;


import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.gh.acs.model.dto.*;
import com.gh.acs.service.QueueService;
import com.gh.acs.service.SHMeetingService;
import com.gh.acs.service.SHMsgService;
import com.gh.common.utils.GHResponse;
import com.gh.common.utils.PageResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Api(tags = "北京视瀚接口")
@RestController
//@RequestMapping("/shihan")
public class HanShiController {

    @Autowired
    private SHMsgService service;

    @Autowired
    private QueueService queueService;

    @Autowired
    private SHMeetingService shMeetingService;


    @GetMapping("msg-device")
    @ApiOperation("信息发布设备")
    public GHResponse msgDevice(String grouplist, Integer page, Integer size) {
        com.alibaba.fastjson.JSONArray data = service.getDeviceList(grouplist, page, size);

        return GHResponse.ok(data);
    }

    @GetMapping("mission-list")
    @ApiOperation("信息发布设备")
    public GHResponse missionlist() {
        JSONArray data = service.missionList();

        return GHResponse.ok(data);
    }


    @GetMapping("open")
    @ApiOperation("信息发布开屏")
    public GHResponse open(String id) {
        boolean data = service.openScreen(id);
        if (data)
            return GHResponse.ok(data);
        else
            return GHResponse.failed();
    }

    @GetMapping("close")
    @ApiOperation("信息发布关屏")
    public GHResponse close(String id) {
        boolean data = service.closeScreen(id);
        if (data)
            return GHResponse.ok(data);
        else
            return GHResponse.failed();
    }


    @GetMapping("hj-device")
    @ApiOperation("呼叫设备监控")
    @DS("hj")
    public GHResponse device(Integer page,Integer size,String keyword,Integer dept) {
        if(StrUtil.isNotEmpty(keyword)){
            keyword="%"+keyword+"%";
        }
        PageResult<List<HjDeviceDTO>> device = service.getDevice(keyword, dept, page, size);
        return GHResponse.ok(device.getData(),device.getTotal());
    }


    @GetMapping("doctor")
    @ApiOperation("医生管理")
    @DS("hj")
    public GHResponse doctor(Integer page,Integer size,String keyword,Integer dept) {
        if(StrUtil.isNotEmpty(keyword)){
            keyword="%"+keyword+"%";
        }
        PageResult<List<DoctorDTO>> doctor = service.getDoctor(keyword, dept, page, size);
        return  GHResponse.ok(doctor.getData(),doctor.getTotal());
    }



    @GetMapping("nurse")
    @ApiOperation("护士管理")
    @DS("hj")
    public GHResponse nurse(Integer page,Integer size,String keyword,Integer dept) {
        if(StrUtil.isNotEmpty(keyword)){
            keyword="%"+keyword+"%";
        }
        PageResult<List<NurseDTO>> nurse = service.getNurse(keyword, dept, page, size);
        return GHResponse.ok(nurse.getData(),nurse.getTotal());
    }



    @GetMapping("patient")
    @ApiOperation("患者管理")
    @DS("hj")
    public GHResponse patient(Integer page,Integer size,String keyword,Integer dept) {
        if(StrUtil.isNotEmpty(keyword)){
            keyword="%"+keyword+"%";
        }
        PageResult<List<PatientDTO>> patient = service.getPatient(keyword, dept, page, size);
        return GHResponse.ok(patient.getData(),patient.getTotal());
    }


    @GetMapping("dept")
    @ApiOperation("部门查询")
    @DS("hj")
    public GHResponse dept() {
        List<DeptDTO> dept = service.getDept();
        return GHResponse.ok(dept);
    }

    @GetMapping("window-real")
    @ApiOperation("窗口实时监控")
    @DS("pd")
    public GHResponse windowReal(String keyword) {
        if(StrUtil.isNotEmpty(keyword)){
            keyword="%"+keyword+"%";
        }
        List<QueueDTO> queue = queueService.getQueue(keyword);
        return GHResponse.ok(queue);
    }

    @GetMapping("bus-real")
    @ApiOperation("业务实时监控")
    @DS("pd")
    public GHResponse busReal(String keyword) {
        if(StrUtil.isNotEmpty(keyword)){
            keyword="%"+keyword+"%";
        }
        List<QueueDTO> queue = queueService.getBusQueue(keyword);
        return GHResponse.ok(queue);
    }

    @GetMapping("dept-real")
    @ApiOperation("业务实时监控")
    @DS("pd")
    public GHResponse deptReal(String keyword) {
        if(StrUtil.isNotEmpty(keyword)){
            keyword="%"+keyword+"%";
        }
        List<QueueDTO> queue = queueService.getDeptQueue(keyword);
        return GHResponse.ok(queue);
    }

    @GetMapping("pd-window")
    @ApiOperation("排队叫号窗口")
    @DS("pd")
    public GHResponse windowList(String keyword) {
        if(StrUtil.isNotEmpty(keyword)){
            keyword="%"+keyword+"%";
        }
        List<QueueWindowDTO> queue = queueService.getWindow(keyword);
        return GHResponse.ok(queue);
    }

    @GetMapping("pd-dept")
    @ApiOperation("排队叫号窗口")
    @DS("pd")
    public GHResponse queueDept(String keyword) {
        List<QueueDeptDTO> queue = queueService.getQueueDept();
        return GHResponse.ok(queue);
    }


    @GetMapping("queue-detail")
    @ApiOperation("窗口详情")
    @DS("pd")
    public GHResponse queueDept(String keyword,Integer deptId,Integer winId,Integer busId) {
        List<QueueRecordDTO> queueDetail = queueService.getQueueDetail(keyword, deptId, winId, busId);
        return GHResponse.ok(queueDetail);
    }



    @GetMapping("queue-record")
    @ApiOperation("排队叫号记录")
    @DS("pd")
    public GHResponse queueRecord(String keyword,String bt,String et,Integer page,Integer size) {
        if(StrUtil.isNotEmpty(keyword)){
            keyword="%"+keyword+"%";
        }
        PageResult<List<QueueRecordDTO>> record = queueService.getQueueRecord(keyword, bt, et, page, size);
        return GHResponse.ok(record.getData(),record.getTotal());
    }


    @GetMapping("meeting-list")
    @ApiOperation("会议列表")
    public GHResponse meetingList(Integer roomId,String speakerLike,String createdBeginTime,String createdEndTime) {
        JSONArray data = shMeetingService.getMeetingList(roomId, speakerLike, createdBeginTime, createdEndTime);
        return GHResponse.ok(data);
    }

    @GetMapping("room-list")
    @ApiOperation("会议室列表")
    public GHResponse roomList() {
        JSONArray data = shMeetingService.getRoomList();
        return GHResponse.ok(data);
    }

    @GetMapping("doorplate-list")
    @ApiOperation("门牌列表")
    public GHResponse doorPlateList() {
        JSONArray data = shMeetingService.getDoorPlateList();
        return GHResponse.ok(data);
    }






    @GetMapping("rsa")
    @ApiOperation("rsa")
    public GHResponse rsa(@RequestParam(required = false) String data) throws Exception {
        //加密前格式：shine|当前时间戳|login_id
        String resp = service.getRsaCode("shine|"+System.currentTimeMillis()+"|admin");
        return GHResponse.ok(resp);
    }

    @GetMapping("device-list")
    @ApiOperation("设备列表")
    public GHResponse deviceList() {
        cn.hutool.json.JSONArray data = service.getDeviceListV1();
        return GHResponse.ok(data);
    }

    @PostMapping("device-control")
    @ApiOperation("设备控制")
    public GHResponse deviceControl(@RequestBody JSONObject data) {
        boolean resp = service.deviceControl(data);
        return GHResponse.ok(resp);
    }

    @GetMapping("play-list")
    @ApiOperation("播放列表")
    public GHResponse playList() {
        cn.hutool.json.JSONArray data = service.getPlayList();
        return GHResponse.ok(data);
    }

    @GetMapping("template-list")

    @ApiOperation("模板列表")
    public GHResponse templateList() {
        cn.hutool.json.JSONArray data = service.templateList();
        return GHResponse.ok(data);
    }

    @GetMapping("element-list")
    @ApiOperation("元素列表")
    public GHResponse elementList(Integer type) {
        cn.hutool.json.JSONArray data = service.elementList(type);
        return GHResponse.ok(data);
    }


}
