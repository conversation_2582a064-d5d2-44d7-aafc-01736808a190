package com.gh.acs.controller;


import com.alibaba.fastjson.JSONObject;
import com.gh.acs.model.entity.DaShiDoor;
import com.gh.acs.service.DaShiDoorService;
import com.gh.common.utils.GHResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.stream.Collectors;

@Api("深圳门禁")
@RestController
@RequestMapping("/door")
@Slf4j
public class DaShiController {



    @Autowired
    private DaShiDoorService service;



    @PostMapping("real")
    @ApiOperation("实时数据")
    public GHResponse getDevice(@RequestBody JSONObject param)  {

        if(null!=param)
        {
            try {
                log.info("实时数据:{}",param.toJSONString());
                List<DaShiDoor> data = param.getJSONArray("data").toJavaList(DaShiDoor.class);
                List<DaShiDoor> doors = data.stream().filter(daShiDoor -> null != daShiDoor).collect(Collectors.toList());
                service.saveOrUpdateBatch(doors);
            }catch (Exception e) {
                log.error("保存失败", e);
                e.printStackTrace();
            }

        }

        return GHResponse.ok();
    }

    @GetMapping("status")
    @ApiOperation("实时数据")
    public GHResponse status()  {

        List<DaShiDoor> list = service.list();
        return GHResponse.ok(list);
    }







}
