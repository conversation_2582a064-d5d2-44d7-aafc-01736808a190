package com.gh.acs.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Description:
 * User: zhangkeguang
 * Date: 2024-01-29-14:41
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("gcxyzlbiao")
public class ziliao {

    @TableField(value = "bh")
    private String bh;

}
