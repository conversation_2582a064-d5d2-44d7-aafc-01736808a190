package com.gh.acs.controller;


import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.gh.acs.service.GtvService;
import com.gh.common.utils.GHResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Api("淮北信息发布")
@RestController
@RequestMapping("/gtv")
public class GtvController {

    @Autowired
    private GtvService service;




    @PostMapping("device")
    @ApiOperation("获取设备")
    public GHResponse getDevice(@RequestBody JSONObject param)  {
        String data = service.getData("/cdms/client/qryClientList", JSONUtil.toJsonStr(param));
        return GHResponse.ok(data);
    }



    @PostMapping("materList")
    @ApiOperation("获取素材列表")
    public GHResponse materList(@RequestBody JSONObject param)  {
        String data = service.getData("/cdms/material/qryMaterialList", JSONUtil.toJsonStr(param));
        return GHResponse.ok(data);
    }

    @PostMapping("playList")
    @ApiOperation("获取播放列表")
    public GHResponse qryPlaylist(@RequestBody JSONObject param)  {
        String data = service.getData("/cdms/program/qryPlaylist", JSONUtil.toJsonStr(param));
        return GHResponse.ok(data);
    }


    @PostMapping("cmd")
    @ApiOperation("发送指令")
    public GHResponse sendCmd(@RequestBody JSONObject param)  {
        String data = service.sendCmd("/cdms/client/sendCmd", JSONUtil.toJsonStr(param));
        return GHResponse.ok(data);
    }




}
