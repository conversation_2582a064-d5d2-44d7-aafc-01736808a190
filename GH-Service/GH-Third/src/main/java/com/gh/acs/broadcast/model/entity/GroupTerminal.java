package com.gh.acs.broadcast.model.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("GHITC_GroupTerminal")
@Builder
public class GroupTerminal {
    @TableId(type = IdType.AUTO)
    private Integer id;
    private Integer groupId;
    private Integer tid;

}
