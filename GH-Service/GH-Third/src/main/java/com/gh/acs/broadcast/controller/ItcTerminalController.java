package com.gh.acs.broadcast.controller;

import cn.hutool.json.JSONObject;
import com.gh.acs.broadcast.model.entity.ITCTask;
import com.gh.acs.broadcast.model.entity.ITCTerminal;
import com.gh.acs.broadcast.model.vo.VolVO;
import com.gh.acs.broadcast.service.ITCService;
import com.gh.common.utils.GHResponse;
import com.gh.log.annotation.OperLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@RequestMapping("/itc/terminal")
@RestController
@Api(tags = "终端管理")
public class ItcTerminalController {

    @Autowired
    private ITCService itcService;




    @GetMapping
    @ApiOperation("查询终端信息")
    public GHResponse<List<ITCTerminal>> Selects(Integer endpointID)
    {
        List<ITCTerminal> list =itcService.getTerminal();
        if(null!=endpointID)
        {
            list=list.stream().filter(it->it.getEndpointID()==endpointID).collect(Collectors.toList());
        }
        return GHResponse.ok(list);
    }

    @GetMapping("statistic")
    @ApiOperation("终端种类统计")
    public GHResponse statistic( )
    {
        Map<Integer, Long> map = itcService.getTerminal()
                .stream()
                .collect(Collectors.groupingBy(ITCTerminal::getEndpointType, Collectors.counting()));
        return GHResponse.ok(map);
    }

    @GetMapping("status")
    @ApiOperation("终端状态统计")
    public GHResponse status( )
    {
        Map<Integer, Map<Integer, Long>> map = itcService.getTerminal()
                .stream()
                .collect(Collectors.groupingBy(ITCTerminal::getEndpointType,
                        Collectors.groupingBy(ITCTerminal::getStatus, Collectors.counting())));
        return GHResponse.ok(map);
    }



    @GetMapping("record")
    @ApiOperation("终端日志")
    public GHResponse record(String keyword,Integer size,Integer page ,String bt,String et)
    {
        JSONObject terminalLog = itcService.getTerminalLog(keyword, page, size, bt, et);
        return GHResponse.ok(terminalLog);
    }


    @GetMapping("task")
    @ApiOperation("终端日志")
    public GHResponse task(String keyword,Integer size,Integer page ,String bt,String et)
    {
        JSONObject taskLog = itcService.getTaskLog(keyword, page, size, bt, et);
        return GHResponse.ok(taskLog);
    }



    @PostMapping("vol")
    @ApiOperation("设置终端音量")
    @OperLog(category = "ITC终端管理",description = "设置终端音量")
    public GHResponse<List<ITCTask>> setVol(@RequestBody VolVO vol)
    {
        Boolean b=  itcService.setTerminalVolume(vol.getVol(),vol.getTid());
        if(b)
        {
            return GHResponse.ok();
        }
        return GHResponse.failed();
    }



}
