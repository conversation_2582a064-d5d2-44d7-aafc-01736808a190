package com.gh.acs.controller;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gh.acs.model.entity.AIAlarm;
import com.gh.acs.service.AIAlarmService;
import com.gh.acs.service.AIService;
import com.gh.common.utils.GHResponse;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Description:
 * User: zhangkeguang
 * Date: 2024-07-19-09:00
 */
@RestController
@Api(tags = "九江实验室ai预警推送")
@Slf4j
@RequestMapping("/ai")
public class AIAlarmApiController {


    @Autowired
    private AIAlarmService service;

    @Autowired
    private AIService aiService;


    @GetMapping("category")
    public GHResponse category() {

        Map<String, Long> collect = service.lambdaQuery().list().stream().collect(Collectors.groupingBy(AIAlarm::getTypes, Collectors.counting()));
        return GHResponse.ok(collect);

    }

    @GetMapping("list")
    public GHResponse list(String vname,
                           @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")Date bt,
                           @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")Date et, Integer page, Integer size) {


        Page<AIAlarm> page1 = service.lambdaQuery()
                .like(StrUtil.isNotEmpty(vname),AIAlarm::getVname, vname)
                .between(bt!=null,AIAlarm::getLogTime, bt, et)
                .orderByDesc(AIAlarm::getLogTime)
                .page(new Page<>(page, size));
        return GHResponse.ok(page1.getRecords(), page1.getTotal());


    }

    //本周报警统计,每天数量统计
    @GetMapping("week")
    public GHResponse week() {
        //本周日期
        List  list = new ArrayList<>();
        for (int i = 1; i <= 7; i++) {
            list.add(0);
        }
        List<AIAlarm> alarms = service.lambdaQuery()
                .between(AIAlarm::getLogTime, DateUtil.beginOfWeek(new Date(), true), DateUtil.endOfWeek(new Date(), true))
                .list();
         for (AIAlarm alarm : alarms) {
             //1 周日 2 周一 3 周二 4 周三 5 周四 6 周五 7 周六
           Integer week= DateUtil.dayOfWeek(alarm.getLogTime());
           if(week==1){
               //放在最后
                list.set(6, (Integer) list.get(6)+1);
           }
              else{
                list.set(week-2, (Integer) list.get(week-2)+1);
              }
         }
        return GHResponse.ok(list);
    }



    @GetMapping("event/fb")
    public GHResponse event() {
        return GHResponse.ok(aiService.getEventFB());
    }

    @GetMapping("event/trend")
    public GHResponse eventTrend() {
        return GHResponse.ok(aiService.getEventQS());
    }

    @GetMapping("event/list")
    public GHResponse eventList(String date, Integer page, Integer size, String type, Long fromdate, Long todate) {
        return GHResponse.ok(aiService.getEventList(date, page, size, type, fromdate, todate));
    }



}
