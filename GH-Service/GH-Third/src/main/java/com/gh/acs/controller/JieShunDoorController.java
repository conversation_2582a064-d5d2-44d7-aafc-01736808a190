package com.gh.acs.controller;


import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.gh.acs.service.JieShunService;
import com.gh.common.utils.GHResponse;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@Api("门禁管理")
@RestController
@RequestMapping("/jieshun")
public class JieShunDoorController {

    @Autowired
    private JieShunService service;

    @GetMapping("parkin")
    public GHResponse parkIn(String bt,String et,Integer page,Integer size)
    {
        JSONObject jsonObject = service.parkIn(bt, et, page, size);
        return GHResponse.ok(JSONUtil.toJsonStr(jsonObject));
    }

    @GetMapping("inRecord")
    public GHResponse inRecord(String bt,String et,Integer page,Integer size)
    {
        JSONObject jsonObject = service.inRecord(bt, et, page, size);
        return GHResponse.ok(JSONUtil.toJsonStr(jsonObject));
    }

    @GetMapping("outRecord")
    public GHResponse outRecord(String bt,String et,Integer page,Integer size)
    {
        JSONObject jsonObject = service.outRecord(bt, et, page, size);
        return GHResponse.ok(JSONUtil.toJsonStr(jsonObject));
    }


    @GetMapping("doorRecord")
    public GHResponse doorRecord(String bt,String et,Integer page,Integer size)
    {
        JSONObject jsonObject = service.doorRecord(bt, et, page, size);
        return GHResponse.ok(JSONUtil.toJsonStr(jsonObject));
    }


    @GetMapping("doorNormalRecord")
    public GHResponse doorNormalRecord(String bt,String et,Integer page,Integer size)
    {
        JSONObject jsonObject = service.doorNormalRecord(bt, et, page, size);
        return GHResponse.ok(JSONUtil.toJsonStr(jsonObject));
    }

    @GetMapping("doorAlarmRecord")
    public GHResponse doorAlarmRecord(String bt,String et,Integer page,Integer size)
    {
        JSONObject jsonObject = service.doorAlarmRecord(bt, et, page, size);
        return GHResponse.ok(JSONUtil.toJsonStr(jsonObject));
    }

    @GetMapping("doorUsers")
    public GHResponse doorUsers(String bt,String et,Integer page,Integer size)
    {
        JSONObject jsonObject = service.doorUsers(bt, et, page, size);
        return GHResponse.ok(JSONUtil.toJsonStr(jsonObject));
    }

    @GetMapping("doorStatus")
    public GHResponse doorStatus()
    {
        JSONArray jsonObject = service.doorStatus();
        return GHResponse.ok(jsonObject);
    }

    @GetMapping("info")
    public GHResponse getSummaryInfo()
    {
        JSONObject jsonObject = service.getSummaryInfo();
        return GHResponse.ok(JSONUtil.toJsonStr(jsonObject));
    }

    @GetMapping("deviceStatus")
    public GHResponse getDeviceStatus()
    {
        JSONObject jsonObject = service.getDeviceStatus();
        return GHResponse.ok(jsonObject);
    }

    @GetMapping("fkEvents")
    public GHResponse fkEvents(String bt,String et,Integer page,Integer size)
    {
        JSONObject jsonObject = service.fkEvents(bt, et, page, size);
        return GHResponse.ok(JSONUtil.toJsonStr(jsonObject));
    }

    @GetMapping("dept")
    public GHResponse dept()
    {
        String jsonObject = service.getDepts();
        return GHResponse.ok(jsonObject);
    }


    @PostMapping("addperson")
    public GHResponse addperson(@RequestBody JSONObject object)
    {
        boolean add = service.addPerson(object);
        if(add)
        {
            return GHResponse.ok();
        }
        return GHResponse.failed();
    }

    @PostMapping("auth")
    public GHResponse auth(@RequestBody JSONObject object)
    {
        boolean add = service.authPark(object);
        if(add)
        {
            return GHResponse.ok();
        }
        return GHResponse.failed();
    }

    @GetMapping("person")
    public GHResponse person(String keyword,Integer page,Integer size)
    {
        JSONObject person = service.getAllPerson(keyword, page, size);
        return GHResponse.ok(JSONUtil.toJsonStr(person));
    }


}
