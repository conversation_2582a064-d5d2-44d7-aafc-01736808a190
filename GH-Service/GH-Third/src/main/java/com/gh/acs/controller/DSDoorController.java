package com.gh.acs.controller;


import cn.hutool.json.JSONObject;
import com.gh.acs.service.DaShiDoorService;
import com.gh.common.utils.GHResponse;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@Api("达实门禁管理")
@RestController
@RequestMapping("/dashi")
public class DSDoorController {

    @Autowired
    private DaShiDoorService service;


    @PostMapping("record")
    public GHResponse<String> record(@RequestBody JSONObject params) throws Exception {
        String record = service.getRecord(params);
        return GHResponse.ok(record);
    }

    @PostMapping("door")
    public GHResponse<String> door(@RequestBody JSONObject params) throws Exception {
        String record = service.getDoor(params);
        return GHResponse.ok(record);
    }



    @PostMapping("event")
    public GHResponse<String> event(@RequestBody JSONObject params) throws Exception {
        String record = service.getEvent(params);
        return GHResponse.ok(record);
    }



}
