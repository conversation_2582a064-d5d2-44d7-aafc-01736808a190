package com.gh.acs.controller;


import com.alibaba.fastjson.JSONObject;
import com.gh.acs.model.vo.HsdaVO;
import com.gh.acs.model.vo.TsdaVO;
import com.gh.acs.service.EnergyService;
import com.gh.common.utils.GHResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@Api("档案馆创月能耗接口")
@RestController
@RequestMapping("/chuangyue")
@Slf4j
public class ChuangYueController {

    @Autowired
    private EnergyService service;

    @GetMapping("area")
    @ApiOperation("获取区域列表")
    public GHResponse getAreaList() {
        return GHResponse.ok(service.getAreaList());
    }

    @GetMapping("trend")
    @ApiOperation("能耗趋势")
    public GHResponse getTrendEnergy(String start_date,String end_date,String scada_value,Integer area_id) {
        return GHResponse.ok(service.getTrendEnergy(start_date,end_date,scada_value,area_id));
    }

    @GetMapping("stat")
    @ApiOperation("能耗统计")
    public GHResponse getStatEnergy(String start_date,String end_date,String scada_value,Integer area_id) {
        return GHResponse.ok(service.getStatEnergy(start_date,end_date,scada_value,area_id));
    }

    @GetMapping("detail")
    @ApiOperation("能耗明细")
    public GHResponse getDetailList(Integer page,String scada_value,Integer area_id) {
        return GHResponse.ok(service.getDetailList(page,scada_value,area_id));
    }

    @GetMapping("gateway")
    @ApiOperation("获取网关")
    public GHResponse getGateway() {
        return GHResponse.ok(service.getGateway());
    }

    @GetMapping("device")
    @ApiOperation("设备图表")
    public GHResponse getDeviceChart(String scada_value) {
        return GHResponse.ok(service.getDeviceChart(scada_value));
    }

    @GetMapping("rank")
    @ApiOperation("排行榜")
    public GHResponse getRankChart(String scada_value) {
        return GHResponse.ok(service.getRankChart(scada_value));
    }








}
