package com.gh.acs.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.gh.acs.model.dto.LiLinDoorRecordDto;
import com.gh.acs.model.entity.LiLinDoorRecord;
import com.gh.common.utils.PageResult;

import java.util.List;

public interface LiLinDoorRecordService extends IService<LiLinDoorRecord> {
    PageResult<List<LiLinDoorRecordDto>> getDoorRecord(String bt, String et, String keyword, Integer page, Integer size);
}
