package com.gh.acs.broadcast.sdk;

public interface ActionCode {
    String Terminal="c2ls_get_server_terminals_status";
    String TerminalVolume="c2ls_set_terminal_volume";
    String TaskVolume="c2ls_set_task_volume";
    String TTS_Engine="c2ls_get_tts_engine_info";
    String Music_List="c2ls_get_server_music_list";
    String Damand_Music="c2ls_mobile_terminal_damand_music";//点播
    String TTS_Music="c2ls_server_tts_mp3_play";
    String Task_List="c2ls_get_task_status";
    String Task_State="c2ls_get_server_terminals_status";
    String Remote_Play="c2ls_control_remote_task";
    String Stop_Task="c2ls_stop_task";

}
