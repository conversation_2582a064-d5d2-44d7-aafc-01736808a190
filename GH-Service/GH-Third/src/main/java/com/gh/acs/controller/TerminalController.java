package com.gh.acs.controller;


import cn.hutool.json.JSONArray;
import com.gh.acs.model.dto.Cmd;
import com.gh.acs.model.dto.SendText;
import com.gh.acs.model.dto.Terminal;
import com.gh.acs.model.vo.PlayTimeVo;
import com.gh.acs.service.SigitalService;
import com.gh.common.utils.GHResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RequestMapping("/sigital")
@RestController
@Api(tags = "盐城得德酒店信息发布")
public class TerminalController {

    @Autowired
    private SigitalService apiService;


    @GetMapping("terminal")
    @ApiOperation("查询终端信息")
    public GHResponse<List<Terminal>> getTerminal( )
    {
        List<Terminal> list =apiService.getTerminal();
        return GHResponse.ok(list);
    }

    @PostMapping("send")
    @ApiOperation("发送文字播报")
    public GHResponse getTerminal(@RequestBody SendText sendText)
    {
        boolean send=apiService.sendText(sendText);
        if(send)
        {
            return GHResponse.ok();

        }
        return GHResponse.failed();
    }

    @PostMapping("cmd")
    @ApiOperation("终端操作")
    public GHResponse set(@RequestBody  Cmd cmd)
    {
        boolean send=apiService.cmd(cmd.getIds(),cmd.getCmd());
        if(send)
        {
            return GHResponse.ok();

        }
        return GHResponse.failed();
    }

    @PostMapping("vol")
    @ApiOperation("终端设置音量")
    public GHResponse setVol(@RequestBody  Cmd cmd)
    {
        boolean send=apiService.setVolume(cmd.getIds(),cmd.getVol());
        if(send)
        {
            return GHResponse.ok();

        }
        return GHResponse.failed();
    }


    @PostMapping("time")
    @ApiOperation("设置规定时间开关机")
    public GHResponse setSwitch(PlayTimeVo playTimeVo)
    {
        boolean send=apiService.setSwitchTime(playTimeVo.getIds(),playTimeVo.getPlayTimes());
        if(send)
        {
            return GHResponse.ok();

        }
        return GHResponse.failed();
    }



    @GetMapping("program")
    @ApiOperation("查询终端信息")
    public GHResponse programList( )
    {
        JSONArray list =apiService.getProgramList();
        return GHResponse.ok(list);
    }





}
