package com.gh.acs.controller;


import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import com.gh.acs.model.vo.AuditVo;
import com.gh.acs.service.MeetingService;
import com.gh.common.utils.GHResponse;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@Api("大华门禁管理")
@RestController
@RequestMapping("/meeting")
public class MeetingController {

    @Autowired
    private MeetingService service;


    @GetMapping("room")
    public GHResponse<Object> room(String keyword) {
        String data = service.roomList(keyword);
        return getGHResponse(data);
    }




        // 1我发起的  2我参与的  3我审核的  0 默认

    //status
    //0 待审核
    //1 预约成功
    //2 已取消
    //3 未开始
    //4 进行中
    //5 已结束
    //6 审核未通过
    //7 审核已过期 过了会议开始时间审核人还未审核
    // 8.待操作  参会人未操作是否接受会议
    //9.已接受
    //10.已拒绝
    //11.邀请已过期
    //100 全部
    @GetMapping("list")
    public GHResponse list( @RequestParam(name = "status",defaultValue = "100") Integer status,
                           @RequestParam(name = "type",defaultValue = "0") Integer type,
                           @RequestParam(name = "page", defaultValue = "1") Integer page,
                           @RequestParam(name = "size",defaultValue = "10") Integer size,
                           String bt, String et,Integer room_id) {
        if(status==null)
        {
            status=100;
        }
        if(type==null)
        {
            type=0;
        }
        String data = service.meetingList(status, type, page, size, bt, et,room_id);

        return getGHResponsePage(data);
    }


    @PostMapping("audit")
    public GHResponse<Object> vehicleList(@RequestBody AuditVo auditVo) {
        boolean audit = service.audit(auditVo);
        if (audit) {
            return GHResponse.ok();
        }
        return GHResponse.failed();
    }


    private GHResponse getGHResponse(String data) {
        if (StrUtil.isNotEmpty(data)) {
            try {
                JSONObject object = JSONObject.parseObject(data);
                JSONArray array = object.getJSONArray("data");
                return GHResponse.ok(array, array.stream().count());
            } catch (JSONException e) {
                return GHResponse.failed(e.getMessage());
            }

        }
        return GHResponse.failed("Response data empty");

    }

    private GHResponse getGHResponsePage(String data) {
        if (StrUtil.isNotEmpty(data)) {
            try {
                JSONObject object = JSONObject.parseObject(data);
                JSONObject resp = object.getJSONObject("data");
                JSONArray array = resp.getJSONArray("list");
                Integer count = resp.getInteger("count");
                return GHResponse.ok(array, count);
            } catch (JSONException e) {
                return GHResponse.failed(e.getMessage());
            }

        }
        return GHResponse.failed("Response data empty");

    }


}
