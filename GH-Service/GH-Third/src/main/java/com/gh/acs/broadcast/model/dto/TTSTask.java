package com.gh.acs.broadcast.model.dto;


import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class TTSTask {
    private List<Integer> tids;
    private Integer priority;
    private Integer volume;
    private Integer speed;
    private Integer times;
    private String text;
    private  String taskName;
}
