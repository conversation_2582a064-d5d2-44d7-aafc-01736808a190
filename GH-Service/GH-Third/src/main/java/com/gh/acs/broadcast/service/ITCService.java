package com.gh.acs.broadcast.service;

import cn.hutool.json.JSONObject;
import com.gh.acs.broadcast.model.dto.ITCDir;
import com.gh.acs.broadcast.model.dto.TaskState;
import com.gh.acs.broadcast.model.entity.ITCGroup;
import com.gh.acs.broadcast.model.entity.ITCTask;
import com.gh.acs.broadcast.model.entity.ITCTerminal;


import java.util.List;

public interface ITCService {
    List<ITCTerminal> getTerminal();
    List<ITCGroup> getGroup();
    String getToken();

    Boolean setTerminalVolume(Integer volume,List<Integer> tid);

    void setTaskVolume(Integer volume,String taskId);
    JSONObject getTerminalLog(String keyword,Integer page,Integer size,String bt,String et);

    JSONObject getTaskLog(String keyword,Integer page,Integer size,String bt,String et);

    String getTTSEngine();

    List<ITCDir> getMusic();
    boolean upload(Integer id, byte[] file,String name);
    boolean  createMusicTask(List<Integer> tids,List<Integer> mids,Integer priority,Integer volume,String mode,String taskName);
    boolean  createTTSTask(List<Integer> tids,Integer priority,Integer volume,Integer speed,Integer times,String text,String taskName);
    boolean CmdTask(String taskId,String code);

    List<ITCTask> getTaskList();

    boolean stopTask(String taskId);

    boolean addMusicCategory(String name,Integer isPublic);

    TaskState getTaskInfo(String taskId);

}
