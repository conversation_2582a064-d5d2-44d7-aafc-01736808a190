package com.gh.acs.controller;


import com.gh.acs.service.BeansService;
import com.gh.common.utils.GHResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Api("九江实验室危化品柜")
@RestController
@RequestMapping("/beans")
public class BeansController {

    @Autowired
    private BeansService service;


    @GetMapping("GetsReagentCabinet")
    @ApiOperation("危化品柜")
    public GHResponse GetsReagentCabinet(Integer page, Integer size, String CabinetName, Integer CabinetType, String location) {
        return GHResponse.ok(service.GetsReagentCabinet(page, size, CabinetName, CabinetType, location)) ;
    }

    @GetMapping("GetsProductLibrary")
    @ApiOperation("试剂列表")
    public GHResponse GetsProductLibrary(Integer page, Integer size, String ReagentName, Integer ReagentType, String CompanyName) {
        return GHResponse.ok(service.GetsProductLibrary(page, size, ReagentName, ReagentType, CompanyName)) ;
    }

    @GetMapping("GetsMaterials")
    @ApiOperation("实时库存")
    public GHResponse GetsMaterials(Integer page, Integer size, String CabinetName, Integer MaterialName, Integer State) {
        return GHResponse.ok(service.GetsMaterials(page, size, CabinetName, MaterialName, State)) ;
    }

    @GetMapping("GetMaterialStatistics")
    @ApiOperation("试剂统计")
    public GHResponse GetMaterialStatistics(Integer page, Integer size, String CabinetName, Integer ReagentName, Integer ReagentType) {
        return GHResponse.ok(service.GetMaterialStatistics(page, size, CabinetName, ReagentName, ReagentType)) ;
    }

    @GetMapping("GetMaterialLocation")
    @ApiOperation("试剂统计 查看")
    public GHResponse GetMaterialLocation(Integer page, Integer size, String MaterialName) {
        return GHResponse.ok(service.GetMaterialLocation(page, size, MaterialName)) ;
    }

    @GetMapping("GetsOverdueReturnWarning")
    @ApiOperation("逾期未归还预警")
    public GHResponse GetsOverdueReturnWarning(Integer page, Integer size, String CabinetName, String MaterialName, Integer MaterialType, String StartTime, String EndTime) {
        return GHResponse.ok(service.GetsOverdueReturnWarning(page, size, CabinetName, MaterialName, MaterialType, StartTime, EndTime)) ;
    }

    @GetMapping("GetsExpirationWarning")
    @ApiOperation("试剂过期预警")
    public GHResponse GetsExpirationWarning(Integer page, Integer size, String CabinetName, String MaterialName, Integer MaterialType, String StartTime, String EndTime) {
        return GHResponse.ok(service.GetsExpirationWarning(page, size, CabinetName, MaterialName, MaterialType, StartTime, EndTime)) ;
    }

    @GetMapping("GetsStockWarning")
    @ApiOperation("库存预警")
    public GHResponse GetsStockWarning(Integer page, Integer size, String CabinetName, String MaterialName, Integer MaterialType, String StartTime, String EndTime) {
        return GHResponse.ok(service.GetsStockWarning(page, size, CabinetName, MaterialName, MaterialType, StartTime, EndTime)) ;
    }

    @GetMapping("GetsReagentCabinetStateRecords")
    @ApiOperation("试剂柜状态记录")
    public GHResponse GetsReagentCabinetStateRecords(Integer page, Integer size, String CabinetName, String StartTime, String EndTime) {
        return GHResponse.ok(service.GetsReagentCabinetStateRecords(page, size, CabinetName, StartTime, EndTime)) ;
    }

    @GetMapping("GetsReagentCabinetDoorRecords")
    @ApiOperation("试剂柜开关记录")
    public GHResponse GetsReagentCabinetDoorRecords(Integer page, Integer size, String CabinetName, String StartTime, String EndTime) {
        return GHResponse.ok(service.GetsReagentCabinetDoorRecords(page, size, CabinetName, StartTime, EndTime)) ;
    }

    @GetMapping("GetsCabinetWarningRecords")
    @ApiOperation("试剂柜预警")
    public GHResponse GetsCabinetWarningRecords(Integer page, Integer size, String CabinetName, String StartTime, String EndTime, Integer WarningType) {
        return GHResponse.ok(service.GetsCabinetWarningRecords(page, size, CabinetName, StartTime, EndTime, WarningType)) ;
    }

    @GetMapping("GetMaterialOutInRecord")
    @ApiOperation("出入库记录")
    public GHResponse GetMaterialOutInRecord(Integer page, Integer size, String CabinetName, String MaterialName, Integer MaterialType, String StartTime, String EndTime, Integer RecordType) {
        return GHResponse.ok(service.GetMaterialOutInRecord(page, size, CabinetName, MaterialName, MaterialType, StartTime, EndTime, RecordType)) ;
    }

    @GetMapping("GetMaterialReceiveReturnRecord")
    @ApiOperation("领用归还记录")
    public GHResponse GetMaterialReceiveReturnRecord(Integer page, Integer size, String CabinetName, String MaterialName, Integer MaterialType, String StartTime, String EndTime, Integer RecordType) {
        return GHResponse.ok(service.GetMaterialReceiveReturnRecord(page, size, CabinetName, MaterialName, MaterialType, StartTime, EndTime, RecordType)) ;
    }

    @GetMapping("GetsMaterialRecord")
    @ApiOperation("盘点记录")
    public GHResponse GetsMaterialRecord(Integer page, Integer size, String CabinetName, String MaterialName, Integer MaterialType, String StartTime, String EndTime) {
        return GHResponse.ok(service.GetsMaterialRecord(page, size, CabinetName, MaterialName, MaterialType, StartTime, EndTime)) ;
    }






}
