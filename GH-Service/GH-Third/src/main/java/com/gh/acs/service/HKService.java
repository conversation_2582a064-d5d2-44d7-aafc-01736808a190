package com.gh.acs.service;

import com.gh.acs.model.dto.DoorState1DTO;
import com.gh.acs.model.vo.DoorControlVO;
import com.gh.acs.model.vo.HKTerminalCmdVO;

import java.util.List;

public interface HKService {

    //门禁
    String getDoorDeviceState(Integer page, Integer size, String ip,String region) throws Exception;


    String getDoorState(Integer page, Integer size, String doorName,String regionIndexCodes) throws Exception;
    List<DoorState1DTO> getDoorState1(Integer page, Integer size, String ip) throws Exception;


    String getDoorEvent(Integer page, Integer size,String bt,String et ,String personName,String doorName,String regionCode) throws Exception;

    String doorControl(DoorControlVO doorControlVO) throws Exception;


    //停车场
    String vehicleList(String plateNo,Integer page,Integer size) throws Exception;

    String crossRecords(String plateNo,Integer page,Integer size,String bt,String et) throws Exception;

    String tempCarInRecords(String keyword,Integer page,Integer size,String bt,String et) throws Exception;


    //信息发布

    String getTerminal(String keyword,Integer page,Integer size) throws Exception;


    String getMaterials(String keyword,Integer page,Integer size) throws Exception;


    String getPrograms(String keyword,Integer page,Integer size) throws Exception;


    String play(String cmd,Integer programId,String playMode) throws Exception;
    String terminalCmd(HKTerminalCmdVO cmdVO) throws Exception;


    String eventLogs(String keyword,Integer page,Integer size,String bt,String et) throws Exception;

    String monitoringPoint(String keyword,Integer page,Integer size) throws Exception;

    String appointment(String keyword,Integer page,Integer size,String bt,String et) throws Exception;


}
