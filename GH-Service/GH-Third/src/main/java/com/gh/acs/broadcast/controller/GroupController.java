//package com.gh.acs.broadcast.controller;
//
//import cn.hutool.core.collection.CollUtil;
//import cn.hutool.core.util.ArrayUtil;
//import com.gh.acs.broadcast.model.entity.ITCGroup;
//import com.gh.acs.broadcast.model.entity.ITCTerminal;
//import com.gh.acs.broadcast.service.ITCGroupService;
//import com.gh.acs.broadcast.service.ITCService;
//import com.gh.common.exception.ExceptionEnum;
//import com.gh.common.exception.GhCustomException;
//import com.gh.common.exception.SystemEnumMsg;
//import com.gh.common.utils.GHResponse;
//
//import com.gh.common.utils.PageResult;
//import io.swagger.annotations.Api;
//import io.swagger.annotations.ApiOperation;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.web.bind.annotation.*;
//
//import javax.validation.Valid;
//import java.util.Arrays;
//import java.util.List;
//
//@RequestMapping("/itc/group")
//@RestController
//@Api(tags = "分组管理")
//public class GroupController {
//    @Autowired
//    private ITCGroupService itcGroupService;
//    private ITCService itcService;
//
//    @PostMapping
//    @ApiOperation("增加分组")
//    public GHResponse Add(@RequestBody @Valid ITCGroup group)
//    {
//        List<ITCGroup> list = itcGroupService.lambdaQuery().eq(ITCGroup::getName, group.getName())
//                .list();
//        if(CollUtil.isNotEmpty(list))
//        {
//            throw new GhCustomException(ExceptionEnum.PROJECT_REPEATE_ADD);
//        }
//        boolean insert = itcGroupService.save(group);
//        if(insert)
//        {
//            return GHResponse.ok(null, SystemEnumMsg.CREATE_SUCCESS.msg());
//        }
//        return GHResponse.failed(SystemEnumMsg.CREATE_ERROR.msg());
//    }
//
//    @PutMapping
//    @ApiOperation("更新分组")
//    public GHResponse Patch(@RequestBody @Valid ITCGroup itcGroup)
//    {
//        List<ITCGroup> itcGroups = itcGroupService.lambdaQuery().eq(ITCGroup::getName, itcGroup.getName())
//                .ne(ITCGroup::getId, itcGroup.getId())
//                .list();
//        if(CollUtil.isNotEmpty(itcGroups))
//        {
//            throw new GhCustomException(ExceptionEnum.PROJECT_REPEATE_ADD);
//        }
//        boolean update = itcGroupService.updateById(itcGroup);
//        if(update)
//        {
//            return GHResponse.ok();
//        }
//        return GHResponse.failed(SystemEnumMsg.Update_ERROR.msg());
//    }
//    @DeleteMapping
//    @ApiOperation("删除分组")
//    public GHResponse Delete(@RequestParam Integer[] ids)
//    {
//
//        if(ArrayUtil.isNotEmpty(ids))
//        {
//            boolean delete = itcGroupService.removeByIds(Arrays.asList(ids));
//            if(delete)
//            {
//                return GHResponse.ok();
//            }
//        }
//
//        return GHResponse.failed(SystemEnumMsg.Delete_ERROR.msg());
//    }
//
//    @GetMapping
//    @ApiOperation("查询分组信息")
//    public GHResponse<List<ITCGroup>> Selects( )
//    {
//        List<ITCGroup> list = itcGroupService.lambdaQuery().list();
//        return GHResponse.ok(list);
//    }
//
//    @GetMapping("terminal")
//    @ApiOperation("查询分组中的终端")
//    public GHResponse<List<ITCTerminal>> SelectsTerminal(Integer groupId, Integer page, Integer size)
//    {
//        PageResult<List<ITCTerminal>> result = itcGroupService.getTerminalByGroup(groupId, page, size);
//        return GHResponse.ok(result.getData(),result.getTotal());
//    }
//
//    @GetMapping("import")
//    @ApiOperation("导入分组")
//    public GHResponse importGroup( )
//    {
//        List<ITCGroup> group = itcService.getGroup();
//        if(CollUtil.isNotEmpty(group))
//        {
//            boolean b = itcGroupService.saveOrUpdateBatch(group);
//            if(b)
//            {
//                return GHResponse.ok();
//            }
//        }
//        return GHResponse.failed();
//    }
//
//
//
//}
