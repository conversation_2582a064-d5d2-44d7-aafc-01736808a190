package com.gh.acs.controller;


import cn.hutool.json.JSONObject;
import com.gh.acs.service.DH8900Service;
import com.gh.common.utils.GHResponse;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Api("大华门禁管理")
@RestController
@RequestMapping("/dahua")
public class DHDoorController {

    @Autowired
    private DH8900Service service;


    @GetMapping("door")
    public GHResponse<String> getDoor(String keyword, Integer page, Integer size) throws Exception {
        String doorDevice = service.getDoorDevice(keyword, page, size);

        return GHResponse.ok(doorDevice);
    }

    @GetMapping("channel")
    public GHResponse<String> getChannel() throws Exception {
        String doorDevice = service.getChannel(null, null, null);

        return GHResponse.ok(doorDevice);
    }




    @GetMapping("login")
    public GHResponse<Object> login() throws Exception {
        String token = service.login();
        return GHResponse.ok(token);
    }

    @GetMapping("record")
    public GHResponse<Object> getRecord(String keyword,String bt,String et ,Integer page, Integer size)
    {
        JSONObject doorLog = service.getDoorLog(bt, et, page, size, keyword);

        return GHResponse.ok(doorLog);
    }

    @GetMapping("count")
    public GHResponse<Object> getRecordCount(String keyword,String bt,String et ,Integer page, Integer size)
    {
        Integer count = service.getDoorLogCount(bt, et, page, size, keyword);

        return GHResponse.ok(count);
    }


    @GetMapping("test")
    public GHResponse<Object> test(String password,String publicKey) throws Exception {



        return null;
    }




}
