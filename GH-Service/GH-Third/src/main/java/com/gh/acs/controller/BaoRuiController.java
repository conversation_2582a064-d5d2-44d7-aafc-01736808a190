package com.gh.acs.controller;


import com.alibaba.fastjson.JSONObject;
import com.gh.acs.service.BaoRuiService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Api("保瑞自控")
@RestController
@RequestMapping("/baorui")
@Slf4j
public class BaoRuiController {

    @Autowired
    private BaoRuiService service;

    @GetMapping("meter")
    @ApiOperation("能耗表具")
    public Object meter(String meterName,Integer meterStatus,Integer energyType,Integer page,Integer limit)  {
        JSONObject data = service.meter(meterName, meterStatus, energyType, page, limit);
        return data;
    }

    @GetMapping("trend")
    @ApiOperation("能耗趋势")
    public Object trend(String targetMonth)  {
        JSONObject data = service.trend(targetMonth);
        return data;
    }

    @GetMapping("quarter")
    @ApiOperation("能耗雷达图")
    public Object quarter()  {
        JSONObject data = service.quarter();
        return data;
    }


    @GetMapping("byMonth")
    @ApiOperation("每月能耗消耗")
    public Object byMonth( String targetMonth,Integer energyType)  {
        JSONObject data = service.byMonth(targetMonth,energyType);
        return data;
    }

    @GetMapping("getEveryConsumptionByEnergy")
    @ApiOperation("当月水和电总量和折算标煤")
    public Object getEveryConsumptionByEnergy( String targetMonth)  {
        JSONObject data = service.getEveryConsumptionByEnergy(targetMonth);
        return data;
    }

    @GetMapping("getConsumptionByEnergy")
    @ApiOperation("当月能耗总量和价格")
    public Object getConsumptionByEnergy( String targetMonth)  {
        JSONObject data = service.getConsumptionByEnergy(targetMonth);
        return data;
    }


    @GetMapping("metersummaryappoint")
    @ApiOperation("截止某个时间")
    public Object metersummaryappoint( String targetMonth,Integer page,Integer limit)
    {
        JSONObject data = service.metersummaryappoint(targetMonth,page,limit);
        return data;
    }

    @GetMapping("list")
    @ApiOperation("区域")
    public Object metersummaryappoint()
    {
        JSONObject data = service.listTree();
        return data;
    }

    @GetMapping("generateDataReport")
    @ApiOperation("数据报表")
    public Object generateDataReport(String address, Integer energyType, String beginTime, String endTime, String aggregationMethod, Integer page, Integer limit)
    {
        JSONObject data = service.generateDataReport(address,energyType,beginTime,endTime,aggregationMethod,page,limit);
        return data;
    }



    @GetMapping("metermonthlysummary")
    @ApiOperation("月统计")
    public Object metermonthlysummary(String metermonthlysummary, Integer page, Integer limit,String meterName)
    {
        JSONObject data = service.metermonthlysummary(metermonthlysummary,page,limit,meterName);
        return data;
    }




    @GetMapping("queryHourlySummary")
    @ApiOperation("小时统计")
    public Object queryHourlySummary(String queryHourlySummary, Integer page, Integer limit)
    {
        JSONObject data = service.queryHourlySummary(queryHourlySummary,page,limit);
        return data;
    }









}
