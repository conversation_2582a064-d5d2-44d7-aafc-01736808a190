package com.gh.acs.config;

import com.hikvision.artemis.sdk.config.ArtemisConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Description:
 * User: zhangkeguang
 * Date: 2023-02-20-11:27
 */

@Configuration
public class HKArtemisConfig {


    @Autowired
    private HKConfig hkConfig;
    @Bean
    public ArtemisConfig ArtemisConfig()
    {
        ArtemisConfig config = new ArtemisConfig();
        config.setHost(hkConfig.getHost()); //
        config.setAppKey(hkConfig.getAppKey());
        config.setAppSecret(hkConfig.getAppSecret());

        return config;
    }
}
