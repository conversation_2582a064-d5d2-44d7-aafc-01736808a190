package com.gh.acs.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gh.acs.model.dto.EnergyHisDataDTO;
import com.gh.acs.model.dto.EnergyRealDataDTO;
import com.gh.acs.model.entity.ziliao;

import java.util.Date;
import java.util.List;

/**
 * Description:
 * User: zhangkeguang
 * Date: 2022-12-10-15:56
 */

public interface energyMapper extends BaseMapper<ziliao> {

   List<EnergyRealDataDTO> getEnergyRealData(Page page, String keyword);

   List<EnergyHisDataDTO> getEnergyHistory(Page page, String keyword, Date bt, Date et);

}
