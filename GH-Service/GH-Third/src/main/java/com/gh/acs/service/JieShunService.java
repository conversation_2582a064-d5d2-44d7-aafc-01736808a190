package com.gh.acs.service;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;

public interface JieShunService {

    JSONObject parkIn(String bt, String et,Integer page,Integer size);
    JSONObject inRecord(String bt, String et,Integer page,Integer size);
    JSONObject outRecord(String bt, String et,Integer page,Integer size);


    JSONObject doorRecord(String bt, String et,Integer page,Integer size);
    JSONObject doorAlarmRecord(String bt, String et,Integer page,Integer size);
    JSONObject doorNormalRecord(String bt, String et,Integer page,Integer size);
    JSONObject doorUsers(String bt, String et,Integer page,Integer size);
    JSONArray doorStatus();

    JSONObject getSummaryInfo();
    JSONObject getDeviceStatus();
    JSONObject fkEvents(String bt, String et,Integer page,Integer size);

    JSONObject getAllPerson(String keyword,Integer page,Integer size);


    String getDepts();

    boolean authPark(JSONObject object);

    boolean addPerson(JSONObject object);


}
