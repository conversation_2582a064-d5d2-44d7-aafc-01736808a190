package com.gh.acs.controller;


import cn.hutool.core.util.StrUtil;
import com.gh.acs.model.dto.*;
import com.gh.acs.model.vo.CmdParamVo;
import com.gh.acs.service.APIService;
import com.gh.common.utils.GHResponse;
import com.gh.common.utils.PageResult;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.stream.Collectors;

@Api("门禁管理")
@RestController
@RequestMapping("/door")
public class DoorController {

    @Autowired
    private APIService service;

    @GetMapping("person")
    public GHResponse<List<Person>> getPerson(String keyword,Integer page,Integer size)
    {
        List<Person> person = service.getPerson();
        List<Person> list = person.stream().filter(person1 -> {
            if (StrUtil.isNotEmpty(keyword)) {
                return person1.getEmplyName().contains(keyword);
            }
            return true;
        }).collect(Collectors.toList());
        if(PageResult.isPage(page,size))
        {
            List<Person> pageList = list.stream().skip((page - 1) * size)
                    .limit(size).collect(Collectors.toList());
            return GHResponse.ok(pageList,list.size());
        }else
        {
            return GHResponse.ok(list,list.size());
        }
    }

    @PostMapping("cmd")
    public GHResponse<List<Person>> CmdDoor(CmdParamVo cmdParamVo)
    {
        boolean success = service.CmdDoor(cmdParamVo.getCmdParam(), cmdParamVo.getCmd());
        if(success)
        {
            return GHResponse.ok();
        }
        return GHResponse.failed();
    }

    public GHResponse<List<com.gh.acs.model.dto.DoorController>>  getDoorController(String keyword,Integer page,Integer size)
    {
        List<com.gh.acs.model.dto.DoorController> controllers = service.getDoorController();
        List<com.gh.acs.model.dto.DoorController> list = controllers.stream().filter(controller -> {
            if (StrUtil.isNotEmpty(keyword)) {
                return controller.getStationName().contains(keyword);
            }
            return true;
        }).collect(Collectors.toList());
        if(PageResult.isPage(page,size))
        {
            List<com.gh.acs.model.dto.DoorController> pageList = list.stream().skip((page - 1) * size)
                    .limit(size).collect(Collectors.toList());
            return GHResponse.ok(pageList,list.size());
        }else
        {
            return GHResponse.ok(list,list.size());
        }
    }


    @GetMapping("door")
    public GHResponse<List<Door>> getDoor(String keyword, Integer page, Integer size)
    {
        List<Door> door = service.getDoor(page,size,keyword);

        return GHResponse.ok(door);
    }

    @GetMapping("record")
    public GHResponse<List<Record>> getRecord(String keyword,String bt,String et ,Integer page, Integer size)
    {
        PageResult<List<Record>> pageResult = service.getRecord(bt, et, page, size);
        if(StrUtil.isNotEmpty(keyword))
        {
            List<Record> list = pageResult.getData();
            List<Record> records = list.stream().filter(record -> record.getDoorName().contains(keyword)).collect(Collectors.toList());
            return  GHResponse.ok(records,pageResult.getTotal());
        }else
        {
            return  GHResponse.ok(pageResult.getData(),pageResult.getTotal());
        }

    }


    @GetMapping("alarm-record")
    public GHResponse<List<AlarmRecord>> getAlarmRecord(String keyword,String bt,String et ,Integer page, Integer size)
    {
        PageResult<List<AlarmRecord>> pageResult = service.getAlarmRecord(bt,et,page,size,keyword);
        return  GHResponse.ok(pageResult.getData(),pageResult.getTotal());
    }
}
