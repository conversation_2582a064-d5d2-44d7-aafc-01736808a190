package com.gh.acs.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gh.acs.model.entity.DeviceSsu;
import com.gh.acs.model.vo.DeviceSsuVO;

import java.util.List;

public interface basiMapper extends BaseMapper<DeviceSsu> {

    List<DeviceSsuVO> getDevice(Page page,String usersn,String username,String path );
}
