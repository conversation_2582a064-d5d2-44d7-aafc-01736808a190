package com.gh.acs.controller;


import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.gh.acs.model.dto.EnergyHisDataDTO;
import com.gh.acs.model.dto.EnergyRealDataDTO;
import com.gh.acs.service.CDEnergyService;
import com.gh.common.utils.GHResponse;
import com.gh.common.utils.PageResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.List;

@Api("成都第七人民医院能耗")
@RestController
@RequestMapping("/cd")
@Slf4j
public class CdEnergyController {

    @Autowired
    private CDEnergyService service;


    @GetMapping("meter")
    @ApiOperation("建筑列表")
    @DS("pd")
    public GHResponse building(Integer size,Integer page,String keyword) {
        if(StrUtil.isNotEmpty(keyword)){
            keyword ="%"+keyword+"%";
        }
        PageResult<List<EnergyRealDataDTO>> data = service.getEnergyRealData(page, size, keyword);
        return GHResponse.ok(data.getData(),data.getTotal());
    }


    @GetMapping("history")
    @ApiOperation("建筑列表")
    @DS("pd")
    public GHResponse history(Integer size, Integer page, String keyword, @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date bt,
                              @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date et
                              ) {
        if(StrUtil.isNotEmpty(keyword)){
            keyword ="%"+keyword+"%";
        }
        PageResult<List<EnergyHisDataDTO>> data = service.getEnergyHistory(page, size, keyword,bt,et);
        return GHResponse.ok(data.getData(),data.getTotal());
    }



}
