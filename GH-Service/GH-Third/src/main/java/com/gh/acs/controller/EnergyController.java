package com.gh.acs.controller;


import com.alibaba.fastjson.JSONObject;
import com.gh.acs.model.vo.HsdaVO;
import com.gh.acs.model.vo.TsdaVO;
import com.gh.acs.service.EnergyService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Api("华为机房")
@RestController
@RequestMapping("/pinggao")
@Slf4j
public class EnergyController {

    @Autowired
    private EnergyService service;

    @PostMapping("tsda")
    @ApiOperation("历史数据")
    public Object tsda(@RequestBody TsdaVO tsdaVO)  {
        JSONObject data = service.tsda(tsdaVO.getType(),tsdaVO.getStart(),tsdaVO.getEnd(),tsdaVO.getFlag(),tsdaVO.getInterval(),tsdaVO.getIds());
        return data;
    }

    @PostMapping("hsda")
    @ApiOperation("实时数据")
    public Object hsda(@RequestBody HsdaVO hsdaVO) {
        JSONObject data = service.hsda(hsdaVO.getType(),hsdaVO.getIds());
        return data;

    }







}
