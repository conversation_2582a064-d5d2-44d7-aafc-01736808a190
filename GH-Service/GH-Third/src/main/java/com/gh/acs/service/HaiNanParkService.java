package com.gh.acs.service;

import com.gh.acs.model.dto.HaiNanInRecord;
import com.gh.acs.model.dto.HaiNanParkRecord;
import com.gh.acs.model.dto.HaiNanParkRecordOpt;
import com.gh.common.utils.PageResult;

import java.util.List;

public interface HaiNanParkService {

    PageResult<List<HaiNanParkRecord>> getRecord(String keyword, String bt, String et, Integer page, Integer limit);


    PageResult<List<HaiNanParkRecordOpt>> getOptRecord(String keyword, String bt, String et, Integer page, Integer limit);


    PageResult<List<HaiNanInRecord>> getInRecord(String keyword, String bt, String et, Integer page, Integer limit);
}
