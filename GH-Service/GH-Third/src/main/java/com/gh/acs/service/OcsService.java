package com.gh.acs.service;

import cn.hutool.json.JSONObject;

public interface OcsService {


    JSONObject getDoor(String doorName,Integer page,Integer size);

    JSONObject getDoorStatus(Integer doorId);

    JSONObject getRecord(String bt,String et,Integer page,Integer size);

    JSONObject getAlarm(String bt,String et,Integer page,Integer size);

    JSONObject opt(String action,Integer doorId);
}
