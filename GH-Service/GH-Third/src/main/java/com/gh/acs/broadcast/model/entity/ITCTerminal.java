package com.gh.acs.broadcast.model.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("GHITC_Terminal")
public class ITCTerminal {
    @TableId(type = IdType.AUTO)
    private Integer id;
    private Integer Brightness;
    private Integer CallCode;
    private Integer DisableFlag;
    private Integer EndpointID;
    private String EndpointIP;
    private String EndpointMac;
    private String EndpointName;
    private Integer EndpointType;
    private String EndpointTypeName;
    private String EndpointVersion;
    private Integer PowerControl;
    private Integer Status;
    private String StatusDsp;
    private String TaskID;
    private Integer TaskType;
    private String TaskTypeName;
    private Integer TimeMode;
    private Integer Volume;

    @TableField(exist = false)
    //groupterminal表中的id,方便删除
    private Integer gtid;

    private Integer ProxyServerID;
    private String ProxyServerIP;
    private String ProxyServerName;
    private Integer ShortOutput;




}
