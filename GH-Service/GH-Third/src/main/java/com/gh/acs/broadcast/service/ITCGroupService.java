package com.gh.acs.broadcast.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.gh.acs.broadcast.model.entity.ITCGroup;
import com.gh.acs.broadcast.model.entity.ITCTerminal;
import com.gh.common.utils.PageResult;

import java.util.List;

public interface ITCGroupService extends IService<ITCGroup> {
    PageResult<List<ITCTerminal>> getTerminalByGroup(Integer groupId, Integer page, Integer size);
}
