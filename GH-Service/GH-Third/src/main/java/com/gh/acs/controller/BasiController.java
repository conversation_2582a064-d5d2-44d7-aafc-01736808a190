package com.gh.acs.controller;


import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.gh.acs.model.entity.DaySum;
import com.gh.acs.model.entity.HourSum;
import com.gh.acs.model.entity.MonthSum;
import com.gh.acs.model.entity.Plot;
import com.gh.acs.model.vo.DeviceSsuVO;
import com.gh.acs.service.*;
import com.gh.common.utils.GHResponse;
import com.gh.common.utils.PageResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.stream.Collectors;

@Api("柏诚能耗")
@RestController
@RequestMapping("/basi")
@Slf4j
public class BasiController {

    @Autowired
    private BasiService service;

    @Autowired
    private PlotService plotService;

    @Autowired
    private MonthSumService monthSumService;

    @Autowired
    private DaySumService daySumService;

    @Autowired
    private HourSumService hourSumService;

    @GetMapping("building")
    @ApiOperation("建筑列表")
    public Object building() {
        String buildings = service.getBuildings();
        if (StrUtil.isNotEmpty(buildings)) {
            JSONObject data = JSONUtil.parseObj(buildings);
            return GHResponse.ok(data.getJSONArray("buildinglist"));
        }
        return GHResponse.ok(null);
    }

    @GetMapping("userssuinfo")
    @ApiOperation("获取用户信息及关联的仪表信息")
    public Object userssuinfo(String buildingid, String usersn) {
        String buildings = service.userssuinfo(buildingid, usersn);
        if (StrUtil.isNotEmpty(buildings)) {
            JSONObject data = JSONUtil.parseObj(buildings);
            return GHResponse.ok(data);
        }
        return GHResponse.ok(null);
    }


    @GetMapping("userlist")
    @ApiOperation("获取用户列表")
    public Object userlist(String buildingid, Integer pagesize, Integer pageindex) {
        String buildings = service.userlist(buildingid, pagesize, pageindex);
        if (StrUtil.isNotEmpty(buildings)) {
            JSONObject data = JSONUtil.parseObj(buildings);
            return GHResponse.ok(data.getJSONArray("userlist"));
        }
        return GHResponse.ok(null);
    }

    @GetMapping("currentdata")
    @ApiOperation("获取仪表的实时数据及状态")
    public Object currentdata(@RequestParam List<String> ids) {
        String buildings = service.currentdata(ids);
        if (StrUtil.isNotEmpty(buildings)) {
            JSONObject data = JSONUtil.parseObj(buildings);
            return GHResponse.ok(data);
        }
        return GHResponse.ok(null);
    }

    @GetMapping("historydata")
    @ApiOperation("读取仪表历史日期日数据")
    public Object historydata(@RequestParam List<String> ids, String daydt) {
        String buildings = service.historydata(ids, daydt);
        if (StrUtil.isNotEmpty(buildings)) {
            JSONObject data = JSONUtil.parseObj(buildings);
            return GHResponse.ok(data);
        }
        return GHResponse.ok(null);
    }

    @GetMapping("fmulistinfo")
    @ApiOperation("获取建筑管理器")
    public Object fmulistinfo(String buildingid, String fmuid) {
        String buildings = service.fmulistinfo(buildingid, fmuid);
        if (StrUtil.isNotEmpty(buildings)) {
            JSONObject data = JSONUtil.parseObj(buildings);
            return GHResponse.ok(data);
        }
        return GHResponse.ok(null);
    }

    @GetMapping("ssulistdata")
    @ApiOperation("通过该接口可获取建筑管理器下属仪表列表信息和实时数据")
    public Object ssulistdata(String buildingid, String fmuid, String startssuaddr, String endssuaddr) {
        String buildings = service.ssulistdata(buildingid, fmuid, startssuaddr, endssuaddr);
        if (StrUtil.isNotEmpty(buildings)) {
            JSONObject data = JSONUtil.parseObj(buildings);
            return GHResponse.ok(data);
        }
        return GHResponse.ok(null);
    }

    @GetMapping("usermeterlist")
    @ApiOperation("获取建筑下面的房间及房间对应的表号")
    public Object usermeterlist(String buildingid, Integer pagesize, Integer pageindex) {
        String buildings = service.usermeterlist(buildingid, pagesize, pageindex);
        if (StrUtil.isNotEmpty(buildings)) {
            JSONObject data = JSONUtil.parseObj(buildings);
            return GHResponse.ok(data);
        }
        return GHResponse.ok(null);
    }

    @GetMapping("readssustate")
    @ApiOperation("采集仪表的数据及状态")
    public Object readssustate(@RequestParam List<String> ids) {
        String buildings = service.readssustate(ids);
        if (StrUtil.isNotEmpty(buildings)) {
            JSONObject data = JSONUtil.parseObj(buildings);
            return GHResponse.ok(data);
        }
        return GHResponse.ok(null);
    }


    @GetMapping("tcehour")
    @ApiOperation("仪表小时")
    public Object tcehour(String ssuid, String metersn, String sdate) {
        String buildings = service.tcehour(ssuid, metersn, sdate);
        if (StrUtil.isNotEmpty(buildings)) {
            Object parse = com.alibaba.fastjson.JSONObject.parse(buildings);
            return GHResponse.ok(parse);
        }
        return GHResponse.ok(null);
    }


    @GetMapping("tceday")
    @ApiOperation("仪表天")
    public Object tceday(String ssuid, String metersn, String year, String month) {
        String buildings = service.tceday(ssuid, metersn, year, month);
        if (StrUtil.isNotEmpty(buildings)) {
            Object parse = com.alibaba.fastjson.JSONObject.parse(buildings);
            return GHResponse.ok(parse);
        }
        return GHResponse.ok(null);
    }


    @GetMapping("tceyear")
    @ApiOperation("仪表月")
    public Object tceyear(String ssuid, String metersn, String year) {
        String buildings = service.tceyear(ssuid, metersn, year);
        if (StrUtil.isNotEmpty(buildings)) {
            Object parse = com.alibaba.fastjson.JSONObject.parse(buildings);
            return GHResponse.ok(parse);
        }
        return GHResponse.ok(null);
    }


    @GetMapping("list")
    @ApiOperation("list")
    @DS("park")
    public Object list(String usersn, Integer page, Integer size, String username,String path) {
        if(StrUtil.isNotEmpty(username))
        {
            username = "%"+username+"%";
        }
        PageResult<List<DeviceSsuVO>> device = service.getDevice(usersn, username, path,page, size);
        return GHResponse.ok(device.getData(), device.getTotal());
    }


    @GetMapping("plot")
    @ApiOperation("plot")
    @DS("park")
    public Object plot() {
        List<Plot> list = plotService.list();
        List<Plot> plots = GetParentTreeNode(list, 0);
        return GHResponse.ok(plots);
    }

    @GetMapping("day")
    @ApiOperation("day")
    @DS("park")
    public Object day(String id, String date) {
        List<DaySum> list = daySumService
                .lambdaQuery().eq(StrUtil.isNotEmpty(id),DaySum::getParDevChID, id)
                .eq(DaySum::getDdate, date)
                .list();
        return GHResponse.ok(list);
    }

    @GetMapping("hour")
    @ApiOperation("hour")
    @DS("park")
    public Object hour(String id, String date) {
        List<HourSum> list = hourSumService.lambdaQuery().eq(StrUtil.isNotEmpty(id),HourSum::getParDevChID, id)
                .eq(HourSum::getHdate, date)
                .list();
        return GHResponse.ok(list);
    }


    @GetMapping("month")
    @ApiOperation("month")
    @DS("park")
    public Object month(String id, String date) {
        List<MonthSum> list = monthSumService.lambdaQuery().eq(StrUtil.isNotEmpty(id),MonthSum::getParDevChID, id)
                .eq(MonthSum::getMyear, date)
                .list();
        return GHResponse.ok(list);
    }




    private List<Plot> GetParentTreeNode(List<Plot> menus, Integer parentId) {
        List<Plot> areaList = menus.stream().filter(menu -> menu != null && menu.getPlparent().equals(parentId))
                .map(menu -> {
                            Plot treeVo = Plot.builder()
                                    .ParUserID(menu.getParUserID())
                                    .PlotID(menu.getPlotID())
                                    .PlText(menu.getPlText())
                                    .Plparent(menu.getPlparent())
                                    .PlPath(menu.getPlPath())
                                    .children(GetParentTreeNode(menus, menu.getPlotID()))
                                    .build();
                            return treeVo;
                        }

                ).collect(Collectors.toList());
        return areaList;
    }




}
