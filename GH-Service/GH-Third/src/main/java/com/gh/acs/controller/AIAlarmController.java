package com.gh.acs.controller;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.gh.acs.model.dto.AIAlarmDTO;
import com.gh.acs.model.entity.AIAlarm;
import com.gh.acs.service.AIAlarmService;
import com.gh.common.utils.GHResponse;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Api("九江实验室ai报警推送")
@RestController
@RequestMapping("/ai")
@Slf4j
public class AIAlarmController {


    @Autowired
    private AIAlarmService service;

    @PostMapping("push")
    public GHResponse record(@RequestBody AIAlarmDTO params) throws Exception {
        log.info("属性数据:{}", params.toString());
        if (params != null) {
            if (params.getEvent().equalsIgnoreCase("start")) {
                AIAlarm aiAlarm = AIAlarm.builder()
                        .vname(params.getVname())
                        .vtags(CollUtil.join(params.getVtags(), StrUtil.COMMA))
                        .types(CollUtil.join(params.getTypes(), StrUtil.COMMA))
                        .logTime(params.getTime())
                        .event(params.getEvent())
                        .video(params.getVideo())
                        .snap(params.getSnap())
                        .uuid(params.getUuid())
                        .build();
                service.save(aiAlarm);
            } else if (params.getEvent().equalsIgnoreCase("end")) {
                service.lambdaUpdate().set(AIAlarm::getEndTime, params.getTime())
                        .eq(AIAlarm::getUuid, params.getUuid()).update();
            } else if (params.getEvent().equalsIgnoreCase("video")) {
                service.lambdaUpdate().set(AIAlarm::getVideo, params.getVideo())
                        .eq(AIAlarm::getUuid, params.getUuid()).update();
            } else if (params.getEvent().equalsIgnoreCase("snap")) {
                service.lambdaUpdate().set(AIAlarm::getSnap, params.getSnap())
                        .eq(AIAlarm::getUuid, params.getUuid()).update();
            }
        }
        return GHResponse.ok();
    }



}
