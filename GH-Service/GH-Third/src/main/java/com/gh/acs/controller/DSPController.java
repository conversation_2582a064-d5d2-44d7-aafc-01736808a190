package com.gh.acs.controller;


import com.alibaba.fastjson.JSONObject;
import com.gh.acs.service.DSPService;
import com.gh.common.utils.GHResponse;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@Api("迪士普接口")
@RestController
@RequestMapping("/dsp")
@Slf4j
public class DSPController {

    @Autowired
    private DSPService dspService;

    @GetMapping("/online")
    public boolean online() {
        return dspService.online();
    }

    @GetMapping("/getAllDevicesID")
    public GHResponse getAllDevicesID() {
        return GHResponse.ok(dspService.getAllDevicesID());
    }
    @GetMapping("/getAllMusicsInfo")
    public GHResponse getAllMusicsInfo() {
        return GHResponse.ok(dspService.getAllMusicsInfo());
    }
    @GetMapping("/getAllDevicesInfo")
    public GHResponse getAllDevicesInfo() {
        return GHResponse.ok(dspService.getAllDevicesInfo());
    }
    @PostMapping("/postPlayMusicByList")
    public GHResponse postPlayMusicByList(@RequestBody JSONObject data) {
        return GHResponse.ok(dspService.postPlayMusicByList(data));
    }

    @PostMapping("/postStopMusic")
    public GHResponse postStopMusic(@RequestBody JSONObject data) {
        return GHResponse.ok(dspService.postStopMusic(data));
    }


    @PostMapping("/postSetvol")
    public GHResponse postSetvol(@RequestBody JSONObject data) {
        return GHResponse.ok(dspService.postSetvol(data));
    }




}
