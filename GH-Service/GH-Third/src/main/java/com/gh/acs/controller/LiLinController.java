package com.gh.acs.controller;


import com.gh.acs.model.dto.LiLinDoorRecordDto;
import com.gh.acs.model.entity.LiLinAlarm;
import com.gh.acs.model.entity.LiLinCallRecord;
import com.gh.acs.service.LiLinAlarmService;
import com.gh.acs.service.LiLinCallRecordService;
import com.gh.acs.service.LiLinDoorRecordService;
import com.gh.common.utils.GHResponse;
import com.gh.common.utils.PageResult;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Api("立林接口")
@RestController
@RequestMapping("/lilin")
public class LiLinController {

    @Autowired
    private LiLinDoorRecordService linDoorRecordService;

    @Autowired
    private LiLinCallRecordService linCallRecordService;

    @Autowired
    private LiLinAlarmService liLinAlarmService;

    @GetMapping("door-record")
    public GHResponse<List<LiLinDoorRecordDto>> getDoorRecord(String bt,String et,String keyword,Integer page,Integer size)
    {
        PageResult<List<LiLinDoorRecordDto>> record = linDoorRecordService.getDoorRecord(bt, et, keyword, page, size);

        return GHResponse.ok(record.getData(),record.getTotal());

    }


    @GetMapping("call-record")
    public GHResponse<List<LiLinCallRecord>> getCallRecord(String bt,String et,Integer page,Integer size)
    {
        PageResult<List<LiLinCallRecord>> record = linCallRecordService.getCallRecord(bt,et,page,size);

        return GHResponse.ok(record.getData(),record.getTotal());

    }


    @GetMapping("alarm-record")
    public GHResponse<List<LiLinAlarm>> getAlarm(String bt,String et,Integer page,Integer size)
    {
        PageResult<List<LiLinAlarm>> record = liLinAlarmService.getAlarm(bt,et,page,size);

        return GHResponse.ok(record.getData(),record.getTotal());

    }


}
