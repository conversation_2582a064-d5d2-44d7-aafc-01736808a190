package com.gh.acs.controller;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.gh.acs.model.dto.RattopPlayDTO;
import com.gh.acs.model.dto.RattopTerminalDTO;
import com.gh.acs.service.RattopService;
import com.gh.common.utils.GHResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 连云港海州体育馆广播系统
 */
@RequestMapping("/rattop")
@RestController
@Api(tags = "广播系统")
public class RattopController {
    @Autowired
    private RattopService rattopService;



    @GetMapping("terminal")
    @ApiOperation("查询设备列表")
    public GHResponse<List<RattopTerminalDTO>> getTerminal(Integer page,Integer size,String devName )
    {

        JSONObject terminal = rattopService.getTerminal(page, size,devName);
        if(null!=terminal)
        {
            JSONArray data = terminal.getJSONArray("rows");
            List<RattopTerminalDTO> list = JSONUtil.toList(data, RattopTerminalDTO.class);
            return GHResponse.ok(list,terminal.getInt("total"));
        }
        return GHResponse.ok(null);
    }


    @GetMapping("mediaFileList")
    @ApiOperation("查询媒体文件列表")
    public GHResponse<JSONArray> getMediaFileList()
    {
        JSONArray mediaFileList = rattopService.getMediaFileList();
        return GHResponse.ok(mediaFileList);
    }

    @PostMapping("addTask")
    @ApiOperation("添加任务")
    public GHResponse<Boolean> addTask(@RequestBody RattopPlayDTO rattopPlayDTO)
    {
        boolean b = rattopService.addTask(rattopPlayDTO);
        return GHResponse.ok(b);
    }

    @PostMapping("playRealtimeTask")
    @ApiOperation("播放实时任务")
    public GHResponse<Boolean> playRealtimeTask(Integer taskId)
    {
        boolean b = rattopService.playRealtimeTask(taskId);
        return GHResponse.ok(b);
    }

    @PostMapping("setVolume")
    @ApiOperation("设置音量")
    public GHResponse<Boolean> setVolume(Integer taskId,Integer vol)
    {
        boolean b = rattopService.setVolume(taskId, vol);
        return GHResponse.ok(b);
    }

    @PostMapping("playPrevious")
    @ApiOperation("播放上一首")
    public GHResponse<Boolean> playPrevious(Integer taskId)
    {
        boolean b = rattopService.playPrevious(taskId);
        return GHResponse.ok(b);
    }

    @PostMapping("playNext")
    @ApiOperation("播放下一首")
    public GHResponse<Boolean> playNext(Integer taskId)
    {
        boolean b = rattopService.playNext(taskId);
        return GHResponse.ok(b);
    }

    @PostMapping("stopRealtimeTask")
    @ApiOperation("停止实时任务")
    public GHResponse<Boolean> stopRealtimeTask(Integer taskId)
    {
        boolean b = rattopService.stopRealtimeTask(taskId);
        return GHResponse.ok(b);
    }

    @PostMapping("removeRealtimeTask")
    @ApiOperation("删除实时任务")
    public GHResponse<Boolean> removeRealtimeTask(@RequestParam String ids)
    {
        boolean b = rattopService.removeRealtimeTask(ids);
        return GHResponse.ok(b);
    }

    @GetMapping("realTimeTaskList")
    @ApiOperation("查询实时任务列表")
    public GHResponse realTimeTaskList(Integer page,Integer size,String taskName)
    {
        JSONObject realTimeTaskList = rattopService.realTimeTaskList(page,size,taskName);
        return GHResponse.ok(realTimeTaskList);
    }

    @PostMapping("updateRealtimeTask")
    @ApiOperation("更新实时任务")
    public GHResponse<Boolean> updateRealtimeTask(@RequestBody RattopPlayDTO rattopPlayDTO)
    {
        boolean b = rattopService.updateRealtimeTask(rattopPlayDTO);
        return GHResponse.ok(b);
    }







}
