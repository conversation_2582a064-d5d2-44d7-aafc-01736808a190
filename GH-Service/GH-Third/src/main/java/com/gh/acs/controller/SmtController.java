package com.gh.acs.controller;


import cn.hutool.json.JSONObject;
import com.gh.acs.service.SmtService;
import com.gh.common.utils.GHResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@Api(tags = "蜀山智慧灯杆")
@RestController
@RequestMapping("/smt")
public class SmtController {

    @Autowired
    private SmtService service;


    @GetMapping("statistics")
    @ApiOperation("统计")
    public GHResponse statistics(String url) {
        JSONObject data = service.getStatistics(url);

        return GHResponse.ok(data);
    }

    @GetMapping("boxList")
    @ApiOperation("灯杆列表")
    public GHResponse boxList(String status,String keyword,Integer page,Integer size) {
        JSONObject data = service.boxList(page,size,keyword,status);

        return GHResponse.ok(data);
    }



    @PostMapping("on")
    @ApiOperation("开灯")
    public GHResponse on(@RequestBody JSONObject data ) {
        JSONObject resp = service.turnOn(data);

        return GHResponse.ok(resp);
    }

    @PostMapping("off")
    @ApiOperation("关灯")
    public GHResponse off(@RequestBody JSONObject data) {
        JSONObject resp = service.turnOff(data);

        return GHResponse.ok(resp);
    }







}
