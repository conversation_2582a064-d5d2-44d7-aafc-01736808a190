package com.gh.acs.model.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class Record {
    private String areaName;   //门区名称
    private Integer cardType;
    private Integer deviceId;  //设备id
    private Integer doorId;  //门id
    private String doorName;
    private Integer doorStatus;
    private String employeeId;     //刷卡人员证件号
    private String employeeName;   //刷卡人员姓名
    private Integer employeeSysNo;    //刷卡人员编号
    private String eventDate;//刷卡时间
    private Integer id;
    private String name;           //刷卡人员部门名称
    private String serial;  //刷卡卡号
    private String stationName;
    private String typedescribe;
    private String typename;
    private String temperature;
    private Integer mask;  //1带口罩，2未戴，0未知
    private String employeePhoto; //网络地址

}
