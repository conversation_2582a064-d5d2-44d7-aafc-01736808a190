package com.gh.acs.model.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Description:
 * User: zhangkeguang
 * Date: 2025-01-08-10:22
 */
@Data
@AllArgsConstructor
@NoArgsConstructor

public class RattopPlayDTO {
    //A 音频文件 T文字广播 C 采集
    private String audioSource;

    // 0 使用 1 不使用
    private String checkDeviceVol;

    private String deviceIds;

    private String fileIds;

    //播放模式 1 顺序播放 2循环播放 3 随机播放
    private  String playMode;

    private  Integer playNum;

    //播放参数 0 文件时长 2 指定次数
    private String playOps;

    private String taskName;

     // 0-未执行，1- 执行中
    private String runStatus;

    //所属方案id
    private Integer schemeId;

    //任务类型 0 实时任务 1 定时任务
    private String taskType;

    private String taskVol;


    //播放时长
    private Integer playDuration;


    //循环播放时长
    private Integer loopPlayDuration;

    private Integer taskId;


}
