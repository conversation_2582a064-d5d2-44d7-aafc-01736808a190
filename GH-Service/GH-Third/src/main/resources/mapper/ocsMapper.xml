<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gh.acs.mapper.OcsMapper">


    <select id="getRecord" resultType="com.gh.acs.model.dto.UserCrdTmDTO">
        SELECT utm.UserName,utm.CarCode,utm.Crdtm,utm.Plate_Color as plateColor,c.ChannelName FROM tc_usercrdtm utm
            LEFT JOIN tc_channel c on utm.ChannelID=c.ChannelID
        <where>

            <if test="bt!=null">
                utm.Crdtm <![CDATA[>=]]> #{bt}
            </if>
            <if test="et!=null">
                and  utm.Crdtm <![CDATA[<=]]> #{et}
            </if>

            <if test="keyword!=null and keyword!=''">
                and utm.CarCode like #{keyword}
            </if>
        </where>
        order by utm.Crdtm desc
        LIMIT #{page},#{size}
    </select>
    <select id="getRecordCount" resultType="int">
        SELECT COUNT(1) FROM tc_usercrdtm utm
        <where>

            <if test="bt!=null">
                utm.Crdtm <![CDATA[>=]]> #{bt}
            </if>
            <if test="et!=null">
                and  utm.Crdtm <![CDATA[<=]]> #{et}
            </if>

            <if test="keyword!=null and keyword!=''">
                and utm.CarCode like #{keyword}
            </if>
        </where>
    </select>



    <select id="getRecordAno" resultType="com.gh.acs.model.dto.UserCrdTmDTO">
        SELECT utm.UserName,utm.CarCode,utm.Crdtm,c.ChannelName FROM tc_usercrdtmin_anomaly utm
        LEFT JOIN tc_channel c on utm.ChannelID=c.ChannelID
        <where>

            <if test="bt!=null">
                utm.Crdtm <![CDATA[>=]]> #{bt}
            </if>
            <if test="et!=null">
                and  utm.Crdtm <![CDATA[<=]]> #{et}
            </if>

            <if test="keyword!=null and keyword!=''">
                and utm.CarCode like #{keyword}
            </if>
        </where>
        order by utm.Crdtm desc
        LIMIT #{page},#{size}
    </select>
    <select id="getRecordAnoCount" resultType="int">
        SELECT COUNT(1) FROM tc_usercrdtmin_anomaly utm
        <where>

            <if test="bt!=null">
                utm.Crdtm <![CDATA[>=]]> #{bt}
            </if>
            <if test="et!=null">
                and  utm.Crdtm <![CDATA[<=]]> #{et}
            </if>

            <if test="keyword!=null and keyword!=''">
                and utm.CarCode like #{keyword}
            </if>
        </where>
    </select>



    <select id="getRecordIn" resultType="com.gh.acs.model.dto.UserCrdTmDTO">
        SELECT utm.UserName,utm.CarCode,utm.Crdtm,utm.Plate_Color as plateColor,c.ChannelName FROM tc_usercrdtm_in utm
        LEFT JOIN tc_channel c on utm.ChannelID=c.ChannelID
        <where>

            <if test="bt!=null">
                utm.Crdtm <![CDATA[>=]]> #{bt}
            </if>
            <if test="et!=null">
                and  utm.Crdtm <![CDATA[<=]]> #{et}
            </if>

            <if test="keyword!=null and keyword!=''">
                and utm.CarCode like #{keyword}
            </if>
        </where>
        order by utm.Crdtm desc
        LIMIT #{page},#{size}
    </select>
    <select id="getRecordInCount" resultType="int">
        SELECT COUNT(1) FROM tc_usercrdtm_in utm
        <where>

            <if test="bt!=null">
                utm.Crdtm <![CDATA[>=]]> #{bt}
            </if>
            <if test="et!=null">
                and  utm.Crdtm <![CDATA[<=]]> #{et}
            </if>

            <if test="keyword!=null and keyword!=''">
                and utm.CarCode like #{keyword}
            </if>
        </where>
    </select>


    <select id="getCarStatistics" resultType="string">
        SELECT SerialData FROM tc_notuploadinterfacerecords ORDER BY AddDate DESC LIMIT 1
    </select>


    <select id="getOperatorRecord" resultType="com.gh.acs.model.dto.OperatorRecordDTO">
        SELECT r.OperatorDate,r.TriggerCarcode,c.ChannelName,o.OperatorName,pd.ItemName  FROM tc_opengaterecord r
        LEFT JOIN tc_channel c on r.ChannelID=c.CameraID
        LEFT JOIN pb_operator o on r.OperatorId=o.OperatorID
        left join pb_dictype pd on r.GateType=pd.ItemValue  and pd.ItemTypeName='开闸方式'
        <where>

            <if test="bt!=null">
                r.OperatorDate <![CDATA[>=]]> #{bt}
            </if>
            <if test="et!=null">
                and  r.OperatorDate <![CDATA[<=]]> #{et}
            </if>

            <if test="keyword!=null and keyword!=''">
                and r.TriggerCarcode like #{keyword}
            </if>
        </where>
        order by r.OperatorDate desc
        LIMIT #{page},#{size}
    </select>

    <select id="getOperatorRecordCount" resultType="int">
        SELECT count(1)  FROM tc_opengaterecord r
        LEFT JOIN tc_channel c on r.ChannelID=c.CameraID
        LEFT JOIN pb_operator o on r.OperatorId=o.OperatorID
        left join pb_dictype pd on r.GateType=pd.ItemValue  and pd.ItemTypeName='开闸方式'
        <where>

            <if test="bt!=null">
                r.OperatorDate <![CDATA[>=]]> #{bt}
            </if>
            <if test="et!=null">
                and  r.OperatorDate <![CDATA[<=]]> #{et}
            </if>

            <if test="keyword!=null and keyword!=''">
                and r.TriggerCarcode like #{keyword}
            </if>
        </where>
    </select>


</mapper>
