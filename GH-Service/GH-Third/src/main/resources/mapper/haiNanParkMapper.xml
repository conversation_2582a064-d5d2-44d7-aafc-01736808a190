<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gh.acs.mapper.HaiNanParkMapper">

    <select id="getRecord" resultType="com.gh.acs.model.dto.HaiNanParkRecord">
        SELECT *
        FROM (
        SELECT TOP (#{size}) *
        FROM (
        SELECT TOP (#{page} * #{size}) *
        FROM Sys_Park_InOutRecords
        ORDER BY passTime DESC
        ) AS SubQuery
        ORDER BY passTime
        ) AS Pagination

        <where>

            <if test="bt!=null">
                passTime <![CDATA[>=]]> #{bt}
            </if>
            <if test="et!=null">
                and passTime <![CDATA[<=]]> #{et}
            </if>

            <if test="keyword!=null and keyword!=''">
                and plateNumber like #{keyword}
            </if>

        </where>
        order by passTime desc
    </select>

    <select id="getRecordCount" resultType="int">
        SELECT count(1) as count
        FROM Sys_Park_InOutRecords
        <where>

            <if test="bt!=null">
                passTime <![CDATA[>=]]> #{bt}
            </if>
            <if test="et!=null">
                and passTime <![CDATA[<=]]> #{et}
            </if>

            <if test="keyword!=null and keyword!=''">
                and plateNumber like #{keyword}
            </if>

        </where>
    </select>

    <select id="getOptRecord" resultType="com.gh.acs.model.dto.HaiNanParkRecordOpt">
        SELECT *
        FROM (
        SELECT TOP (#{size}) *
        FROM (
        SELECT TOP (#{page} * #{size}) *
        FROM Sys_Park_BrakeOpLog
        ORDER BY createTime DESC
        ) AS SubQuery
        ORDER BY createTime
        ) AS Pagination


        <where>

            <if test="bt!=null">
                createTime <![CDATA[>=]]> #{bt}
            </if>
            <if test="et!=null">
                and createTime <![CDATA[<=]]> #{et}
            </if>

            <if test="keyword!=null and keyword!=''">
                and plateNumber like #{keyword}
            </if>

        </where>
        order by createTime desc
    </select>
    <select id="getOptRecordCount" resultType="int">
        SELECT count(1) as count
        FROM Sys_Park_BrakeOpLog
        <where>

            <if test="bt!=null">
                createTime <![CDATA[>=]]> #{bt}
            </if>
            <if test="et!=null">
                and createTime <![CDATA[<=]]> #{et}
            </if>

            <if test="keyword!=null and keyword!=''">
                and plateNumber like #{keyword}
            </if>

        </where>
    </select>
    <select id="getInRecord" resultType="com.gh.acs.model.dto.HaiNanInRecord">
        SELECT *
        FROM (
        SELECT TOP (#{size}) *
        FROM (
        SELECT TOP (#{page} * #{size}) *
        FROM Sys_Park_InParkRecords
        ORDER BY InParkTime DESC
        ) AS SubQuery
        ORDER BY InParkTime
        ) AS Pagination

        <where>

            <if test="bt!=null">
                InParkTime <![CDATA[>=]]> #{bt}
            </if>
            <if test="et!=null">
                and InParkTime <![CDATA[<=]]> #{et}
            </if>


            <if test="keyword!=null and keyword!=''">
                and plateNumber like #{keyword}
            </if>

        </where>
        order by InParkTime desc
    </select>

    <select id="getInRecordCount" resultType="int">
        SELECT count(1) as count
        FROM Sys_Park_InParkRecords

        <where>

            <if test="bt!=null">
                InParkTime <![CDATA[>=]]> #{bt}
            </if>
            <if test="et!=null">
                and InParkTime <![CDATA[<=]]> #{et}
            </if>


            <if test="keyword!=null and keyword!=''">
                and plateNumber like #{keyword}
            </if>

        </where>

    </select>
</mapper>
