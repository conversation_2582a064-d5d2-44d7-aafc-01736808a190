<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gh.acs.mapper.basiMapper">
    <select id="getDevice" resultType="com.gh.acs.model.vo.DeviceSsuVO">
        select DevSsuID,ssu.ParUserID,ParSsuID,ParDevFmuID,SsuCode,SsuAddr,MeterAddr,ssu.ReMark,
        ParSsuState,u.UserName,u.UserSN,ch.ChCurrValue,PlText,StateDesc,FeeName,ch.DevChID from st_DeviceSSU ssu
        left join fe_UserInf u on ssu.ParUserID=u.UserID
        left join st_DeviceCH ch on ssu.DevSsuID=ch.ParDevSsuID
        left join fe_Plot p on ssu.ParUserID=p.ParUser<PERSON>
        left join di_SsuState on ssu.ParSsuState=di_SsuState.diSsuState
        left join bas_FeeType t on ch.ParFeeTypeID=t.FeeTypeID
        <where>
            <if test="usersn!=null and usersn!=''">
                ssu.ParUserID = #{usersn}
            </if>
            <if test="username!=null and username!=''">
                u.UserName like #{username}
            </if>
            <if test="path!=null and path!=''">
                charindex(#{path},p.PlPath)>0
            </if>
        </where>

    </select>


</mapper>
