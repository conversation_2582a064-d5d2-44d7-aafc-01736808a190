<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gh.acs.mapper.QueueMapper">
    <select id="getQueue" resultType="com.gh.acs.model.entity.QueueEntity">
        SELECT  q.* ,p.Name as personName,w.Name  as windowName,bi.Name as busName,d.Name  as deptName,w.Login_Time  from Queue q
        left join Personnel p on q.Personnel_ID =p.ID
        LEFT JOIN [Window] w on window_id=w.id
        LEFT JOIN  Business_Item bi on Bus_ID =bi.ID
        LEFT JOIN  Department d  on q.Dep_ID =d.ID
        <where>
            <if test="keyword!=null and keyword!=''">
                w.name like #{keyword} or bi.name like #{keyword}
            </if>
        </where>

    </select>



    <select id="getQueueDetail" resultType="com.gh.acs.model.dto.QueueRecordDTO">
        SELECT  tu.Name as userName ,tu.Phone ,tu.Identity_Card  ,p.Name  as personName,bi.Name  as busName,w.Name  as windowName, ts.Call_Count ,ts.Call_Time ,
        ts.State ,ts.Pass_Time ,ts.Finish_Time ,ts.Register_Time ,ts.Source_Type
        FROM  Queue ts
        left join TakeUser tu  on ts.TakeUser_ID =tu.ID
        LEFT JOIN  Personnel p on ts.Personnel_ID =p.ID
        LEFT join Business_Item bi  on ts.Bus_ID =bi.ID
        LEFT JOIN [Window] w on ts.Window_ID =w.ID
        <where>
            <if test="winId!=null">
              ts.Window_ID =#{winId}
            </if>
            <if test="busId!=null">
                ts.bus_id =#{busId}
            </if>
            <if test="deptId!=null">
                ts.dep_id =#{deptId}
            </if>
            <if test="keyword!=null and keyword!=''">
                and   tu.name like #{keyword}
            </if>
        </where>
        ORDER  by ts.Register_Time  DESC
    </select>


    <select id="getQueueRecord" resultType="com.gh.acs.model.dto.QueueRecordDTO">
        SELECT  tu.Name as userName ,tu.Phone ,tu.Identity_Card  ,p.Name  as personName,bi.Name  as busName,w.Name  as windowName, ts.Call_Count ,ts.Call_Time ,
        ts.State ,ts.Pass_Time ,ts.Finish_Time ,ts.Register_Time ,ts.Source_Type
        FROM  TimeStatistics ts
        left join TakeUser tu  on ts.TakeUser_ID =tu.ID
        LEFT JOIN  Personnel p on ts.Personnel_ID =p.ID
        LEFT join Business_Item bi  on ts.Bus_ID =bi.ID
        LEFT JOIN [Window] w on ts.Window_ID =w.ID
        <where>
            <if test="bt!=null">
                ts.Register_Time <![CDATA[>=]]> #{bt}
            </if>
            <if test="et!=null">
                and ts.Register_Time <![CDATA[<=]]> #{et}
            </if>

            <if test="keyword!=null and keyword!=''">
               and   (tu.name like #{keyword} or tu.phone like #{keyword} or tu.Identity_Card like #{keyword})
            </if>
        </where>
        ORDER  by ts.Register_Time  DESC
        OFFSET #{offset}  ROWS
        FETCH NEXT #{size} ROWS ONLY;
    </select>

    <select id="getQueueRecordCount" resultType="int">
        SELECT  count(1)
        FROM  TimeStatistics ts
        left join TakeUser tu  on ts.TakeUser_ID =tu.ID
        LEFT JOIN  Personnel p on ts.Personnel_ID =p.ID
        LEFT join Business_Item bi  on ts.Bus_ID =bi.ID
        LEFT JOIN [Window] w on ts.Window_ID =w.ID
        <where>
            <if test="bt!=null">
                ts.Register_Time <![CDATA[>=]]> #{bt}
            </if>
            <if test="et!=null">
                and ts.Register_Time <![CDATA[<=]]> #{et}
            </if>

            <if test="keyword!=null and keyword!=''">
                and   (tu.name like #{keyword} or tu.phone like #{keyword} or tu.Identity_Card like #{keyword})
            </if>
        </where>

    </select>




    <select id="getDept" resultType="com.gh.acs.model.dto.QueueDeptDTO">
        SELECT  * FROM  Department d
    </select>


    <select id="getWindow" resultType="com.gh.acs.model.dto.QueueWindowDTO">
        SELECT  * FROM  [window] d where d.ifdelete=0

            <if test="keyword!=null and keyword!=''">
                and d.name like #{keyword}
            </if>
    </select>





</mapper>
