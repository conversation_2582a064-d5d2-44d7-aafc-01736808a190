<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gh.acs.mapper.LiLinDoorMapper">
    <select id="getDoorRecord" resultType="com.gh.acs.model.dto.LiLinDoorRecordDto">
        SELECT r.the_date,r.card_owner,r.card_no,home_name,r.state,d.address FROM e_access_control_record r
        LEFT JOIN e_setting_device d on r.device_id=d.id
        <where>
            <if test="keyword!=null and keyword!=''">
                r.card_owner like #{keyword}
            </if>
            <if test="bt!=null">
                and  r.the_date <![CDATA[>=]]> #{bt}
            </if>
            <if test="et!=null">
                and r.the_date <![CDATA[<=]]> #{et}
            </if>
        </where>
        order by r.the_date desc
    </select>





</mapper>
