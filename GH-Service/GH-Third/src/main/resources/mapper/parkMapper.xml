<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gh.acs.mapper.parkMapper">
    <select id="existTable" resultType="int">
        SELECT COUNT(1) FROM information_schema.tables WHERE table_schema='park' AND table_name = #{tableName}
    </select>

    <update id="createTable" parameterType="string" >
        CREATE TABLE ${name} (
                                  `id` int NOT NULL AUTO_INCREMENT,
                                  `serialno` varchar(100) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
                                  `berth` varchar(10) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
                                  `parkCode` varchar(10) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
                                  `devNum` int DEFAULT NULL,
                                  `eventType` int DEFAULT NULL,
                                  `eventTime` bigint DEFAULT NULL,
                                  `license` varchar(20) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
                                  `color` int DEFAULT NULL,
                                  `plateType` int DEFAULT NULL,
                                  `images` text COLLATE utf8_unicode_ci,
                                  `left` int DEFAULT NULL,
                                  `isPay` int DEFAULT NULL,
                                  `TT` bigint DEFAULT NULL,
                                  PRIMARY KEY (`id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8_unicode_ci
    </update>

<!--    <update id="saveRecord" parameterType="com.gh.acs.model.entity.Record" >-->
<!--        INSERT INTO ${table} (`serialno`, `berth`, `parkCode`, `devNum`, `eventType`, `eventTime`, `license`, `color`, `plateType`, `images`, `left`, `isPay`, `TT`)-->
<!--        VALUES ( ${record.serialno,jdbcType=VARCHAR}, ${record.berth,jdbcType=VARCHAR}},-->
<!--                ${record.parkCode,jdbcType=VARCHAR}},-->
<!--                ${record.devNum,jdbcType=VARCHAR}}, ${record.eventType,jdbcType=VARCHAR}},-->
<!--                ${record.eventTime},${record.license,jdbcType=VARCHAR}},-->
<!--                ${record.color,jdbcType=VARCHAR}}, ${record.plateType,jdbcType=VARCHAR}},-->
<!--                ${record.images,jdbcType=VARCHAR}}, ${record.left,jdbcType=VARCHAR}},-->
<!--                ${record.isPay,jdbcType=VARCHAR}}, ${record.TT,jdbcType=VARCHAR}})-->
<!--    </update>-->


    <select id="getCar" resultType="com.gh.acs.model.dto.CarPlaceDTO">
        select p.Angle,p.x,p.Y,r.Car_No,p.CarPlaceNO from PG_MapCarPlace p left join REG_RECORD r on p.CarPlaceNO=r.CAM_LOCATION
    </select>





</mapper>
