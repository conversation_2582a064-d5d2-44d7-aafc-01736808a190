<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gh.acs.mapper.ShiHanMapper">


    <select id="getDevice" resultType="com.gh.acs.model.dto.HjDeviceDTO">
        SELECT d.Device_ID,Device_Name,d.Device_IP,Device_Mac,Device_Status,Device_Online,Device_Type,d.Department,key_value as deviceTypeName FROM hospital_devices d
        left join hospital_device_type_name t on d.Device_Type=t.key_id
        <where>

            <if test="dept!=null">
                d.Department =#{dept}
            </if>


            <if test="keyword!=null and keyword!=''">
                and d.Device_Name like #{keyword}
            </if>
        </where>
    </select>


<!--    <select id="getServiceLog" resultType="com.gh.acs.model.dto.ServiceLogDTO">-->
<!--        SELECT * FROM hospital_service_log l-->
<!--        <where>-->

<!--            <if test="dept!=null">-->
<!--                d.Department =#{dept}-->
<!--            </if>-->


<!--            <if test="keyword!=null and keyword!=''">-->
<!--                and d.Device_Name like #{keyword}-->
<!--            </if>-->
<!--        </where>-->
<!--    </select>-->



    <select id="getCallRecord" resultType="com.gh.acs.model.dto.CallRecordDTO">
        SELECT department ,send,savetime,status FROM hospital_call_record r
        <where>

            <if test="dept!=null">
                r.dept_id =#{dept}
            </if>

        </where>
    </select>


    <select id="getDoctor" resultType="com.gh.acs.model.dto.DoctorDTO">
        SELECT d.doctor_name,doctor_title,doctor_sex,Department,doctor_type,doctor_card,description FROM hospital_doctors  d
        <where>

            <if test="dept!=null">
                d.Department =#{dept}
            </if>

            <if test="keyword!=null and keyword!=''">
                and d.doctor_name like #{keyword}
            </if>

        </where>
    </select>



    <select id="getDept" resultType="com.gh.acs.model.dto.DeptDTO">
        SELECT * from hospital_department d
    </select>



    <select id="getNurse" resultType="com.gh.acs.model.dto.NurseDTO">
        SELECT n.nurse_title,nurse_name,nurse_sex ,Department,Nurse_Card,Nurse_Type,description FROM hospital_nurses n
        <where>

            <if test="dept!=null">
                n.Department =#{dept}
            </if>

            <if test="keyword!=null and keyword!=''">
                and n.nurse_name like #{keyword}
            </if>

        </where>
    </select>


    <select id="getPatient" resultType="com.gh.acs.model.dto.PatientDTO">
        SELECT Patient_Name,Device_Name,Patient_Sex,Patient_Age,Come_Time,Leave_Time,Patient_Number,
        Nurse_Name,Doctor_Name,Grade_Name,Department FROM hospital_patients p
        <where>

            <if test="dept!=null">
                p.Department =#{dept}
            </if>

            <if test="keyword!=null and keyword!=''">
                and p.Patient_Name like #{keyword}
            </if>

        </where>
    </select>





</mapper>
