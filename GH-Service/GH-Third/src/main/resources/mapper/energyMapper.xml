<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gh.acs.mapper.energyMapper">
    <select id="getEnergyRealData" resultType="com.gh.acs.model.dto.EnergyRealDataDTO">
        SELECT bh,mc,yzxm,lx,F_OrigValue  FROM gcxyzlbiao gzl LEFT JOIN  Engey_NEWD e on e.F_MeterParamID=gzl.bh
        <where>
            <if test="keyword!=null and keyword!=''">
                and   mc like #{keyword}
            </if>
        </where>
        order by bh
    </select>



    <select id="getEnergyHistory" resultType="com.gh.acs.model.dto.EnergyHisDataDTO">
        SELECT bh,mc,yzxm,lx,F_CollectTime,F_OrigValue,LAST_Time,LAST_Value,div_Value FROM  gcxyzlbiao
        LEFT JOIN MeterData on bh=F_MeterParamID
        <where>
            <if test="keyword!=null and keyword!=''">
                   mc like #{keyword}
            </if>
            <if test="bt!=null and et!=null">
                and   MeterData.F_CollectTime between #{bt} and #{et}
            </if>
        </where>
        order by gcxyzlbiao.bh
    </select>






</mapper>
