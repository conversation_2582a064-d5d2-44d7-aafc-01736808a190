spring:
  application:
    name: acs-service
  cloud:
    nacos:
      server-addr: 36.152.149.52:28848
      config:
        file-extension: yaml
        namespace: 9027158d-ab5d-43f7-a691-3b7546cc79f9
        extension-configs:
#          - data-id: mysql.yml
          - data-id: redis.yml
          - data-id: json.yml
          - data-id: swagger.yml
      discovery:
        server-addr: 36.152.149.52:28848
        namespace: 9027158d-ab5d-43f7-a691-3b7546cc79f9
  datasource:
    dynamic:
      primary: master
      strict: false
      datasource:
        master:
          url: ********************************************************************************************************************************************************************************************************************
          username: root
          password: Njgh8888
          driver-class-name: com.mysql.jdbc.Driver
          type: com.zaxxer.hikari.HikariDataSource
#        hj:
#          url: ************************************************************************************************************************************************************************************************************************
#          username: root
#          password: Njgh8888
#          driver-class-name: com.mysql.jdbc.Driver
#          type: com.zaxxer.hikari.HikariDataSource
#        ocs:
#          url: ****************************************************************************************************************************************************************************************************************************
#          username: root
#          password: Njgh8888
#          driver-class-name: com.mysql.jdbc.Driver
#          type: com.zaxxer.hikari.HikariDataSource

#        pd:
#          driver-class-name: com.microsoft.sqlserver.jdbc.SQLServerDriver
#          url: **********************************************************************
#          username: sa
#          password: 123456


      hikari:
          minimum-idle: 1
          connection-test-query: SELECT 1 FROM DUAL
          maximum-pool-size: 8
          auto-commit: true
          idle-timeout: 30000
          pool-name: GHHikariCP
          max-lifetime: 120000
          connection-timeout: 30000
  mvc:
    servlet:
      load-on-startup: 1

mybatis-plus:
  mapper-locations: classpath:mapper/*.xml
  configuration:
    map-underscore-to-camel-case: false
server:
  port: 9036


zk:
  url: http://123
  session: 123
  username: admin
  password: 21232f297a57a5a743894a0e4a801fc3

dahua:
  url: http://172.20.11.2
  username: admin
  password: admin
  clientid: 1
  clientsecret: 1

sigital:
  url: http://172.20.11.248:6101
  token: yanchengdede

itc:
  url: 47.119.143.190:8001 #广播
  url1: http://101.69.231.70:9101 #信息发布
  username: test0313
  password: test0313
  token: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.5yfuulZIeKwt49ka-MoX-6Z6WMWsvPjJ-_TIiU5BLko


kunshan:
  door: http://127:8080

lilin:
  clientId: 111
  clientSecret: 1111


jinhu:
  info:
    token: 123456
    url: http://


jieshun:
  url: http://
  token: 111
  username: 9999
  password: 123456

ipcast:
  ip: ************
  username: admin
  password: admin

hk:
  host: ***************:443
  appKey: 20242676
  appSecret: SBlBQe1dxb9o2RwTJeGk


meeting:
  url: http://**************:88
  username: admin
  password: admin
#//华为机房
hw:
  url: http://**************:1026
pinggao:
  url: http://**************:8888/auth
  key: pdsl1t9qp7f0pe24pq9ew99ws3px0lqq
  token: pdsj94w3wbcgb1r490leq8ce2wdlfr6z

#保瑞自控
baorui:
  url: http://**************
  username: yuanlin
  password: e10adc3949ba59abbe56e057f20f883e


#柏诚能耗
basi:
  appid: gzbasicapi
  secret: gzbasicapi_123
  key: GZbasicapi2022dj
  url: http://*************:1303



#淮北环控主机
pec:
  url: http://*************


#淮北宇视停车系统
unv:
  #停车场地址
  park: http://*************
  #停车场平台账号
  user: admin
  pass: 123456
  #车位地址
  car: http://*************
  #车位引导平台账号
  username: admin
  password: AE9OddGsD/OP9Bfh0X3ee4VOtI1He7NOQBEg9xs67Rrncrj8q0Fyw4uKVRsYh8TZKZF6WaSWFqwVJVVbqUiRiw==

#淮北信息发布
gtv:
  url: http://192.168.70.92:8088/cdms/login
  username: sa
  password: e10adc3949ba59abbe56e057f20f883e

#深圳达实门禁
dashi:
  url: http://*************:8090
  key: BF0829547048056603A677A1EE6E26C4

#蜀山为民中心能耗
yla:
  url: http://
  username: hexin
  password: hexin


#立方门禁
ocs:
  url: http://

#蜀山智慧灯杆
smt:
  url: https://smt.chinahcxny.com/api
  username: 蜀山为民智慧灯杆
  password: 123456

#蜀山视视瀚
shihan:
  msgurl: http://**************/admin/open
  meeting_url: https://www.h5-yes.com
  meeting_key: 2383lyadt6hipr3xon9q
  meeting_secret: 654ea3c9016cb718af4a669ae9918f182c7682e7

#迪士普音乐
dsp:
  url: http://192.168.2.300:25012/api

chuangyue:
  url: http://************
  username: admin
  password: admin


nova:
  url: http://nova.paas.wuyezhijia.cn/gateway
  client_id: zhonglianzhihui
  client_secret: zhonglian@2024
#毕恩思
beans:
  url: http://nova.paas.wuyezhijia.cn


#九江德福会议室
df:
  url: http://**********:8888/seeyon
  userName: hys
  password: 46389d05-c986-4080-b2fd-27b1c95d5419
  loginName: 闲置


ai:
  url: http://***********:6789
  name: admin
  pass: 123456


#连云港雷拓广播
rattop:
  url: http://localhost:9010
  username: admin
  password: admin
