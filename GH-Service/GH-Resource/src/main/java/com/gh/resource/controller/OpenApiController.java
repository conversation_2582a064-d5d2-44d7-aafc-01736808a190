package com.gh.resource.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.gh.common.exception.SystemEnumMsg;
import com.gh.common.redis.RedisHelper;
import com.gh.common.utils.GHResponse;
import com.gh.common.utils.PageResult;
import com.gh.resource.model.dto.*;
import com.gh.resource.model.entity.Device;
import com.gh.resource.model.entity.Product;
import com.gh.resource.model.vo.OpenProductVO;
import com.gh.resource.service.DeviceService;
import com.gh.resource.service.DeviceStandardService;
import com.gh.resource.service.ProductService;
import com.gh.resource.service.ProductStdService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@RestController
@Api(tags = "开放接口")
@Validated
public class OpenApiController {

    @Autowired
    private ProductService productService;

    @Autowired
    private DeviceService deviceService;

    @Autowired
    private DeviceStandardService deviceStandardService;

    @Autowired
    private RedisHelper redisHelper;

    @Autowired
    private ProductStdService productStdService;

    @GetMapping("/v1/{projectCode}/product/list")
    @ApiOperation("查询产品")
    @Validated
    public GHResponse<List<OpenProductVO>> listProduct(
            @PathVariable("projectCode") @NotBlank(message = "项目编码不能为空") String projectCode) {

        List<Product> product = productService.getOpenProduct(projectCode, null, null);
        List<OpenProductVO> list = product.stream().map(p -> {
            OpenProductVO productVO = new OpenProductVO();
            productVO.setId(p.getId());
            productVO.setName(p.getName());
            productVO.setIdentifier(p.getIdentifier());
            return productVO;
        }).collect(Collectors.toList());
        return GHResponse.ok(list, list.size());
    }

    @GetMapping("/v1/{projectCode}/product/{id:[0-9]{1,10}}")
    @ApiOperation("查询产品")
    @Validated
    public GHResponse<OpenProductVO> productById(
            @PathVariable("projectCode") @NotBlank(message = "项目编码不能为空") String projectCode,
            @PathVariable("id") @NotNull(message = "产品id不能为空") Integer id) {
        List<Product> product = productService.getOpenProduct(projectCode, id, null);
        if (CollUtil.isNotEmpty(product)) {
            OpenProductVO productVO = OpenProductVO.builder()
                    .id(product.get(0).getId())
                    .name(product.get(0).getName())
                    .identifier(product.get(0).getIdentifier())
                    .build();
            return GHResponse.ok(productVO, 1);
        }
        return GHResponse.ok(null);
    }

    @GetMapping("/v1/{projectCode}/product/{identifier:[a-zA-Z][a-zA-Z0-9\\\\$_\\\\-]*}")
    @ApiOperation("查询产品")
    @Validated
    public GHResponse<OpenProductVO> productByIdentifier(
            @PathVariable("projectCode") @NotBlank(message = "项目编码不能为空") String projectCode,
            @PathVariable("identifier") @NotBlank(message = "产品编码不能为空") String identifier) {
        List<Product> product = productService.getOpenProduct(projectCode, null, identifier);
        if (CollUtil.isNotEmpty(product)) {
            OpenProductVO productVO = OpenProductVO.builder()
                    .id(product.get(0).getId())
                    .name(product.get(0).getName())
                    .identifier(product.get(0).getIdentifier())
                    .build();
            return GHResponse.ok(productVO, 1);
        }
        return GHResponse.ok(null);
    }

    @GetMapping("/v1/{projectCode}/device/list")
    @ApiOperation("查询设备")
    @Validated
    public GHResponse<List<DeviceInfoDto>> deviceList(@RequestParam(required = false) String identifier,
            @PathVariable("projectCode") @NotBlank(message = "项目编码不能为空") String projectCode, Integer areaId) {
        List<DeviceInfoDto> list = deviceService.getOpenDeviceList(projectCode, identifier, null, null, areaId);
        return GHResponse.ok(list, list.size());

    }

    @GetMapping("/v1/{projectCode}/device/{id:[0-9]{1,10}}")
    @ApiOperation("查询设备")
    @Validated
    public GHResponse<List<DeviceInfoDto>> deviceById(
            @PathVariable("projectCode") @NotBlank(message = "项目编码不能为空") String projectCode,
            @PathVariable("id") @NotNull(message = "设备id不能为空") Integer id) {
        List<DeviceInfoDto> list = deviceService.getOpenDeviceList(projectCode, null, id, null, null);
        return GHResponse.ok(list, list.size());

    }

    @GetMapping("/v1/{projectCode}/device/{code:(?![0-9]).*}")
    @ApiOperation("查询设备")
    @Validated
    public GHResponse<List<DeviceInfoDto>> deviceByCode(
            @PathVariable("projectCode") @NotBlank(message = "项目编码不能为空") String projectCode,
            @PathVariable("code") @NotBlank(message = "设备code不能为空") String code) {
        List<DeviceInfoDto> list = deviceService.getOpenDeviceList(projectCode, null, null, code, null);
        return GHResponse.ok(list, list.size());

    }

    @GetMapping("/v1/{projectCode}/product/property/{identifier}")
    @ApiOperation("查询产品属性")
    @Validated
    public GHResponse productProperty(
            @PathVariable("identifier") @NotBlank(message = "产品identifier不能为空") String identifier) {

        PageResult<List<ProductStdDTO>> pageResult = productStdService.getStandardByProductId(null, null, null, null,
                null, identifier);
        return GHResponse.ok(pageResult.getData(), pageResult.getTotal());
    }

    @GetMapping("/v1/{projectCode}/device/property/{code}")
    @ApiOperation("查询设备属性")
    @Validated
    public GHResponse deviceProperty(@PathVariable("projectCode") @NotBlank(message = "项目编码不能为空") String projectCode,
            @PathVariable("code") @NotBlank(message = "设备code不能为空") String code) {
        List<DeviceValDTO> deviceVal = deviceStandardService.getDeviceVal(null, null, null, code, null, projectCode,
                null);

        return GHResponse.ok(deviceVal);

    }

    @GetMapping("/v1/{projectCode}/device/read/{code}")
    @ApiOperation("查询单个设备所有属性值")
    @Validated
    public GHResponse devicePropertyVal(@PathVariable("projectCode") @NotBlank(message = "项目编码不能为空") String projectCode,
            @PathVariable("code") @NotBlank(message = "设备code不能为空") String code) {
        // 判读code是不是全是数字
        List<DeviceValDTO> deviceVal = deviceStandardService.getDeviceVal(null, null, null, code, null, projectCode,
                null);
        if (CollUtil.isNotEmpty(deviceVal)) {
            deviceVal.forEach(deviceValDTO -> {
                deviceValDTO.getParams().forEach(deviceStandard -> {
                    String variable = deviceStandard.getVariable();
                    if (StrUtil.isNotEmpty(variable)) {
                        String value = redisHelper.HashGetFields(14, "var:" + variable, "Value");
                        if (StrUtil.isNotEmpty(value)) {
                            deviceStandard.setValue(value);
                        }
                    }
                });
            });
        }
        return GHResponse.ok(deviceVal);

    }

    @GetMapping("/v1/{projectCode}/device/read/val")
    @ApiOperation("查询单个设备所有属性值")
    @Validated
    public GHResponse devicePropertyVal(@PathVariable("projectCode") @NotBlank(message = "项目编码不能为空") String projectCode,
            String code, Integer deviceId) {
        List<DeviceValDTO> deviceVal = deviceStandardService.getDeviceVal(null, deviceId, null, code, null, projectCode,
                null);
        if (CollUtil.isNotEmpty(deviceVal)) {
            deviceVal.forEach(deviceValDTO -> {
                deviceValDTO.getParams().forEach(deviceStandard -> {
                        String value = redisHelper.HashGetFields(14,
                                "product:" + deviceValDTO.getProductId() + ":" + deviceValDTO.getId(),
                                deviceStandard.getIdentifier());
                        if (StrUtil.isNotEmpty(value)) {
                            deviceStandard.setValue(value);
                        }
                    
                });
            });
        }
        return GHResponse.ok(deviceVal);

    }

    @GetMapping("/v1/{projectCode}/devices/read")
    @ApiOperation("查询所有设备所有属性值")
    @Validated
    public GHResponse devicesPropertyVal(
            @PathVariable("projectCode") @NotBlank(message = "项目编码不能为空") String projectCode, String identifier,
            Integer areaId) {
        List<DeviceValDTO> deviceVal = deviceStandardService.getDeviceVal(null, null, identifier, null, null,
                projectCode, areaId);

        List<String> list = deviceVal.stream().flatMap(deviceValDTO -> deviceValDTO.getParams().stream())
                .filter(deviceStandard -> null != deviceStandard.getProductId() && null != deviceStandard.getDeviceId()
                        && StrUtil.isNotBlank(deviceStandard.getIdentifier()))
                .map(deviceStandard -> "product:" + deviceStandard.getProductId() + ":" + deviceStandard.getDeviceId())
                .collect(Collectors.toList());
        Map<String, Map<String, String>> map = redisHelper.hashOjectList(14, list);
        for (DeviceValDTO deviceValDTO : deviceVal) {
            deviceValDTO.getParams().forEach(deviceStandard -> {
                Map<String, String> property = map
                        .get("product:" + deviceStandard.getProductId() + ":" + deviceStandard.getDeviceId());
                if (property != null) {
                    deviceStandard.setValue(property.get(deviceStandard.getIdentifier()));
                }
            });
        }
        return GHResponse.ok(deviceVal);
    }

    @GetMapping("/v1/{projectCode}/device/read")
    @ApiOperation("查询单个设备单个属性值")

    public GHResponse propertyWrite(@PathVariable("projectCode") @NotBlank(message = "项目编码不能为空") String projectCode,
            @RequestParam @NotBlank(message = "设备deviceCode不能为空") String deviceCode,
            @RequestParam @NotBlank(message = "设备属性property不能为空") String property) {
        List<DeviceValDTO> deviceVal = deviceStandardService.getDeviceVal(null, null, null, deviceCode, property,
                projectCode, null);
        if (CollUtil.isNotEmpty(deviceVal)) {
            deviceVal.forEach(deviceValDTO -> {
                deviceValDTO.getParams().forEach(deviceStandard -> {
                    String value = redisHelper.HashGetFields(14,
                            "product:" + deviceValDTO.getProductId() + ":" + deviceValDTO.getId(),
                            deviceStandard.getIdentifier());
                    if (StrUtil.isNotEmpty(value)) {
                        deviceStandard.setValue(value);
                    }

                });
            });
        }
        return GHResponse.ok(deviceVal);
    }

    @PostMapping("/v1/{projectCode}/device/add")
    @ApiOperation("新增设备")
    @Validated
    public GHResponse addDevice(@PathVariable("projectCode") @NotBlank(message = "项目编码不能为空") String projectCode,
            @RequestBody Device device) {
        List<DeviceProjectDTO> list = deviceService.getDeviceProjectList(projectCode);
        if (CollUtil.isNotEmpty(list)) {
            device.setProjectId(list.get(0).getId());
        }
        boolean insert = deviceService.Save(device);
        if (insert) {
            return GHResponse.ok(device, SystemEnumMsg.CREATE_SUCCESS.msg());
        }
        return GHResponse.failed(SystemEnumMsg.CREATE_ERROR.msg());
    }

    @PostMapping("/v1/device/del")
    @ApiOperation("删除设备")
    @Validated
    public GHResponse addDevice(@RequestParam Integer[] ids) {
        boolean remove = deviceService.removeByIds(Arrays.asList(ids));
        if (remove) {
            return GHResponse.ok();
        }
        return GHResponse.failed(SystemEnumMsg.Delete_ERROR.msg());

    }

    @PostMapping("/v1/device/update")
    @ApiOperation("编辑设备")
    @Validated
    public GHResponse addDevice(@RequestBody Device device) {
        boolean update = deviceService.Update(device);
        if (update) {
            return GHResponse.ok();
        }
        // }
        return GHResponse.failed(SystemEnumMsg.Update_ERROR.msg());
    }

    @PostMapping("/v1/device/property/push")
    public GHResponse devProPush(@RequestBody JSONObject params) {

        boolean push = deviceService.push(params);
        if (push) {
            return GHResponse.ok();
        }

        return GHResponse.failed();
    }

    @GetMapping("/v1/{projectCode}/device/tree")
    @ApiOperation("设备树")
    public GHResponse<List<ProductTreeDTO>> deviceTree(Integer productId) {

        List<ProductTreeDTO> ProductDeviceDTO = productService.getAllDeviceTree(productId);
        return GHResponse.ok(ProductDeviceDTO);
    }

}
