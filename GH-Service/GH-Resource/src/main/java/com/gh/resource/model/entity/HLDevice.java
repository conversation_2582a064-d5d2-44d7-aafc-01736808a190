package com.gh.resource.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * Description:
 * User: zhangkeguang
 * Date: 2024-01-05-10:54
 */

@Data
@Builder
@ApiModel(description = "上海互联临时设备表演示使用")
@TableName("lvfu_dev_monitoring")
@NoArgsConstructor
@AllArgsConstructor
public class HLDevice {

    @TableId(type = IdType.AUTO)
    private Integer id;

    private String deviceName;
    private String stationName;
    private String monitorSiteName;
    private String oid;
    private String ipAddress;
    private String deviceType;
    private String category;
    private String manufacturer;
    private Integer cmStatus;
    private float cpuPercentage;
    private float memoryPercentage;
    private String systemUptime;
    private Integer numberOfSystemUsers;
    private Date systemTime;
    private float memorySize;
    private float totalMemory;
    private float freeMemory;
    private float usedMemory;
    private String managementAddress;
//    private float temperature;
    private String fanStatus;
    private Date createTime;

    private Integer processNum;

    private String json_file;
    private float osDiskCpa;
    private float osDiskUse;
    private float osDiskPercentage;

    private String power1Status;
    private String power2Status;
    private String temperatureStatus;


}
