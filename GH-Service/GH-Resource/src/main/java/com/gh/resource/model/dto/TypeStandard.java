package com.gh.resource.model.dto;


import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TypeStandard {
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String id;
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String name;
    private Integer deviceId;
    private String variable;
    private String keyCode;
    private String deviceName;
    private String deviceCode;
//    private String roomId;
    private Integer productId;
    private String identifier;
    private String productName;
    private Integer projectId;
}
