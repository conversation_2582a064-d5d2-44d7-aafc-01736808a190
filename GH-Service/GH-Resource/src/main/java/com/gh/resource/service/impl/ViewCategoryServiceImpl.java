package com.gh.resource.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gh.resource.mapper.ViewCategoryMapper;
import com.gh.resource.model.entity.DiagramFile;
import com.gh.resource.model.entity.ViewCategory;
import com.gh.resource.model.vo.ViewCategoryTreeVo;
import com.gh.resource.service.DiagramFileService;
import com.gh.resource.service.DiagramService;
import com.gh.resource.service.ViewCategoryService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class ViewCategoryServiceImpl extends ServiceImpl<ViewCategoryMapper,ViewCategory> implements ViewCategoryService {


    private final ViewCategoryMapper viewCategoryMapper;


    private final DiagramService diagramService;

    private final DiagramFileService diagramFileService;


    @Override
    public List<ViewCategoryTreeVo> Select(Integer projectId,boolean view) {
        QueryWrapper<ViewCategory> wrapper = new QueryWrapper<>();
        //组态图分类不区分项目，仅组态图区分
//        if (null != projectId) {
//            wrapper.eq("projectId", projectId);
//        }
        List<ViewCategory> categories = viewCategoryMapper.selectList(wrapper);
        if(view)
        {
            return GetParentViewTreeNode(categories, -1,projectId);
        }
        else
            return GetParentTreeNode(categories,-1);

    }



    private List<ViewCategoryTreeVo> GetParentTreeNode(List<ViewCategory> categories, Integer parentId) {
        List<ViewCategoryTreeVo> categoryTreeVos = categories.stream().filter(category -> category.getParentID().equals(parentId) )
                .map(category ->
                        ViewCategoryTreeVo.builder()
                                .id(category.getID())
                                .name(category.getName())
                                .children(GetParentTreeNode(categories, category.getID()))
                                .build()
                ).collect(Collectors.toList());
        return categoryTreeVos;
    }

    private List<ViewCategoryTreeVo> GetParentViewTreeNode(List<ViewCategory> categories, Integer parentId,Integer projectId) {
        List<ViewCategoryTreeVo> categoryTreeVos = categories.stream().filter(category -> category.getParentID().equals(parentId) )
                .map(category ->
                        {
                            List<ViewCategoryTreeVo> voList = diagramFileService.lambdaQuery().eq(DiagramFile::getCategoryId, category.getID())
                                    .and(item -> item.eq(DiagramFile::getProjectId, projectId))
                                    .list()
                                    .stream().map(diagram -> ViewCategoryTreeVo.builder().id(diagram.getId())
                                            .type(2).name(diagram.getName()).build()).collect(Collectors.toList());

                            voList.addAll(GetParentTreeNode(categories, category.getID()));
                            return ViewCategoryTreeVo.builder()
                                    .id(category.getID())
                                    .name(category.getName())
                                    .type(1)
                                    .children(voList)
                                    .build();
                        }
                ).collect(Collectors.toList());
        return categoryTreeVos;
    }


}
