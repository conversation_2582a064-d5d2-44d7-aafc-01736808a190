package com.gh.resource.model.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * Description:
 * User: zhangkeguang
 * Date: 2023-12-28-14:48
 */

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("ghdm_assetspath")
@ApiModel(value = "AssetsPath",description = "资源路径表")
public class AssetsPath {
    @TableId(type = IdType.AUTO)
    private  Integer id;

    private String path;


    private Integer deviceId;

    private Integer projectId;

    private Integer productId;

    private Integer tslId;


    @TableField(fill = FieldFill.INSERT)
    private Date createTime;


    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
}
