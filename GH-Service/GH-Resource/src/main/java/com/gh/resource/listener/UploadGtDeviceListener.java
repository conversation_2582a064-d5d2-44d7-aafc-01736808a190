package com.gh.resource.listener;

import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.exception.ExcelDataConvertException;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.gh.common.redis.RedisHelper;
import com.gh.resource.model.entity.Device;
import com.gh.resource.service.DeviceService;
import com.gh.resource.service.DeviceStandardService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;


/**
 * Description:国铁定制 设备excel批量添加导入
 * User: zhangkeguang
 * Date: 2021-12-01 16:19:38
 */

@Slf4j
@Component
public class UploadGtDeviceListener extends AnalysisEventListener<Device> {

    @Autowired
    private DeviceService deviceService;


    @Autowired
    private DeviceStandardService deviceStandardService;


    @Autowired
    private RedisHelper redisHelper;

    @Override
    @Transactional
    public void invoke(Device device, AnalysisContext analysisContext) {
        log.info("解析到一条数据:{}", JSONUtil.toJsonStr(device));
        device.setCode(StrUtil.isNotEmpty(device.getCode()) ? device.getCode() : UUID.fastUUID().toString());
        deviceService.saveOrUpdate(device, Wrappers.<Device>lambdaQuery().eq(Device::getName, device.getName()));

    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
        log.error("解析完成");
    }

    @Override
    public void onException(Exception exception, AnalysisContext context) throws Exception {
        log.error("解析失败，但是继续解析下一行:{}", exception.getMessage());
        exception.printStackTrace();
        // 如果是某一个单元格的转换异常 能获取到具体行号
        // 如果要获取头的信息 配合invokeHeadMap使用
        if (exception instanceof ExcelDataConvertException) {
            ExcelDataConvertException excelDataConvertException = (ExcelDataConvertException) exception;
            log.error("第{}行，第{}列解析异常，数据为:{}", excelDataConvertException.getRowIndex(),
                    excelDataConvertException.getColumnIndex(), excelDataConvertException.getCellData());
        }
    }
}
