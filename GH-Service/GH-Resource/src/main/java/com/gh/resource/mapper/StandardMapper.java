package com.gh.resource.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gh.resource.model.entity.Standard;

import java.util.List;

public interface StandardMapper extends BaseMapper<Standard> {

    List<Standard> getStd(Page<Standard> page,String name, Integer projectId, Integer id,Integer productId,Integer category);
}
