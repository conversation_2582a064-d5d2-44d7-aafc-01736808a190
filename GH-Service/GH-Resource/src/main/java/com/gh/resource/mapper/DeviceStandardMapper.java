package com.gh.resource.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gh.common.model.dto.DeviceStdDTO;
import com.gh.common.model.entity.DeviceStandard;
import com.gh.resource.model.dto.DeviceStdParam;
import com.gh.resource.model.dto.DeviceValDTO;
import com.gh.resource.model.vo.ProductCountVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface DeviceStandardMapper extends BaseMapper<DeviceStandard> {
    List<DeviceStandard> getDeviceStd(Integer id,@Param("deviceId") Integer deviceId,@Param("code")String code,@Param("standardId")Integer standardId);

    List<DeviceStdParam> getDeviceStdParam(@Param("deviceId")Integer deviceId);

    List<ProductCountVO> getDeviceTypeStatus(Integer projectId, List<String> typeCode,Integer areaId);


    List<DeviceValDTO> getDeviceVal(Integer projectId,Integer deviceId,String identifier,String deviceCode,String property,String projectCode,Integer areaId);


    List<DeviceStdDTO> getVariable(String identifier, Integer deviceId,List<Integer> ids);

//    List<EventStandardDTO> getEventStandard(Integer projectId,Integer id,Integer productId);
}
