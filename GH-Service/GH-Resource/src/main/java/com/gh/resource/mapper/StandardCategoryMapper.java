package com.gh.resource.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gh.common.model.vo.DeviceStandardVo;
import com.gh.common.model.vo.DeviceStdValueVo;
import com.gh.common.model.vo.DeviceVo;
import com.gh.resource.model.dto.DeviceInfoDto;
import com.gh.resource.model.dto.DeviceProjectDTO;
import com.gh.resource.model.dto.MenuCount;
import com.gh.resource.model.entity.Device;
import com.gh.resource.model.entity.StandardCategory;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;


public interface StandardCategoryMapper extends BaseMapper<StandardCategory> {




}
