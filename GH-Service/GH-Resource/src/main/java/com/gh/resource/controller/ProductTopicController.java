package com.gh.resource.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ArrayUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gh.common.exception.SystemEnumMsg;
import com.gh.common.utils.GHResponse;
import com.gh.resource.model.entity.ProductTopic;
import com.gh.resource.service.ProductTopicService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Arrays;
import java.util.List;

@RequestMapping("/topic")
@RestController
@Api(tags = "产品topic管理")
public class ProductTopicController {

    @Autowired
    private ProductTopicService service;
    @PostMapping
    @ApiOperation("添加topic")
    public GHResponse Add(@RequestBody @Valid ProductTopic topic)
    {
        //根据名称判断是否存在
        List<ProductTopic> list = service.lambdaQuery().eq(ProductTopic::getTopic,topic.getTopic())
                .list();
        if(CollUtil.isNotEmpty(list))
        {
            return GHResponse.failed(SystemEnumMsg.CREATE_ERROR.msg());
        }
        boolean save = service.save(topic);
        if(save)
        {
            return GHResponse.ok();
        }
        return GHResponse.failed(SystemEnumMsg.CREATE_ERROR.msg());
    }

    @PutMapping
    @ApiOperation("更新topic")
    public GHResponse Patch(@RequestBody ProductTopic topic)
    {
        //根据名称判断是否存在
        List<ProductTopic> list = service.lambdaQuery().eq(ProductTopic::getTopic, topic.getTopic())
                .ne(ProductTopic::getId,topic.getId())
                .list();
        if(CollUtil.isNotEmpty(list))
        {
            return GHResponse.failed("名称已存在");
        }
        boolean update = service.updateById(topic);
        if(update)
        {
            return GHResponse.ok();
        }
        return GHResponse.failed(SystemEnumMsg.Update_ERROR.msg());
    }
    @DeleteMapping
    @ApiOperation("删除topic")
    public GHResponse Delete(@RequestParam Integer[] ids)
    {
        if(ArrayUtil.isNotEmpty(ids))
        {
            boolean delete = service.removeByIds(Arrays.asList(ids));
            if(delete)
            {
                service.remove(Wrappers.<ProductTopic>lambdaQuery().in(ProductTopic::getId,ids));
                return GHResponse.ok();
            }
        }

        return GHResponse.failed(SystemEnumMsg.Delete_ERROR.msg());
    }

    @GetMapping
    @ApiOperation("查询topic列表")
    public GHResponse<List<ProductTopic>> getTopicList(Integer projectId, Integer id,
                                                       Integer productId,
                                                       String name, Integer page, Integer size)
    {
        LambdaQueryChainWrapper<ProductTopic> like = service.lambdaQuery().eq(null != id, ProductTopic::getId, id)
                .eq(null != projectId, ProductTopic::getProjectId, projectId)
                .eq(null != productId, ProductTopic::getProductId, productId)
                .like(null != name, ProductTopic::getTopic, name);
        if(null != page && null != size)
        {
            Page<ProductTopic> page1 = like.page(new Page<>(page, size));
            return GHResponse.ok(page1.getRecords(),page1.getTotal());
        }
        else
        {
            List<ProductTopic> list = like.list();
            return GHResponse.ok(list,list.size());
        }

    }


}
