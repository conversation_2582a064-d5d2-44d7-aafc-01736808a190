package com.gh.resource.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.gh.common.utils.PageResult;
import com.gh.resource.model.entity.Diagram;

import java.util.List;


public interface DiagramService extends IService<Diagram> {
     PageResult<List<Diagram>> Select(Integer categoryId, Integer menuId, Integer areaId, Integer projectId , Integer page, Integer size);

     boolean update(Diagram diagram);
}
