<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gh.resource.mapper.ProductStdMapper">
    <select id="getStandardByProductId" resultType="com.gh.resource.model.dto.ProductStdDTO">
        SELECT ps.*,dc.tagName as unitName,dc1.tagName as dataTypeName FROM ghdm_productstd ps
        LEFT JOIN ghops_dicitem dc on dc.id=ps.unitId
        LEFT JOIN ghops_dicitem dc1 on dc1.id=ps.dataTypeId
        LEFT JOIN ghdm_product p on ps.productId=p.id
        <where>

            <if test="identifier!=null and identifier!=''">
                and p.identifier= #{identifier}
            </if>
            <if test="name!=null and name!=''">
                and ps.name like #{name}
            </if>
            <if test="projectId!=null">
                and ps.projectId=#{projectId}
            </if>
            <if test="productId!=null">
                and ps.productId=#{productId}
            </if>
        </where>
        order by ps.orderNo asc
    </select>




    <select id="getStandard" resultType="com.gh.resource.model.dto.StandardDTO">
       select s.*  from ghdm_productstd s
       where s.productId=#{id}
       order by s.orderNo asc
    </select>


    <resultMap id="product" type="com.gh.resource.model.dto.ProductPropTreeDTO">
        <id column="id" property="id"/>
        <result column="identifier" property="productIdentifier"/>
        <result column="name" property="name"/>
        <collection property="children" column="id" select="getStandard"/>

    </resultMap>


    <select id="getProductTree" resultMap="product">
        select  id,identifier,name from ghdm_product p
        <where>

            <if test="projectId!=null">
                and p.projectId=#{projectId}
            </if>
        </where>
    </select>



</mapper>
