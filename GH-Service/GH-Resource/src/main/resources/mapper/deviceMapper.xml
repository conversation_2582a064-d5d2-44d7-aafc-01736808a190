<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gh.resource.mapper.DeviceMapper">

    <!--    设备所有信息  包含 扩展属性 设备指标-->
    <resultMap id="deviceAll" type="com.gh.common.model.vo.DeviceVo" autoMapping="true">
        <id column="id" property="id"/>
        <result column="t_name" property="deviceTypeName"/>
        <collection property="deviceItems" column="id" select="getItems" autoMapping="true">
            <id column="id" property="id"/>
        </collection>
        <collection property="deviceStandards" column="id" select="getDeviceStandards">
        </collection>
    </resultMap>

    <!--    设备基础信息-->
    <resultMap id="device" type="com.gh.common.model.vo.DeviceVo" autoMapping="true">
        <id column="id" property="id"/>
        <result column="t_name" property="productName"/>
    </resultMap>


    <select id="GetDevicesPage" resultMap="deviceAll">
        SELECT DISTINCT d.id,d.name,d.code,d.productId,d.position,d.factoryName,d.installTime,d.model,d.status,
        d.color,d.icon,d.latlng,d.useDate,
        d.ip,d.phone,d.person,d.period,d.projectId,d.areaId,d.menuId,d.departmentId,d.manualPath,d.cadPath,
        d.professionType,d.imgPath,d.parentId,
        a.name as areaName,m.name as menuName,p.name as departmentName,j.name as projectName
        ,t.name as t_name FROM GHDM_Device d
        left join ghops_area a on d.areaId=a.id
        left join ghops_menuproduct mdt on d.productId=mdt.productId and mdt.projectId=#{projectId}
        left join ghops_menu m on m.id=mdt.menuId
        LEFT JOIN ghdm_product t ON mdt.productId=t.id AND t.projectId=#{projectId}
        left join ghops_department p on p.id=d.departmentId
        left join ghops_project j on j.id=d.projectId
        <where>
            d.id is not null
            <if test="id!=null">
                and d.id=#{id}
            </if>
            <if test="name!=null and name!=''">
                and d.name like #{name}
            </if>
            <if test="code!=null and code!=''">
                and d.code=#{code}
            </if>
            <if test="projectId!=null">
                and d.projectId=#{projectId}
            </if>
            <if test="areaId!=null">
                and d.areaId=#{areaId}
            </if>
            <if test="departmentId!=null">
                and d.departmentId=#{departmentId}
            </if>
            <if test="menuId!=null">
                and mdt.menuId=#{menuId}
            </if>
            <if test="status!=null">
                and d.status=#{status}
            </if>
            <if test="parentId!=null">
                and d.parentId=#{parentId}
            </if>
            <if test="productId!=null">
                and d.productId=#{productId}
            </if>
        </where>
    </select>


    <!--    本周待报设备-->
    <select id="getMaintenanceDevice" resultMap="deviceAll">
        SELECT d.id,d.name,d.code,d.productId,d.position,d.factoryName,d.installTime,d.model,d.status,
        d.color,d.icon,d.latlng,
        d.ip,d.phone,d.person,d.period,d.projectId,d.areaId,d.menuId,d.departmentId,d.manualPath,d.cadPath,
        d.professionType,d.imgPath,d.parentId,pd.nextTime FROM ghms_plandevice pd
        LEFT JOIN ghdm_device d on pd.deviceId=d.id
        <where>
            <if test="projectId!=null">
                and d.projectId=#{projectId}
            </if>
            <if test="bt!=null">
                and pd.nextTime <![CDATA[>=]]> #{bt}
            </if>
            <if test="et!=null">
                and pd.nextTime <![CDATA[<=]]> #{et}
            </if>

            <if test="nowDate!=null">
                or pd.nextTime <![CDATA[<]]> #{nowDate}
            </if>
        </where>
    </select>

    <!--    按角色角色查询设备-->
    <select id="GetRoleDevicesPage" resultMap="deviceAll">
        SELECT DISTINCT d.id,d.name,d.code,d.productId,d.position,d.factoryName,d.installTime,d.model,d.status,
        d.color,d.icon,d.latlng,d.useDate,
        d.ip,d.phone,d.person,d.period,d.projectId,d.areaId,d.menuId,d.departmentId,d.manualPath,d.cadPath,
        d.professionType,d.imgPath,d.parentId,
        a.name as areaName,m.name as menuName,p.name as departmentName,j.name as projectName
        ,t.name as t_name
        FROM ghauth_userrole LEFT JOIN
        GHAuth_RoleDevice ON ghauth_userrole.roleId=GHAuth_RoleDevice.roleId
        LEFT JOIN GHDM_Device d ON deviceId=d.id
        left join ghops_area a on a.id=d.areaId
        left join ghops_menuproduct mdt on d.productId=mdt.productId
        left join ghops_menu m on m.id=mdt.menuId
        LEFT JOIN ghdm_product t ON mdt.productId=t.id
        left join ghops_department p on p.id=d.departmentId
        left join ghops_project j on j.id=d.projectId
        <where>
            d.id is not null
            <if test="id!=null">
                and d.id=#{id}
            </if>
            <if test="name!=null and name!=''">
                and (d.name like #{name} or d.code like #{name})
            </if>
            <if test="code!=null and code!=''">
                and d.code=#{code}
            </if>
            <if test="projectId!=null">
                and d.projectId=#{projectId}
            </if>
            <if test="areaId!=null">
                and d.areaId=#{areaId}
            </if>
            <if test="departmentId!=null">
                and d.departmentId=#{departmentId}
            </if>
            <if test="menuId!=null">
                and mdt.menuId=#{menuId}
            </if>
            <if test="status!=null">
                and d.status=#{status}
            </if>

            <if test="productId!=null">
                and d.productId=#{productId}
            </if>
            <if test="parentId!=null">
                and d.parentId=#{parentId}
            </if>

            <if test="userId!=null">
                and ghauth_userrole.userId=#{userId}
            </if>
        </where>
    </select>

    <!--    项目管理员超级管理员  设备基础信息-->
    <select id="GetDevicesBasic" resultMap="device">
        SELECT d.id,d.name,d.code,d.productId,d.position,d.factoryName,d.installTime,d.model,d.status,
        d.icon,d.color,d.latlng,d.useDate,
        d.largeSort,d.smallSort,d.sn,d.detailLocation,d.specification,d.taxpayerNumber,d.manufacturerLink,d.description,
        d.productDate,d.purchaseDate,d.cost,d.useLimit,d.ratePower,d.specialParameter,
        d.ip,d.phone,d.person,d.period,d.projectId,d.areaId,d.menuId,d.departmentId,d.manualPath,d.cadPath,
        d.professionType,d.imgPath,d.parentId,
        a.name as areaName,m.name as menuName,p.name as departmentName,j.name as projectName
        ,pt.name as t_name FROM GHDM_Device d
        LEFT JOIN ghdm_product pt ON d.projectId=pt.id
        left join ghops_area a on a.id=d.areaId
        left join ghops_menu m on m.id=d.menuId
        left join ghops_department p on p.id=d.departmentId
        left join ghops_project j on j.id=d.projectId
        <where>
            <if test="id!=null">
                and d.id=#{id}
            </if>
            <if test="name!=null and name!=''">
                and (d.name like #{name} or d.code like #{name})
            </if>
            <if test="code!=null and code!=''">
                and d.code=#{code}
            </if>
            <if test="projectId!=null">
                and d.projectId=#{projectId}
            </if>
            <if test="areaId!=null">
                and d.areaId=#{areaId}
            </if>
            <if test="departmentId!=null">
                and d.departmentId=#{departmentId}
            </if>
            <if test="menuId!=null">
                and d.menuId=#{menuId}
            </if>
            <if test="status!=null">
                and d.status=#{status}
            </if>
            <if test="parentId!=null">
                and d.parentId=#{parentId}
            </if>
            <if test="productId!=null">
                and d.productId=#{productId}
            </if>
        </where>
    </select>

    <!--    按角色角色查询设备   设备基础信息-->
    <select id="GetRoleDevicesBasic" resultMap="device">
        SELECT d.id,d.name,d.code,d.productId,d.position,d.factoryName,d.installTime,d.model,d.status,
        d.color,d.icon,d.latlng,d.useDate,
        d.largeSort,d.smallSort,d.sn,d.detailLocation,d.specification,d.taxpayerNumber,d.manufacturerLink,d.description,
        d.productDate,d.purchaseDate,d.cost,d.useLimit,d.ratePower,d.specialParameter,
        d.ip,d.phone,d.person,d.period,d.projectId,d.areaId,d.menuId,d.departmentId,d.manualPath,d.cadPath,
        d.professionType,d.imgPath,d.parentId,
        a.name as areaName,m.name as menuName,p.name as departmentName,j.name as projectName
        ,pt.`name` as t_name
        FROM ghauth_userrole LEFT JOIN
        GHAuth_RoleDevice ON ghauth_userrole.roleId=GHAuth_RoleDevice.roleId
        LEFT JOIN GHDM_Device d ON deviceId=d.id
        LEFT JOIN ghdm_product pt ON d.productId=pt.id
        left join ghops_area a on a.id=d.areaId
        left join ghops_menu m on m.id=d.menuId
        left join ghops_department p on p.id=d.departmentId
        left join ghops_project j on j.id=d.projectId
        <where>
            <if test="id!=null">
                and d.id=#{id}
            </if>
            <if test="name!=null and name!=''">
                and (d.name like #{name} or d.code like #{name})
            </if>
            <if test="code!=null and code!=''">
                and d.code=#{code}
            </if>
            <if test="projectId!=null">
                and d.projectId=#{projectId}
            </if>
            <if test="areaId!=null">
                and d.areaId=#{areaId}
            </if>
            <if test="departmentId!=null">
                and d.departmentId=#{departmentId}
            </if>
            <if test="menuId!=null">
                and d.menuId=#{menuId}
            </if>
            <if test="status!=null">
                and d.status=#{status}
            </if>

            <if test="productId!=null">
                and d.productId=#{productId}
            </if>
            <if test="parentId!=null">
                and d.parentId=#{parentId}
            </if>

            <if test="userId!=null">
                and ghauth_userrole.userId=#{userId}
            </if>
        </where>
    </select>

    <!--    指标参数  设备类型级别-->
    <select id="getStdParams" resultType="com.gh.resource.model.entity.StandardParam">
        select *
        from GHDM_StandardParam
        where standardId = #{standardId}
    </select>

    <!--查询设备扩展属性-->
    <select id="getItems" resultType="com.gh.resource.model.entity.DeviceProperty">
        select *
        from GHDM_DeviceProp
        where deviceId = #{id}
    </select>

    <!--    查询设备指标-->
    <select id="getDeviceStandards" resultType="com.gh.common.model.dto.StandardDTO">
        SELECT ps.*,
               GHDM_DeviceStandard.variable,
               dc.tagName  as dataTypeName,
               dc1.tagName as unitName
        from GHDM_DeviceStandard
                 LEFT JOIN ghdm_productstd ps on ps.id = GHDM_DeviceStandard.standardId and ps.productId = #{productId}
                 LEFT JOIN ghops_dicitem dc on dc.id = ps.dataTypeId
                 LEFT JOIN ghops_dicitem dc1 on dc1.id = ps.unitId

        where deviceId = #{id}
        order by ps.orderNo
    </select>


    <!--    小程序调用-->
    <select id="getDeviceInfo" resultType="com.gh.resource.model.dto.DeviceInfoDto">
        SELECT d.id,d.`name`,d.model,d.`code`,d.factoryName,t.`name` as productName,a.`name` as areaName,
        dp.`name` as departmentName,d.period,d.installTime,d.position,d.manualPath
        FROM ghdm_device d LEFT JOIN ghdm_product t on d.productId=t.id
        LEFT JOIN ghops_area a on a.id=d.areaId
        LEFT JOIN ghops_department dp on dp.id=d.departmentId
        <where>
            <if test="keyword!=null and keyword!=''">
                d.`name` like #{keyword} or d.factoryName like #{keyword} or a.`name` like #{keyword} or t.`name` like
                #{keyword}
            </if>
            <if test="projectId!=null">
                and d.projectId=#{projectId}
            </if>
            <if test="productId!=null">
                and d.productId=#{productId}
            </if>
            <if test="areaId!=null and areaId.size()>0">
                <foreach collection="areaId" open=" and d.areaId in (" close=")" item="id" separator=",">
                    #{id}
                </foreach>
            </if>
        </where>
    </select>

    <select id="getRoleDeviceInfo" resultType="com.gh.resource.model.dto.DeviceInfoDto">
        SELECT d.id,d.`name`,d.model,d.`code`,d.factoryName,t.`name` as productName,a.`name` as areaName,
        dp.`name` as departmentName,d.period,d.installTime,d.position,d.manualPath
        FROM ghdm_device d LEFT JOIN ghdm_product t on d.productId=t.id
        LEFT JOIN ghops_area a on a.id=d.areaId
        LEFT JOIN ghops_department dp on dp.id=d.departmentId
        LEFT JOIN ghauth_roledevice rd ON d.id=rd.deviceId
        LEFT JOIN ghauth_userrole ur on ur.roleId=rd.roleId
        <where>
            <if test="keyword!=null and keyword!=''">
                and d.`name` like #{keyword} or d.factoryName like #{keyword} or a.`name` like #{keyword} or t.`name`
                like #{keyword}
            </if>
            <if test="projectId!=null">
                and d.projectId=#{projectId}
            </if>
            <if test="userId!=null">
                and ur.userId=#{userId}
            </if>
            <if test="productId!=null">
                and d.productId=#{productId}
            </if>
            <if test="areaId!=null and areaId.size()>0">
                <foreach collection="areaId" open=" and d.areaId in (" close=")" item="id" separator=",">
                    #{id}
                </foreach>
            </if>
        </where>
    </select>


    <!--    查询单个设备指标值 无指标参数  无分页-->
    <resultMap id="deviceWithStandardValue" type="com.gh.common.model.vo.DeviceStdValueVo" autoMapping="true">
        <id column="id" property="id"></id>
        <result column="deviceName" property="name"></result>
        <collection property="deviceStandards" column="did" ofType="com.gh.common.model.dto.DeviceStandardDto"
                    autoMapping="true">
            <result column="sname" property="name"></result>
        </collection>
    </resultMap>

    <select id="getDeviceStdValue" resultMap="deviceWithStandardValue">
        SELECT d.`name` as deviceName,d.id ,ds.variable,ds.`value`,s.`name` as sname
        from ghdm_device d
        LEFT JOIN ghdm_devicestandard ds on d.id=ds.deviceId
        LEFT JOIN ghdm_productstd s on ds.standardId=s.id
        <where>

            <if test="id!=null">
                and d.id=#{id}
            </if>
        </where>
    </select>


    <!--    设备只带指标  分页-->
    <resultMap id="deviceWithStandardPage" type="com.gh.common.model.vo.DeviceStandardVo" autoMapping="true">
        <id column="id" property="id"></id>
        <collection property="deviceStandards" column="{id=id,productId=productId}" select="getDeviceStandards">
        </collection>
    </resultMap>
    <!--    左侧设备列表查询分页-->
    <select id="getDeviceStandardPage" resultMap="deviceWithStandardPage">
        SELECT d.`name`,d.id,d.code,d.icon,a.`name` as areaName,d.detailLocation,d.roomId,d.productId
        from ghdm_device d
        LEFT JOIN ghops_menuproduct mt on mt.productId=d.productId
        LEFT JOIN ghops_menu m on m.id=mt.menuId
        LEFT JOIN ghops_area a on d.areaId=a.id
        left join ghdm_product p on d.productId=p.id

        <where>
            <if test="projectId!=null">
                and d.projectId=#{projectId}
            </if>
            <if test="deviceId!=null">
                and d.id=#{deviceId}
            </if>
            <if test="areaId!=null and areaId.size()>0">
                <foreach collection="areaId" open=" and d.areaId in (" close=")" item="id" separator=",">
                    #{id}
                </foreach>
            </if>

            <if test="menuId!=null">
                and mt.menuId=#{menuId}
            </if>

            <if test="keyword!=null and keyword!=''">
                and d.name like #{keyword}
            </if>
            <if test="roomId!=null and roomId!=''">
                and d.roomId=#{roomId}
            </if>
            <if test="code!=null and code!=''">
                and p.identifier= #{code}
            </if>
        </where>
    </select>
    <!--    根据权限左侧设备列表查询分页-->
    <select id="getRoleDeviceStandardPage" resultMap="deviceWithStandardPage">
        SELECT d.`name` as name,d.id as id ,d.icon,d.code,a.`name` as areaName,d.detailLocation,d.roomId,d.productId
        FROM ghauth_userrole ur
        LEFT JOIN ghauth_roledevice rd on ur.roleid=rd.roleid
        LEFT JOIN ghdm_device d on rd.deviceid=d.id
        LEFT JOIN ghops_menuproduct mt on mt.productId=d.productId
        LEFT JOIN ghops_menu m on m.id=mt.menuId
        LEFT JOIN ghops_area a on d.areaId=a.id
        <where>
            <if test="projectId!=null">
                and d.projectId=#{projectId}
            </if>
            <if test="deviceId!=null">
                and d.id=#{deviceId}
            </if>
            <if test="areaId!=null and areaId.size()>0">
                <foreach collection="areaId" open=" and d.areaId in (" close=")" item="id" separator=",">
                    #{id}
                </foreach>
            </if>

            <!--            <if test="areaId!=null">-->
            <!--                and d.areaId=#{areaId}-->
            <!--            </if>-->

            <if test="menuId!=null">
                and mt.menuId=#{menuId}
            </if>
            <if test="userId!=null">
                and ur.userId=#{userId}
            </if>
            <if test="keyword!=null and keyword!=''">
                and d.name like #{keyword}
            </if>
            <if test="roomId!=null and roomId!=''">
                and d.roomId=#{roomId}
            </if>
            <if test="code!=null and code!=''">
                and m.`code`= #{code}
            </if>
        </where>
    </select>

    <select id="getMenuCount" resultType="com.gh.resource.model.dto.MenuCount">
        SELECT menuId ,areaId ,m.`name` as menuName,count(areaId) as count ,a.`name` as areaName FROM ghdm_device d
        LEFT JOIN ghops_menu m on d.menuId=m.id
        LEFT JOIN ghops_area a on d.areaId=a.id
        <where>
            d.projectId=2 and m.`name` is not NULL
            <if test="code!=null and code!=''">
                and m.code = #{code}
            </if>
        </where>
        GROUP BY menuId,areaId
    </select>


    <select id="getOpenDeviceList" resultType="com.gh.resource.model.dto.DeviceInfoDto">
        select d.*,a.name as areaName,a.fullName,a.fullPath,pd.name as productName,
        pd.id as productId,pd.identifier as productIdentifier from ghdm_device d
        left join ghops_project p on d.projectId=p.id
        left join ghdm_product pd on d.productId=pd.id
        left join ghops_area a on d.areaId=a.id
        where p.remove=0
        <if test="projectCode!=null and projectCode!=''">
            and p.code = #{projectCode}
        </if>
        <if test="identifier!=null and identifier!=''">
            and pd.identifier = #{identifier}
        </if>
        <if test="code!=null and code!=''">
            and d.code = #{code}
        </if>
        <if test="id!=null">
            and d.id = #{id}
        </if>
        <if test="areaId!=null">
            and d.areaId = #{areaId}
        </if>
    </select>


    <select id="getDeviceProjectList" resultType="com.gh.resource.model.dto.DeviceProjectDTO">
        select  id,name,code from  ghops_project where code=#{code}
    </select>

    <select id="getDeviceNameInfo" resultType="com.gh.common.model.dto.DevSimpleDTO">
        SELECT d.`name` as deviceName,d.productId,p.`name` as productName,ps.`name` as propertyName,d.projectId FROM ghdm_device d
        LEFT JOIN ghdm_product p on d.productId=p.id
          LEFT JOIN ghdm_productstd ps on p.id=ps.productId
        WHERE d.id=#{deviceId} and ps.identifier=#{identifier}

    </select>

</mapper>
