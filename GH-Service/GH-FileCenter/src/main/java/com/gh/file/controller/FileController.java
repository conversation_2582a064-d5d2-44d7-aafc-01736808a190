package com.gh.file.controller;


import cn.hutool.core.util.StrUtil;
import com.gh.common.utils.GHResponse;
import com.gh.common.utils.PageResult;
import com.gh.file.model.vo.FileVo;
import com.gh.file.service.FileService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RequestMapping("/file")
@RestController
@Api(tags = "文件查询")
public class FileController {

    @Autowired
    private FileService fileService;

//    @Autowired
//    private StringRedisTemplate stringRedisTemplate;

    @GetMapping
    @ApiOperation("查询文件")
    public GHResponse<List<FileVo>> Selects(String name, Integer fileType, Integer projectId , Integer size, Integer page)
    {
        if(StrUtil.isNotBlank(name))
        {
            name="%"+name+"%";
        }
        PageResult<List<FileVo>> filePage = fileService.GetFilePage(null, name, fileType, page, size, null,projectId);

        return GHResponse.ok(filePage.getData(),filePage.getTotal());
    }


}
