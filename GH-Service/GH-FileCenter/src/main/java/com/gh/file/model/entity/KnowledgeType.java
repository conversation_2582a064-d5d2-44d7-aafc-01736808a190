package com.gh.file.model.entity;


import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@TableName("GHContingency_KnowledgeType")
@AllArgsConstructor
@NoArgsConstructor
@Data
public class KnowledgeType {

    @TableId(type=IdType.AUTO)
    private Integer id;

    private Integer projectId;

    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;


    private String name;

    private String description;
}
