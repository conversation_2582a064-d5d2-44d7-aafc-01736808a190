package com.gh.file.model.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * Description: 文件资料
 * User: zhangkeguang
 * Date: 2024-05-13-14:10
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("GHFile_FileData")
public class FileDataDTO {
    @TableId(type = IdType.AUTO)
    private Integer id;

    private Integer projectId;

    private String name;
    private Integer typeId;
    private String source;
    private Integer isDownload;
    private String tags;
    private String description;
    private String attachment;
    private Date updateTime;
    private Date createTime;

    private String tagName;

}
