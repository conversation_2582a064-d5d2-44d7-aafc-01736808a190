package com.gh.file.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gh.common.utils.PageResult;
import com.gh.file.mapper.FileMapper;
import com.gh.file.model.entity.File;
import com.gh.file.model.vo.FileVo;
import com.gh.file.service.FileService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class FileServiceImpl extends BaseServiceImpl<FileMapper> implements FileService {

    private final FileMapper fileMapper;

    @Override
    public PageResult<List<FileVo>> GetFilePage(Integer id, String name, Integer fileType, Integer page, Integer size, String path,Integer projectId) {
        if(null!=page&&null!=size&&page>0&&size>0)
        {
            Page<FileVo> fileVoPage = new Page<>(page, size);
            List<FileVo> fileVos = fileMapper.GetFilePage(fileVoPage, id, name, fileType,path,projectId);
            return PageResult.<List<FileVo>>builder().total((long)fileVoPage.getTotal()).data(fileVos).build();
        }
        else
        {
            List<FileVo> fileVos = fileMapper.GetFilePage(null, id, name, fileType,path,projectId);
            return PageResult.<List<FileVo>>builder().total((long)fileVos.size()).data(fileVos).build();
        }


    }

    @Override
    public boolean DeleteByPath(String path) {
        if(StrUtil.isNotBlank(path))
        {
            UpdateWrapper<File> updateWrapper = new UpdateWrapper<>();
            updateWrapper.eq("path",path);
            return fileMapper.delete(updateWrapper)>0;
        }
        return false;
    }
}
