package com.gh.file.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gh.file.config.MinioConfig;
import com.gh.file.mapper.RtdbFileMapper;
import com.gh.file.model.entity.RtdbFileEntity;
import com.gh.file.service.RtdbFileService;
import io.minio.GetPresignedObjectUrlArgs;
import io.minio.MinioClient;
import io.minio.PutObjectArgs;
import io.minio.RemoveObjectArgs;
import io.minio.http.Method;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import java.io.IOException;
import java.util.List;

@Service
public class RtdbFileServiceImpl extends ServiceImpl<RtdbFileMapper,RtdbFileEntity> implements RtdbFileService {

    @Autowired
    private MinioClient minioClient;
    @Autowired
    private RtdbFileMapper rtdbFileMapper;
    @Autowired
    private MinioConfig config;

    @Autowired
    private RtdbFileService rtdbFileService;

    @Override
    public String upload(MultipartFile file, String ioServerId, String ioServerKey, String ioServerName, Integer projectId) throws IOException {
        try {
            String extName = StrUtil.subAfter(file.getOriginalFilename(), ".", true);
            String fileName = StrUtil.subBefore(file.getOriginalFilename(), ".", true);


            minioClient.putObject(PutObjectArgs.builder().contentType(extName)
                    .stream(file.getInputStream(), file.getSize(), -1).object(file.getOriginalFilename())
                    .bucket(config.getBucket().get(1))
                    .build()

            );
            String url = minioClient.getPresignedObjectUrl(GetPresignedObjectUrlArgs.builder().bucket(config.getBucket().get(1)).object(fileName)
                    .method(Method.GET)
                    .build());
            RtdbFileEntity file1 = RtdbFileEntity.builder().createPerson("")
                    .createTime(DateUtil.date())
                    .extName(extName)
                    .name(file.getOriginalFilename())
                    .ioServerId(ioServerId)
                    .ioServerName(ioServerName)
                    .ioServerKey(ioServerKey)
                    .path(url)
                    .url("")
                    .size(file.getSize())
                    .projectId(projectId)
                    .build();
            rtdbFileMapper.insert(file1);

            return url;
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        return "";
    }

    @Override
    public void delete(String path) {
        List<RtdbFileEntity> list = rtdbFileService.lambdaQuery().eq(RtdbFileEntity::getPath, path).list();
        list.forEach(rtdbFileEntity -> {
            try {
                minioClient.removeObject(RemoveObjectArgs.builder().bucket(config.getBucket().get(1)).object(rtdbFileEntity.getName()).build());
            } catch (Exception e) {
                e.printStackTrace();
            }
            rtdbFileMapper.delete(new QueryWrapper<RtdbFileEntity>().lambda().eq(RtdbFileEntity::getPath, path));
        });

    }
}
