package com.gh.file.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.gh.common.exception.ExceptionEnum;
import com.gh.common.exception.GhCustomException;
import com.gh.common.utils.GHResponse;
import com.gh.file.listener.UploadListener;
import com.gh.file.model.dto.HomeData;
import com.gh.file.model.vo.PathVo;
import com.gh.file.service.UploadService;
import com.gh.log.annotation.OperLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@RestController
@Api(tags = "文件上传")
public class UploadController {

    @Autowired
    private UploadService uploadService;

    @Autowired
    private UploadListener listener;


    @Autowired
    private HttpServletRequest request;


    @PostMapping(value = "upload",consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @ApiOperation("文件上传")
    @OperLog(category = "文件管理",description = "文件上传")
    @PreAuthorize("hasAnyAuthority('sys_admin','sys_project_admin','sys_file_upload')")
    public GHResponse<List<String>> Upload(@RequestParam("files") MultipartFile files,
                                           @RequestParam(value = "projectId",required = false) Integer projectId,
                                           @RequestParam("fileType") Integer fileType)
    {
        String userName = (String) request.getAttribute("userName");
        List<String> paths=new ArrayList<>();
        if(null!=files)
        {
                String path = null;
                try {
                    path = uploadService.upload(files,fileType, userName==null?"":userName,projectId);
                } catch (Exception e) {
                    e.printStackTrace();
                }
                if(StrUtil.isNotBlank(path))
                {
                    paths.add(path);
                }
        }
        return GHResponse.ok(paths);
    }



    @PreAuthorize("hasAnyAuthority('sys_admin','sys_project_admin','sys_file_del')")
    @DeleteMapping("deleteFile")
    @ApiOperation("删除上传")
    @ApiImplicitParams({@ApiImplicitParam(name = "paths",value = "fileId,文件标识",dataType ="String",allowMultiple = true)})
    public GHResponse DeleteFile(@RequestParam String[] paths)
    {
        if(ArrayUtil.isNotEmpty(paths))
        {
            Arrays.asList(paths).stream().forEach(path->{
                 uploadService.delete(path);
            });
        }
        return GHResponse.ok();
    }

    @PreAuthorize("hasAnyAuthority('sys_admin','sys_project_admin','sys_file_del')")
    @DeleteMapping("delete")
    @ApiOperation("删除上传")
    @OperLog(category = "文件管理",description = "删除上传")
    @ApiImplicitParams({@ApiImplicitParam(name = "paths",value = "fileId,文件标识",dataType ="String",allowMultiple = true)})
    public GHResponse DeleteFile(@RequestBody  PathVo pathVo)
    {
        if(null!=pathVo&&ArrayUtil.isNotEmpty(pathVo.getPaths()))
        {
            pathVo.getPaths().stream().forEach(path->{
//                if(path.startsWith("http"))
//                {
//                    String[] split = path.split(":\\d{1,5}/");
//                    path=split[1];
//                }
                uploadService.delete(path);
            });
        }
        return GHResponse.ok();
    }



    @PostMapping("/upload-home")
    @ApiOperation("批量导入")
    @OperLog(category = "文件管理",description = "批量导入")

    public GHResponse Upload(@RequestParam("file") MultipartFile file) throws IOException {
        if (null == file || file.isEmpty()) {
            throw new GhCustomException(ExceptionEnum.FILE_EMPTY);
        }
        String extName = file.getOriginalFilename().split("\\.")[1];
        if (!extName.equals("xls") && !extName.equals("xlsx")) {
            throw new GhCustomException(ExceptionEnum.FILE_EXT_FORMAT);
        }
        EasyExcel.read(file.getInputStream(), HomeData.class, listener).doReadAllSync();
        return GHResponse.ok();
    }




}
