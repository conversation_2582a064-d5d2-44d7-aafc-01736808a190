<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.gh</groupId>
    <artifactId>GH-BasicInfo</artifactId>
    <version>1.0</version>
    <packaging>jar</packaging>

    <parent>
        <groupId>com.gh</groupId>
        <artifactId>GH-Service</artifactId>
        <version>1.0</version>
    </parent>

    <dependencies>

        <dependency>
            <groupId>com.github.oshi</groupId>
            <artifactId>oshi-core</artifactId>
            <version>5.7.5</version>
        </dependency>
        <dependency>
            <groupId>net.java.dev.jna</groupId>
            <artifactId>jna</artifactId>
            <version>5.8.0</version>
        </dependency>
        <dependency>
            <groupId>net.java.dev.jna</groupId>
            <artifactId>jna-platform</artifactId>
            <version>5.8.0</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>net.java.dev.jna</groupId>
                    <artifactId>jna</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>net.java.dev.jna</groupId>
                    <artifactId>jna-platform</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>

        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>com.gh</groupId>
            <artifactId>GH-Log</artifactId>
            <version>1.0</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.kafka</groupId>
            <artifactId>spring-kafka</artifactId>
        </dependency>


        <dependency>
            <groupId>com.gh</groupId>
            <artifactId>GH-Common-Exception</artifactId>
        </dependency>
        <!--        <dependency>-->
        <!--            <groupId>com.gh</groupId>-->
        <!--            <artifactId>GH-Common-Feign</artifactId>-->
        <!--            <version>1.0</version>-->
        <!--        </dependency>-->
        <!--        <dependency>-->
        <!--            <groupId>com.gh</groupId>-->
        <!--            <artifactId>GH-Common-Utils</artifactId>-->
        <!--        </dependency>-->

        <dependency>
            <groupId>com.gh</groupId>
            <artifactId>GH-Resource-Api</artifactId>
            <version>1.0</version>
        </dependency>
        <dependency>
            <groupId>com.gh</groupId>
            <artifactId>GH-Energy-Api</artifactId>
            <version>1.0</version>
        </dependency>

        <dependency>
            <groupId>com.gh</groupId>
            <artifactId>GH-User-Api</artifactId>
            <version>1.0</version>
        </dependency>

        <dependency>
            <groupId>com.gh</groupId>
            <artifactId>GH-WS-Api</artifactId>
            <version>1.0</version>
        </dependency>

        <dependency>
            <groupId>com.gh</groupId>
            <artifactId>GH-Common-Model</artifactId>
        </dependency>

        <dependency>
            <groupId>com.gh</groupId>
            <artifactId>GH-Common-Redis</artifactId>
        </dependency>

        <dependency>
            <groupId>com.gh</groupId>
            <artifactId>GH-Common-Model</artifactId>
        </dependency>
        <dependency>
            <groupId>com.gh</groupId>
            <artifactId>GH-Common-Filter</artifactId>
        </dependency>
        <dependency>
            <groupId>io.lettuce</groupId>
            <artifactId>lettuce-core</artifactId>
            <version>5.3.6.RELEASE</version>
        </dependency>


        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-alibaba-nacos-config</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-alibaba-nacos-discovery</artifactId>
        </dependency>

<!--        <dependency>-->
<!--            <groupId>net.logstash.logback</groupId>-->
<!--            <artifactId>logstash-logback-encoder</artifactId>-->
<!--            <version>5.3</version>-->
<!--        </dependency>-->


<!--        <dependency>-->
<!--            <groupId>de.codecentric</groupId>-->
<!--            <artifactId>spring-boot-admin-starter-client</artifactId>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>org.springframework.cloud</groupId>-->
<!--            <artifactId>spring-cloud-starter-zipkin</artifactId>-->
<!--        </dependency>-->



        <!-- https://mvnrepository.com/artifact/org.apache.commons/commons-exec -->
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-exec</artifactId>
            <version>1.3</version>
        </dependency>

<!--        &lt;!&ndash; ShardingSphere JDBC &ndash;&gt;-->
<!--        <dependency>-->
<!--            <groupId>org.apache.shardingsphere</groupId>-->
<!--            <artifactId>shardingsphere-jdbc-core-spring-boot-starter</artifactId>-->
<!--            <version>5.1.1</version>-->
<!--        </dependency>-->


<!--        &lt;!&ndash; Curator Framework &ndash;&gt;-->
<!--        <dependency>-->
<!--            <groupId>org.apache.curator</groupId>-->
<!--            <artifactId>curator-framework</artifactId>-->
<!--            <version>5.5.0</version>-->
<!--        </dependency>-->

<!--        &lt;!&ndash; Curator Recipes &ndash;&gt;-->
<!--        <dependency>-->
<!--            <groupId>org.apache.curator</groupId>-->
<!--            <artifactId>curator-recipes</artifactId>-->
<!--            <version>5.5.0</version>-->
<!--        </dependency>-->

<!--        &lt;!&ndash; 如果使用 Curator Client &ndash;&gt;-->
<!--        <dependency>-->
<!--            <groupId>org.apache.curator</groupId>-->
<!--            <artifactId>curator-client</artifactId>-->
<!--            <version>5.5.0</version>-->
<!--        </dependency>-->



    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>2.4.5</version>
<!--                <configuration>-->
<!--                    <executable>true</executable>-->
<!--                </configuration>-->
            </plugin>

<!--            <plugin>-->
<!--                <groupId>com.spotify</groupId>-->
<!--                <artifactId>docker-maven-plugin</artifactId>-->
<!--                <version>1.2.2</version>-->
<!--                <executions>-->
<!--                    <execution>-->
<!--                        <id>build-image</id>-->
<!--                        <phase>package</phase>-->
<!--                        <goals>-->
<!--                            <goal>build</goal>-->
<!--                        </goals>-->
<!--                    </execution>-->
<!--                </executions>-->
<!--                <configuration>-->
<!--                    <serverId>docker</serverId>-->
<!--                    <dockerHost>http://*************:2375</dockerHost>-->
<!--                    <imageName>*************:26667/njgh/basic:v1</imageName>-->
<!--                    <dockerDirectory>src/main/docker</dockerDirectory>-->
<!--                    <resources>-->
<!--                        <resource>-->
<!--                            <targetPath>/</targetPath>-->
<!--                            <directory>${project.build.directory}</directory>-->
<!--                            <include>GH-BasicInfo-1.0.jar</include>-->
<!--                        </resource>-->
<!--                    </resources>-->
<!--                    <registryUrl>*************:26667/njgh</registryUrl>-->
<!--                </configuration>-->
<!--            </plugin>-->
        </plugins>
    </build>


</project>
