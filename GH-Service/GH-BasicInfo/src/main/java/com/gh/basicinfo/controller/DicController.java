package com.gh.basicinfo.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ArrayUtil;
import com.gh.basicinfo.model.entity.Dic;
import com.gh.basicinfo.service.DicService;
import com.gh.common.exception.ExceptionEnum;
import com.gh.common.exception.GhCustomException;
import com.gh.common.exception.SystemEnumMsg;
import com.gh.common.utils.GHResponse;
import com.gh.common.utils.PageResult;
import com.gh.log.annotation.OperLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Arrays;
import java.util.List;

@RequestMapping("/dic")
@RestController
@Api(tags = "数据字典管理")
public class DicController {

    @Autowired
    private DicService dicService;

    @PostMapping
    @ApiOperation("添加数据字典")
    @OperLog(category = "数据字典",description = "添加数据字典")
    @PreAuthorize("hasAnyAuthority('sys_admin','sys_project_admin','sys_dic_add')")
    public GHResponse Add(@RequestBody @Valid Dic dic)
    {
        List<Dic> dics = dicService.lambdaQuery().eq(Dic::getProjectId, dic.getProjectId())
                .and(wrapper->wrapper.eq(Dic::getName,dic.getName()).or()
                        .eq(Dic::getCode,dic.getCode()))
                .list();
        if(CollUtil.isNotEmpty(dics))
        {
            throw new GhCustomException(ExceptionEnum.DIC_CODE_EXIST);
        }
        boolean insert = dicService.save(dic);
        if(insert)
        {
            return GHResponse.ok(null, SystemEnumMsg.CREATE_SUCCESS.msg());
        }
        return GHResponse.ok(SystemEnumMsg.CREATE_ERROR.msg());
    }
    @PutMapping
    @ApiOperation("更新数据字典")
    @OperLog(category = "数据字典",description = "更新数据字典")
    @PreAuthorize("hasAnyAuthority('sys_admin','sys_project_admin','sys_dic_update')")
    public GHResponse Patch(@RequestBody Dic dic)
    {
        List<Dic> dics = dicService.lambdaQuery().eq(Dic::getProjectId, dic.getProjectId())
                .ne(Dic::getId,dic.getId())
                .and(wrapper->wrapper.eq(Dic::getName,dic.getName()).or()
                        .eq(Dic::getCode,dic.getCode()))
                .list();
        if(CollUtil.isNotEmpty(dics))
        {
            throw new GhCustomException(ExceptionEnum.DIC_CODE_EXIST);
        }
        if(dic.getId()>0)
        {
            boolean update = dicService.updateById(dic);
            if(update)
            {
                return GHResponse.ok();
            }
        }
        return GHResponse.failed(SystemEnumMsg.Update_ERROR.msg());
    }

    @DeleteMapping
    @ApiOperation("删除数据字典")
    @OperLog(category = "数据字典",description = "删除数据字典")
    @PreAuthorize("hasAnyAuthority('sys_admin','sys_project_admin','sys_dic_del')")
    public GHResponse Delete(@RequestParam("ids") Integer[] ids)
    {

        if(ArrayUtil.isNotEmpty(ids))
        {
            boolean delete = dicService.removeByIds(Arrays.asList(ids));
            if(delete)
            {
                return GHResponse.ok();
            }
        }

        return GHResponse.failed(SystemEnumMsg.Delete_ERROR.msg());
    }

    @GetMapping("{id}")
    @ApiOperation("查询数据字典")
    public GHResponse<List<Dic>> Select(@PathVariable(value = "id",required = true) Integer id)
    {
        PageResult<List<Dic>> typePageListResult = dicService.Select(id, null,null, null, null, null);
        return GHResponse.ok(typePageListResult.getData(),typePageListResult.getTotal());
    }

    @GetMapping
    @ApiOperation("查询数据字典")
    public GHResponse<List<Dic>> Selects(String code, String name, Integer type, Integer page, Integer size)
    {

        PageResult<List<Dic>> typePageListResult = dicService.Select(null,name,code, type, page, size);
        return GHResponse.ok(typePageListResult.getData(),typePageListResult.getTotal());
    }
}
