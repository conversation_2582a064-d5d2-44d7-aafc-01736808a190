package com.gh.basicinfo.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gh.basicinfo.mapper.MenuMapper;
import com.gh.basicinfo.model.entity.Menu;
import com.gh.basicinfo.model.vo.MenuTreeVo;
import com.gh.basicinfo.service.MenuService;
import com.gh.common.utils.GHResponse;
import com.gh.common.utils.PageResult;
import com.gh.common.utils.RequestUtil;
import com.gh.user.feign.client.UserFeignClient;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Service
@AllArgsConstructor
public class MenuServiceImpl extends ServiceImpl<MenuMapper, Menu> implements MenuService {

    private final MenuMapper menuMapper;
    private final UserFeignClient userFeignClient;

    @Override
    public PageResult<List<Menu>> Select(Integer id, String keyword, String name, Integer projectId, Integer parentId, Boolean isFunMenu,
                                         Boolean configFlag, Integer page, Integer size, Boolean isWeChatMenu, Boolean isHomeMenu, Boolean isPcMenu, Boolean enable) {

        if (StrUtil.isNotBlank(keyword)) {
            keyword = "%" + keyword + "%";
        }
        List<Menu> menus;
        if (null != page && null != size) {
            Page<Menu> menuPage = new Page<>(page, size);
            if (RequestUtil.Admin() || RequestUtil.ProjectAdmin()) {
                menus = menuMapper.GetAdminRoleMenu(menuPage, id, keyword, name, projectId, parentId, configFlag, isWeChatMenu, isHomeMenu, isPcMenu,enable,null);
            } else {
                GHResponse<List<Integer>> userRole = userFeignClient.getUserRole(RequestUtil.CurrentProject(), RequestUtil.getUserId());
                if (userRole != null && userRole.getData().size() == 0) {
                    userRole.getData().add(-1);
                }
                menus = menuMapper.GetRoleMenu(menuPage, id, keyword, name, projectId, parentId, userRole.getData(), configFlag, isWeChatMenu, isHomeMenu, isPcMenu,enable);
            }
            return PageResult.<List<Menu>>builder().data(menus).total(menuPage.getTotal()).build();
        } else {
            if (RequestUtil.Admin() || RequestUtil.ProjectAdmin()) {
                menus = menuMapper.GetAdminRoleMenu(null, id, keyword, name, projectId, parentId, configFlag, isWeChatMenu, isHomeMenu, isPcMenu,enable,null);
            } else {
                GHResponse<List<Integer>> userRole = userFeignClient.getUserRole(RequestUtil.CurrentProject(), RequestUtil.getUserId());
                if (userRole != null && userRole.getData().size() == 0) {
                    userRole.getData().add(-1);
                }
                menus = menuMapper.GetRoleMenu(null, id, keyword, name, projectId, parentId, userRole.getData(), configFlag, isWeChatMenu, isHomeMenu, isPcMenu,enable);
            }
            return PageResult.<List<Menu>>builder().data(menus).total(menus.size()).build();
        }
    }

    @Override
    public List<MenuTreeVo> SelectTree(Integer id, String keyword, String name, Boolean isFunMenu, Integer projectId, Integer parentId,
                                       Boolean configFlag,
                                       String code,
                                       List<Integer> menuType,
                                       Boolean isWeChatMenu,
                                       Boolean isHomeMenu, Boolean isPcMenu, Boolean enable,String projectCode) {
        List<Menu> menuListPage;
        if (RequestUtil.Admin() || RequestUtil.ProjectAdmin()) {
            menuListPage = menuMapper.GetAdminRoleMenu(null, id, keyword, name, projectId, parentId, configFlag, isWeChatMenu, isHomeMenu, isPcMenu,enable,projectCode);
        } else {
            GHResponse<List<Integer>> userRole = userFeignClient.getUserRole(RequestUtil.CurrentProject(), RequestUtil.getUserId());
            if (userRole != null && userRole.getData().size() == 0) {
                userRole.getData().add(-1);
            }
            menuListPage = menuMapper.GetRoleMenu(null, id, keyword, name, projectId, parentId, userRole.getData(), configFlag, isWeChatMenu, isHomeMenu, isPcMenu,enable);
        }

//        PageResult<List<Menu>> menuListPage = Select(id, keyword, name, projectId, parentId, isFunMenu, configFlag, null, null, isWeChatMenu, isHomeMenu, isPcMenu,enable);
        List<MenuTreeVo> list = GetParentTreeNode(menuListPage, null == parentId ? 0 : parentId, menuType == null ? Arrays.asList(1, 2, 3) : menuType, null);
        if (StrUtil.isNotEmpty(code)) {
            return list.stream().filter(menuTreeVo -> menuTreeVo.getCode() != null && menuTreeVo.getCode().equalsIgnoreCase(code)).collect(Collectors.toList());
        } else {
            return list;
        }
    }

    private List<MenuTreeVo> GetParentTreeNode(List<Menu> menus, Integer parentId, List<Integer> menuType, MenuTreeVo menuTreeVo) {
        List<MenuTreeVo> areaList = menus.stream().filter(menu -> menu != null && menu.getParentId().equals(parentId) && menuType.contains(menu.getMenuType()))
                .map(menu -> {
                            MenuTreeVo treeVo = MenuTreeVo.builder()
                                    .id(menu.getId())
                                    .name(menu.getName())
                                    .parentId(menu.getParentId())
                                    .path(menu.getPath())
                                    .icon(menu.getIcon())
                                    .fullName(menu.getFullName())
                                    .createTime(menu.getCreateTime())
                                    .menuType(menu.getMenuType())
                                    .projectId(menu.getProjectId())
                                    .orderNo(menu.getOrderNo())
                                    .authId(menu.getAuthId())
                                    .shortcut(menu.getShortcut())
                                    .component(menu.getComponent())
                                    .diagramId(menu.getDiagramId())
                                    .productId(menu.getProductId())
                                    .model(menu.getModel())
                                    .code(menu.getCode())
                                    .color(menu.getColor())
                                    .panel(menu.getPanel())
                                    .imgName(menu.getImgName())
                                    .hideCondition(menu.getHideCondition())
                                    .showCondition(menu.getShowCondition())
                                    .cmd(menu.getCmd())
                                    .ar(menu.getAr())
                                    .vr(menu.getVr())
                                    .isHomeMenu(menu.getIsHomeMenu())
                                    .isPcMenu(menu.getIsPcMenu())
                                    .isWeChatMenu(menu.getIsWeChatMenu())
                                    .showFloor(menu.getShowFloor())
                                    .enable(menu.getEnable())
                                    .fullPath(menu.getParentId() == 0 ? menu.getId().toString() : menuTreeVo.getFullPath() + "|" + menu.getId())
                                    .build();
                            treeVo.setChildren(GetParentTreeNode(menus, menu.getId(), menuType, treeVo));
                            return treeVo;
                        }

                ).collect(Collectors.toList());
        return areaList;
    }
}
