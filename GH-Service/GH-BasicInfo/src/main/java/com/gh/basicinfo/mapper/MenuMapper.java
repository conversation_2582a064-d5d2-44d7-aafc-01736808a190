package com.gh.basicinfo.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gh.basicinfo.model.entity.Menu;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface MenuMapper extends BaseMapper<Menu> {
   List<Menu> GetAdminRoleMenu(
           Page page,
           @Param("id") Integer id,
           @Param("keyword") String keyword,
           @Param("name") String name,
           @Param("projectId") Integer projectId,
           @Param("parentId") Integer parentId,
           @Param("configFlag") Boolean configFlag,
           @Param("isWeChatMenu") Boolean isWeChatMenu,
           @Param("isHomeMenu") Boolean isHomeMenu,
           @Param("isPcMenu") Boolean isPcMenu,Boolean enable,String projectCode);
   List<Menu> GetRoleMenu(
           Page page,
           @Param("id") Integer id,
           @Param("keyword") String keyword,
           @Param("name") String name,
           @Param("projectId") Integer projectId,
           @Param("parentId") Integer parentId,
           @Param("roleId") List<Integer> roleId,
           @Param("configFlag") Boolean configFlag,
           @Param("isWeChatMenu") Boolean isWeChatMenu,
           @Param("isHomeMenu") Boolean isHomeMenu,
           @Param("isPcMenu") Boolean isPcMenu,Boolean enable);
}
