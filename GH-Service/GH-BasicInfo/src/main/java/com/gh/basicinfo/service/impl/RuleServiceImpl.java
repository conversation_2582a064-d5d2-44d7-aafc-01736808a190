package com.gh.basicinfo.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gh.basicinfo.mapper.RuleMapper;
import com.gh.basicinfo.model.entity.Rule;
import com.gh.basicinfo.service.RuleService;
import com.gh.common.utils.PageResult;
import org.springframework.stereotype.Service;

import java.util.List;


@Service
public class RuleServiceImpl extends ServiceImpl<RuleMapper, Rule> implements RuleService {


    @Override
    public PageResult<List<Rule>> getRules(Integer id,Integer projectId ,String name, Integer page, Integer size) {
        LambdaQueryChainWrapper<Rule> query = lambdaQuery();
        if(StrUtil.isNotEmpty(name))
        {
            query.like(Rule::getName,"%"+name+"%");
        }
        if(null!=id)
        {
            query.eq(Rule::getId,id);
        }
        if(null!=projectId)
        {
            query.eq(Rule::getProjectId,projectId);
        }
        if(PageResult.isPage(page,size))
        {
            Page<Rule> page1 = query.page(new Page<Rule>(page, size));
            return PageResult.<List<Rule>>builder()
                    .data(page1.getRecords())
                    .total(page1.getTotal())
                    .build();
        }
        else
        {
            List<Rule> rules = query.list();
            return PageResult.<List<Rule>>builder()
                    .data(rules)
                    .total(rules.size())
                    .build();
        }


    }
}
