<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gh.job.mapper.StrategyRepository">

    <select id="selectVar" resultType="com.gh.job.model.dto.GroupVarDto">
        select ghscene_groupvar.*,ghdm_devicestandard.variable ,ghdm_productstd.`name` as
        standardName,ghdm_device.`name`,
        ghdm_device.id as deviceId,ghdm_device.productId,ghdm_productstd.identifier
        from ghscene_groupvar left join ghdm_devicestandard
        on ghscene_groupvar.deviceStandardId=ghdm_devicestandard.id
        LEFT JOIN ghdm_productstd on ghdm_devicestandard.standardId=ghdm_productstd.id
        LEFT JOIN ghdm_device ON ghdm_devicestandard.deviceId=ghdm_device.id
        <where>
            <if test="id!=null">
                ghscene_groupvar.groupId=#{id}
            </if>
        </where>
    </select>

    <resultMap id="groupMap" type="com.gh.job.model.dto.GroupDto">
        <id column="id" property="id"></id>
        <result column="name" property="name"></result>
        <result column="type" property="type"></result>
        <result column="startVal" property="startVal"></result>
        <result column="endVal" property="endVal"></result>
        <result column="delay" property="delay"></result>
        <collection property="groupVars" column="id" select="selectVar"></collection>
    </resultMap>


    <select id="selectGroup" resultMap="groupMap">
        select ghscene_group.*,startVal,endVal,delay from ghscene_group
        LEFT JOIN GHOPS_StrategyGroup on ghscene_group.id=GHOPS_StrategyGroup.groupId
        <where>
            ghscene_group.status=1 and GHOPS_StrategyGroup.type =1
            <if test="id!=null">
                and GHOPS_StrategyGroup.strategyId=#{id}
            </if>
        </where>
    </select>


    <resultMap id="strategyMap" type="com.gh.job.model.dto.StrategyDto">
        <id column="id" property="id"></id>
        <collection property="startGroup" column="id" select="selectGroup"></collection>
        <!-- <collection property="endGroup" column="id" select="selectGroup1"></collection> -->
    </resultMap>

    <select id="select" resultMap="strategyMap">
        SELECT GHOPS_Strategy.* FROM GHOPS_Strategy
        where GHOPS_Strategy.status=1
        <if test="projectId!=null">
            and GHOPS_Strategy.projectId=#{projectId}
        </if>
        <if test="id!=null">
            and GHOPS_Strategy.id=#{id}
        </if>
        <if test="tag!=null">
            and GHOPS_Strategy.tag = #{tag}
        </if>
        <if test="menuId!=null">
            and GHOPS_Strategy.menuId=#{menuId}
        </if>
        <if test="name!=null">
            and GHOPS_Strategy.name like #{name}
        </if>
        <if test="ids!=null and ids!=''">
            and GHOPS_Strategy.id in ${ids}
        </if>


    </select>


    <select id="getStrategySchedule" resultMap="strategyMap">
        SELECT * from GHOPS_StrategySchedule ss
        <where>
            <if test="projectId!=null">
                and ss.projectId=#{projectId}
            </if>
        </where>
    </select>


    <select id="getStrategyModeList" resultType="com.gh.job.model.entity.StrategyMode">
        SELECT * from GHOPS_StrategyMode sm
        where sm.status=1

        <if test="projectId!=null">
            and sm.projectId=#{projectId}
        </if>

    </select>


    <select id="getStrategyModeDevList" resultType="com.gh.job.model.dto.StrategyModeDevDTO">
        SELECT sme.varVal,
        ds.deviceId,
        d.productId,
        ds.variable,
        ps.identifier,
        sme.strategyModeId,
        sme.startTime,
        sme.endTime,p.protocol,d.`code` as deviceCode,d.name as deviceName
        FROM ghops_strategymode sm
        LEFT JOIN ghops_strategymodegroup smg on smg.strategyModeId = sm.id
        LEFT JOIN ghscene_groupvar gr on gr.groupId = smg.groupId
        LEFT JOIN ghdm_devicestandard ds on ds.id = gr.deviceStandardId
        LEFT JOIN ghdm_device d on d.id = ds.deviceId
        LEFT JOIN ghdm_productstd ps on ps.id = ds.standardId
        LEFT JOIN ghdm_product p on p.id=d.productId
        LEFT JOIN ghops_strategymodeevent sme on sme.groupId = smg.groupId
        <if test="id!=null">
            and sme.strategyModeId=#{id}
        </if>
       <if test="bt!=null">
            and sme.startTime &gt;= #{bt}
        </if>
        <if test="et!=null">
            and sme.endTime &lt;= #{et}
        </if>

    </select>


</mapper>
