package com.gh.job.handler;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.gh.common.model.entity.DeviceHealth;
import com.gh.common.model.vo.FieldVO;
import com.gh.common.model.vo.TableVo;
import com.gh.common.redis.RedisHelper;
import com.gh.job.mapper.GHEnergyDeviceMapper;
import com.gh.job.model.dto.DeviceVar;
import com.gh.job.service.DeviceHealthService;
import com.gh.tdengine.feign.client.TdengineFeignClient;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

@Slf4j
@Component
public class DeviceStatusJob {

    @Autowired
    private RedisHelper redisHelper;

    @Autowired
    private GHEnergyDeviceMapper mapper;


    @Autowired
    private DeviceHealthService service;


    @Autowired
    private TdengineFeignClient tdengineFeignClient;







    @XxlJob("device_health")
    public ReturnT<String> execute(String param) {
        try {
            if (StrUtil.isNotEmpty(param)) {
                Integer projectId = Convert.toInt(param, 2);
                //查询该项目下的所有设备变量
                Map<String, List<List<FieldVO>>> map = new HashMap<>();
                //tags
                Map<String, List<FieldVO>> tagMap = new HashMap<>();

                DateTime now = DateUtil.date();
                //校正时间 支持 10 20 30分钟抄表触发
                int min = now.minute() / 10 * 10;
                now.setField(DateField.MINUTE, min).setField(DateField.SECOND, 0).setField(DateField.MILLISECOND, 0);



                List<DeviceVar> deviceVars = mapper.getDeviceVariable(projectId);
                if (CollUtil.isNotEmpty(deviceVars)) {
                    deviceVars
                            .stream()
                            .filter(deviceVar ->StrUtil.isNotEmpty(deviceVar.getIdentifier())&& deviceVar.getIdentifier().equalsIgnoreCase("runStatus"))
                            .forEach(deviceVar -> {
                        if (StrUtil.isNotEmpty(deviceVar.getVariable())) {
                            String value = redisHelper.HashGetFields(14, "var:" + deviceVar.getVariable(), "Value");
                            if (StrUtil.isNotEmpty(value) && value.equalsIgnoreCase("1")) {

                                if (!map.containsKey("DS" + deviceVar.getId())) {
                                    map.put("DS" + deviceVar.getId(), CollUtil.newArrayList());
                                }
                                if (!tagMap.containsKey("DS" + deviceVar.getId())) {
                                    List<FieldVO> tags = CollUtil.newArrayList();
                                    tags.add(FieldVO.builder().name("project").type("int").size(null).value(deviceVar.getProjectId()).build());
                                    tags.add(FieldVO.builder().name("productId").type("int").size(null).value(deviceVar.getProductId()).build());
                                    tags.add(FieldVO.builder().name("deviceId").type("int").size(null).value(deviceVar.getId()).build());
                                    tagMap.put("DS" + deviceVar.getId(), tags);
                                }
                                if (StrUtil.isNotBlank(value)) {

                                    List<FieldVO> fieldVOS = new ArrayList<>();
                                    fieldVOS.add(FieldVO.builder().name("ts").value(now.getTime()).build());
                                    fieldVOS.add(FieldVO.builder().name("_value").value(Convert.toFloat(value, 0.0f)).build());
                                    fieldVOS.add(FieldVO.builder().name("variable").value(deviceVar.getVariable()).build());
                                    fieldVOS.add(FieldVO.builder().name("identifier").value(deviceVar.getIdentifier()).build());
                                    fieldVOS.add(FieldVO.builder().name("deviceName").value(deviceVar.getName()).build());
                                    fieldVOS.add(FieldVO.builder().name("areaId").value(deviceVar.getAreaId()).build());
                                    map.get("DS" + deviceVar.getId()).add(fieldVOS);
                                }

                            }
                        }

                    });

                }

                if(map.size()>0)
                {
                    map.keySet().forEach(key -> {
                        TableVo tableVo = TableVo.builder()
                                .tableName(key)
                                .superTableName("device_status")
                                .dataBaseName("history")
                                .tags(tagMap.get(key))
                                .fields(Arrays.asList("ts", "_value", "variable", "identifier", "deviceName","areaId"))
                                .data(map.get(key))
                                .build();
                        tdengineFeignClient.insert(tableVo);
                    });
                }


            }
        } catch (Exception e) {
            log.error(e.getMessage());
            e.printStackTrace();
        } finally {
            return ReturnT.SUCCESS;
        }
    }


    public void initTime() {
        service.lambdaUpdate().set(DeviceHealth::getLastTime, DateUtil.date()).update();
//        List<DeviceHealth> list = service.lambdaQuery().list();
//        if(CollUtil.isNotEmpty(list))
//        {
//            service.lambdaUpdate().set(DeviceHealth::getLastTime,DateUtil.date()).update();
//        }
    }
}
