package com.gh.maintenance.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.gh.common.model.dto.FCRecordDTO;
import com.gh.common.utils.PageResult;
import com.gh.maintenance.model.dto.WeekFcCount;
import com.gh.maintenance.model.entity.FCRecord;

import java.util.Date;
import java.util.List;

public interface FCRecordService extends IService<FCRecord> {

    PageResult<List<FCRecordDTO>> getFCRecord(String keyword, Integer projectId, Date bt, Date et, Integer page, Integer size, Integer deviceId,Boolean delay);

    List<WeekFcCount> GetWeekCount(Date bt, Date et, Integer projectId);


}
