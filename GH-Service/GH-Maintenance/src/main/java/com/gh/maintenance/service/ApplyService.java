package com.gh.maintenance.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.gh.common.utils.PageResult;
import com.gh.maintenance.model.dto.ApplyDto;
import com.gh.maintenance.model.entity.Apply;

import java.util.List;

public interface ApplyService extends IService<Apply> {

    boolean apply(Apply apply);

    boolean Update(Integer status,Integer id);

    PageResult<List<ApplyDto>> getApply(Integer status, Integer staffId, Integer projectId , Integer id, String name,String instanceId ,Integer page, Integer size);
}
