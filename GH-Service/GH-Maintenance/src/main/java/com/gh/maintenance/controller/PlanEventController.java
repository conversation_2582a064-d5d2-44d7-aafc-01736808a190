package com.gh.maintenance.controller;

import cn.hutool.core.util.ArrayUtil;
import com.gh.common.exception.SystemEnumMsg;
import com.gh.common.model.entity.Plan;
import com.gh.common.utils.GHResponse;
import com.gh.log.annotation.OperLog;
import com.gh.maintenance.model.entity.PlanEvent;
import com.gh.maintenance.service.PlanEventService;
import com.gh.maintenance.service.PlanService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Arrays;

@RequestMapping("/plan-event")
@RestController
@Api(tags = "巡检计划事件")
public class PlanEventController {

    @Autowired
    private PlanEventService service;
    @Autowired
    private PlanService planService;

    @PostMapping
    @ApiOperation("增加巡检事件")
    @OperLog(category = "巡检计划事件",description = "增加巡检事件")
    public GHResponse Add(@RequestBody @Valid PlanEvent planEvent)
    {
        Plan plan = planService.lambdaQuery().eq(Plan::getId, planEvent.getPlanId()).one();
        if(null!=plan)
        {
            //单次执行
            if(plan.getType().equals(1))
            {

            }
        }
        boolean insert = service.save(planEvent);
        if(insert)
        {
            return GHResponse.ok(null, SystemEnumMsg.CREATE_SUCCESS.msg());
        }
        return GHResponse.failed(SystemEnumMsg.CREATE_ERROR.msg());
    }
    @PutMapping
    @ApiOperation("更新巡检事件")
    @OperLog(category = "巡检计划事件",description = "更新巡检事件")
    public GHResponse Patch(@RequestBody @Valid PlanEvent planEvent)
    {
        boolean update = service.updateById(planEvent);
        if(update)
        {
            return GHResponse.ok();
        }
        return GHResponse.failed(SystemEnumMsg.Update_ERROR.msg());
    }
    @DeleteMapping
    @ApiOperation("删除巡检事件")
    @OperLog(category = "巡检计划事件",description = "删除巡检事件")
    public GHResponse Delete(@RequestParam Integer[] ids)
    {

        if(ArrayUtil.isNotEmpty(ids))
        {
            boolean delete = service.removeByIds(Arrays.asList(ids));
            if(delete)
            {
                return GHResponse.ok();
            }
        }

        return GHResponse.failed(SystemEnumMsg.Delete_ERROR.msg());
    }

}
