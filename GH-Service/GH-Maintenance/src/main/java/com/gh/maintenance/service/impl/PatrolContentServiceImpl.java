package com.gh.maintenance.service.impl;

import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gh.common.utils.PageResult;
import com.gh.maintenance.mapper.PatrolContentMapper;
import com.gh.maintenance.model.entity.PatrolContent;
import com.gh.maintenance.service.PatrolContentService;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class PatrolContentServiceImpl extends ServiceImpl<PatrolContentMapper, PatrolContent>
        implements PatrolContentService {

    @Override
    public PageResult<List<PatrolContent>> select(Integer deviceType, Integer projectId, Integer page, Integer size) {
        LambdaQueryChainWrapper<PatrolContent> wrapper = lambdaQuery();
        if (null != deviceType) {
            wrapper.eq(PatrolContent::getDeviceType, deviceType);
        }
        if (null != projectId) {
            wrapper.eq(PatrolContent::getProjectId, projectId);
        }
        if (PageResult.isPage(page, size)) {
            Page<PatrolContent> contentPage = new Page<>(page, size);
            Page<PatrolContent> page1 = wrapper.page(contentPage);
            return PageResult.<List<PatrolContent>>builder().data(page1.getRecords())
                    .total(page1.getTotal()).build();
        }
        {
            List<PatrolContent> list = wrapper.list();
            return PageResult.<List<PatrolContent>>builder().data(list)
                    .total(list.size()).build();
        }

    }
}
