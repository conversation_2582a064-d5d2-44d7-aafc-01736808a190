package com.gh.maintenance.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSONObject;
import com.gh.common.exception.SystemEnumMsg;
import com.gh.common.utils.GHResponse;
import com.gh.common.utils.PageResult;
import com.gh.common.utils.RequestUtil;
import com.gh.log.annotation.OperLog;
import com.gh.maintenance.model.dto.PointScanDto;
import com.gh.maintenance.model.dto.SecurityDetailRecord;
import com.gh.maintenance.model.entity.PointRecord;
import com.gh.maintenance.model.vo.PointScanVo;
import com.gh.maintenance.service.PointRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.net.MalformedURLException;
import java.net.URL;
import java.util.Date;
import java.util.List;

@RequestMapping("/patrol-record")
@RestController
@Api(tags = "巡检记录")
public class PatrolTaskRecordController {

    @Autowired
    private HttpServletResponse response;

    @Autowired
    private PointRecordService service;

    @PostMapping
    @ApiOperation("增加巡检记录")
    @OperLog(category = "巡检记录", description = "增加巡检记录")
    public GHResponse Add(@RequestBody @Valid PointScanVo pointScanVo) {
        boolean insert = service.Save(pointScanVo);
        if (insert) {
            return GHResponse.ok(null, SystemEnumMsg.CREATE_SUCCESS.msg());
        }
        return GHResponse.failed(SystemEnumMsg.CREATE_ERROR.msg());
    }


    @PostMapping("security")
    @ApiOperation("增加安保巡检记录")
    @OperLog(category = "安保巡检记录", description = "增加安保巡检记录")
    public GHResponse AddSecurityRecord(@RequestBody @Valid PointRecord pointScanVo) {

        boolean insert = service.SaveSecurity(pointScanVo);
        if (insert) {
            return GHResponse.ok(null, SystemEnumMsg.CREATE_SUCCESS.msg());
        }
        return GHResponse.failed(SystemEnumMsg.CREATE_ERROR.msg());
    }


    @GetMapping
    @ApiOperation("查询巡检记录")
    public GHResponse<List<PointScanDto>> Selects(Integer userId,
                                                  String keyword,
                                                  Integer areaId,
                                                  Integer page, Integer size,
                                                  Integer taskId,
                                                  Integer planId,
                                                  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") @RequestParam(required = false) Date bt,
                                                  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") @RequestParam(required = false) Date et,
                                                  Integer type) {
        Date begin = DateUtil.beginOfDay(DateUtil.date());
        Date end = DateUtil.endOfDay(DateUtil.date());
        if (null != type && type == 2) {
            begin = DateUtil.beginOfDay(DateUtil.date().offset(DateField.DAY_OF_MONTH, -1));
            end = DateUtil.endOfDay(DateUtil.date().offset(DateField.DAY_OF_MONTH, -1));
        }
        if (null != type && type == 3) {
            begin = DateUtil.beginOfDay(DateUtil.date().offset(DateField.DAY_OF_MONTH, -7));
            end = DateUtil.endOfDay(DateUtil.date());
        }
        if (null != type && type == 4) {
            begin = DateUtil.beginOfDay(DateUtil.date().offset(DateField.DAY_OF_MONTH, -30));
            end = DateUtil.endOfDay(DateUtil.date());
        }
        if (null != type && type == 5) {
            begin = bt;
            end = et;
        }
        PageResult<List<PointScanDto>> select = service.getRecord(areaId, keyword, taskId, userId, RequestUtil.CurrentProject(), begin, end, page, size,planId);
        return GHResponse.ok(select.getData(), select.getTotal());
    }

    @GetMapping("security")
    @ApiOperation("查询安保巡检记录---原始安保巡检记录")
    public GHResponse<List<PointScanDto>> SelectSecurityRecord(Integer userId,
                                                               String keyword,
                                                               Integer areaId,
                                                               Integer page, Integer size,
                                                               @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") @RequestParam(required = false) Date bt,
                                                               @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") @RequestParam(required = false) Date et
    ) {

        PageResult<List<PointScanDto>> select = service.getSecurityRecord(areaId, keyword, userId, RequestUtil.CurrentProject(), bt, et, page, size);
        return GHResponse.ok(select.getData(), select.getTotal());
    }


    @GetMapping("security-detail")
    @ApiOperation("查询安保巡检记录---安保巡检详细记录")
    public GHResponse<List<SecurityDetailRecord>>
    SelectSecurityDetailRecord(String keyword, Integer page, Integer size,
                               Integer type,
                               Integer planId,
                               @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") @RequestParam(required = false) Date bt,
                               @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") @RequestParam(required = false) Date et
    ) {

        Date begin = DateUtil.beginOfDay(DateUtil.date());
        Date end = DateUtil.endOfDay(DateUtil.date());
        String planDate=null;
        if (null != type && type == 2) {
            begin = DateUtil.beginOfDay(DateUtil.date().offset(DateField.DAY_OF_MONTH, -1));
            end = DateUtil.endOfDay(DateUtil.date().offset(DateField.DAY_OF_MONTH, -1));
        }
        if (null != type && type == 3) {
            begin = DateUtil.beginOfDay(DateUtil.date().offset(DateField.DAY_OF_MONTH, -7));
            end = DateUtil.endOfDay(DateUtil.date());
        }
        if (null != type && type == 4) {
            begin = DateUtil.beginOfDay(DateUtil.date().offset(DateField.DAY_OF_MONTH, -30));
            end = DateUtil.endOfDay(DateUtil.date());
        }
        if (null != type && type == 5) {
            begin = bt;
            end = et;
        }
        if(null!=type && type==1)
        {
            begin=null;
            end=null;
            planDate=DateUtil.date().toString("yyyy-MM-dd");
        }

        PageResult<List<SecurityDetailRecord>> select = service.getSecurityDetailRecord(keyword, RequestUtil.CurrentProject(), begin, end, page, size, planDate, null,planId);
        return GHResponse.ok(select.getData(), select.getTotal());
    }


    @GetMapping("security-detail-device")
    @ApiOperation("根据设备id查询详情")
    public GHResponse<List<SecurityDetailRecord>>
    SelectDeviceSecurityDetailRecord(String keyword, Integer page, Integer size,
                               Integer type,
                               Integer planId,
                               Integer deviceId,
                               @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") @RequestParam(required = false) Date bt,
                               @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") @RequestParam(required = false) Date et
    ) {

        Date begin = DateUtil.beginOfDay(DateUtil.date());
        Date end = DateUtil.endOfDay(DateUtil.date());
        String planDate=null;
        if (null != type && type == 2) {
            begin = DateUtil.beginOfDay(DateUtil.date().offset(DateField.DAY_OF_MONTH, -1));
            end = DateUtil.endOfDay(DateUtil.date().offset(DateField.DAY_OF_MONTH, -1));
        }
        if (null != type && type == 3) {
            begin = DateUtil.beginOfDay(DateUtil.date().offset(DateField.DAY_OF_MONTH, -7));
            end = DateUtil.endOfDay(DateUtil.date());
        }
        if (null != type && type == 4) {
            begin = DateUtil.beginOfDay(DateUtil.date().offset(DateField.DAY_OF_MONTH, -30));
            end = DateUtil.endOfDay(DateUtil.date());
        }
        if (null != type && type == 5) {
            begin = bt;
            end = et;
        }
        if(null!=type && type==1)
        {
            begin=null;
            end=null;
            planDate=DateUtil.date().toString("yyyy-MM-dd");
        }

        PageResult<List<SecurityDetailRecord>> select = service.getDeviceSecurityDetailRecord(keyword, RequestUtil.CurrentProject(), begin, end, page, size, planDate, null,planId,deviceId);
        return GHResponse.ok(select.getData(), select.getTotal());
    }


    @GetMapping("security-detail-down")
    @ApiOperation("查询安保巡检记录---安保巡检详细记录")
    public void
    SelectSecurityDetailRecordDown(String keyword,
                                   Integer planId,
                                   @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") @RequestParam(required = false) Date bt,
                                   @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") @RequestParam(required = false) Date et
    ) throws IOException {

        PageResult<List<SecurityDetailRecord>> select = service.getSecurityDetailRecord(keyword, 90, bt, et, null, null, null, null,planId);
        List<SecurityDetailRecord> data = select.getData();
        data.forEach(securityDetailRecord -> {
            if(StrUtil.isNotEmpty(securityDetailRecord.getPaths()))
            {
                JSONArray objects = JSONUtil.parseArray(securityDetailRecord.getPaths());
                if(null!=objects&&objects.size()>0)
                {
                    try {

                        securityDetailRecord.setPathUrl(new URL(URLUtil.encode(objects.get(0).toString())));
                    } catch (MalformedURLException e) {
                        e.printStackTrace();
                    }
                }
            }
        });
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        String name = "巡检记录" + DateUtil.date().toString("yyyyMM");
        String fileName = URLUtil.encode(name, CharsetUtil.CHARSET_UTF_8).replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
        EasyExcel.write(response.getOutputStream(), SecurityDetailRecord.class).sheet(name).doWrite(data);
    }


    @GetMapping("security-statics")
    @ApiOperation("小程序首页统计")
    public GHResponse SelectSecurityStatics(Integer userId, Integer projectId) {
        //计划巡检次数，超时，遗漏，完成，待巡检 总巡检数
        Integer  delay = 0, omit = 0, completed = 0, unCompleted = 0;
        PageResult<List<SecurityDetailRecord>> select = service.getSecurityDetailRecord(null, projectId, null, null, null, null, DateUtil.date().toString("yyyy-MM-dd"), userId,null);
        if (CollUtil.isNotEmpty(select.getData())) {
            for (SecurityDetailRecord record : select.getData()) {
                //遗漏
                if (record.getLogTime() == null && DateUtil.date().isAfter(record.getEndTime())) {
                    ++omit;
                }
                //超时
                if (record.getDelay() != null && record.getDelay()) {
                    ++delay;
                }
                if (record.getLogTime() != null) {
                    ++completed;
                }
                //未完成
                if (record.getLogTime() == null && DateUtil.date().isBefore(record.getEndTime())) {
                    unCompleted = unCompleted + record.getTimes();
                }
            }
        }
        JSONObject data = new JSONObject();
        data.put("omit", omit);
        data.put("delay", delay);
        data.put("completed", completed);
        data.put("unCompleted", unCompleted);
        data.put("all",select.getData().size());
        return GHResponse.ok(data);
    }


    @GetMapping("percent")
    @ApiOperation("当前任务巡检完成的百分比")
    public GHResponse<Double> patrolPercent(Integer taskId) {
        double percent = service.getPatrolPercent(taskId);
        return GHResponse.ok(percent);
    }


}
