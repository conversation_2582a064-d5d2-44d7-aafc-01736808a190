package com.gh.maintenance.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.gh.common.utils.PageResult;
import com.gh.maintenance.model.dto.LineDto;
import com.gh.maintenance.model.entity.Line;

import java.util.List;

public interface LineService extends IService<Line> {
    PageResult<List<LineDto>> select(Integer id, String keyword, Integer type, Integer projectId, Integer page, Integer size);

    boolean Save(LineDto lineDto);

    boolean Update(LineDto lineDto);

    Integer getLinePointCount(Integer taskId);
}
