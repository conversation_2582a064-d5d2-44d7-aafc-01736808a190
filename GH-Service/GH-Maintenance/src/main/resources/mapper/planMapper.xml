<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.gh.maintenance.mapper.PlanMapper">
<!--    <resultMap id="planDto" type="com.gh.maintenance.model.dto.PlanDto">-->
<!--        <id column="id" property="id"></id>-->
<!--        <result column="name" property="name"></result>-->
<!--        <result column="day" property="day"></result>-->
<!--        <result column="lineId" property="lineId"></result>-->
<!--        <result column="tag" property="tag"></result>-->
<!--        <result column="type" property="type"></result>-->
<!--        <result column="projectId" property="projectId"></result>-->
<!--        <result column="staffId" property="staffId"></result>-->
<!--        <result column="lineName" property="lineName"></result>-->
<!--        <result column="level" property="level"></result>-->
<!--        <result column="patrolType" property="patrolType"></result>-->
<!--        <result column="status" property="status"></result>-->
<!--        <result column="groupName" property="groupName"></result>-->
<!--        <collection property="planEvents" column="id" ofType="com.gh.maintenance.model.entity.PlanEvent">-->
<!--            <id property="id" column="eid"></id>-->
<!--            <result column="planId" property="planId"></result>-->
<!--            <result column="startTime" property="startTime"></result>-->
<!--            <result column="endTime" property="endTime"></result>-->
<!--            <result column="day" property="day"></result>-->
<!--        </collection>-->
<!--    </resultMap>-->

<!--    <select id="getPlan" resultMap="planDto">-->
<!--        SELECT ghpatrol_plan.*,ghpatrol_line.`name` as lineName,e.id as eid,e.planId-->
<!--        ,e.startTime,e.endTime,e.day,tagName as groupName FROM ghpatrol_plan-->
<!--        LEFT JOIN ghpatrol_line ON lineId=ghpatrol_line.id-->
<!--        LEFT JOIN ghpatrol_planevent e ON ghpatrol_plan.id=e.planId-->
<!--        LEFT JOIN ghops_staff ON ghpatrol_plan.staffId=ghops_staff.userId-->
<!--        LEFT JOIN ghops_dicitem ON ghops_staff.staffType=tagValue-->
<!--        LEFT JOIN ghops_dic ON ghops_dicitem.dicId=ghops_dic.id and `code`='profession_type'-->
<!--       <where>-->
<!--           <if test="id!=null">-->
<!--               and ghpatrol_plan.id=#{id}-->
<!--           </if>-->
<!--           <if test="projectId!=null">-->
<!--               and ghpatrol_plan.projectId=#{projectId}-->
<!--           </if>-->
<!--           <if test="type!=null">-->
<!--               and ghpatrol_plan.type=#{type}-->
<!--           </if>-->
<!--           <if test="tag!=null">-->
<!--               and ghpatrol_plan.tag=#{tag}-->
<!--           </if>-->
<!--           <if test="status!=null">-->
<!--               and ghpatrol_plan.status=#{status}-->
<!--           </if>-->
<!--           <if test="bt!=null">-->
<!--               and e.startTime <![CDATA[>=]]> #{bt}-->
<!--           </if>-->
<!--           <if test="et!=null">-->
<!--               and e.startTime <![CDATA[<=]]> #{et}-->
<!--           </if>-->
<!--       </where>-->

<!--    </select>-->


        <select id="getEvent" resultType="com.gh.maintenance.model.entity.PlanEvent">
            select * from ghpatrol_planevent
            <where>
                <if test="id!=null">
                    and ghpatrol_planevent.planId=#{id}
                </if>

            </where>
        </select>

        <resultMap id="planDto" type="com.gh.maintenance.model.dto.PlanDto">
            <id column="id" property="id"></id>
            <collection property="planEvents" column="id" select="getEvent" >
            </collection>
        </resultMap>

        <select id="getPlan" resultMap="planDto">
            SELECT  distinct ghpatrol_plan.`name`,ghpatrol_plan.id,ghpatrol_plan.lineId,ghpatrol_plan.type,
            ghpatrol_plan.projectId,ghpatrol_plan.staffId,ghpatrol_plan.tag,`level`,patrolType,`status`,
            description,ghpatrol_line.`name` as lineName FROM ghpatrol_plan
            LEFT JOIN ghpatrol_line ON lineId=ghpatrol_line.id
            left join ghpatrol_planevent on ghpatrol_plan.id=ghpatrol_planevent.planId
            <where>
                <if test="id!=null">
                    and ghpatrol_plan.id=#{id}
                </if>
                <if test="keyword!=null and keyword!=''">
                    and ghpatrol_plan.name like #{keyword}
                </if>
                <if test="projectId!=null">
                    and ghpatrol_plan.projectId=#{projectId}
                </if>
                <if test="type!=null">
                    and ghpatrol_plan.type=#{type}
                </if>
                <if test="tag!=null">
                    and ghpatrol_plan.tag=#{tag}
                </if>
                <if test="bt!=null">
                    and ghpatrol_planevent.startTime <![CDATA[>=]]> #{bt}
                </if>
                <if test="et!=null">
                    and ghpatrol_planevent.startTime <![CDATA[<=]]> #{et}
                </if>

            </where>
        </select>

</mapper>
