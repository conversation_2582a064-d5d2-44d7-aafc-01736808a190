server:
  port: 9022
spring:
  application:
    name: maintenance-service # 应用名称
  mvc:
    servlet:
      load-on-startup: 1
    locale: zh_CN
  activiti:
    # 是否对数据库架构进行检查更新
    database-schema-update: true
    # 历史储存级别
    history-level: full
    #     是否开启数据库储存历史记录 如果不开启这个配置的话 Activiti 只会创建17张表，完整的Activiti引擎依赖25张表
    db-history-used: true
  cloud:
    nacos:
      server-addr: ${host:*************:28848}
      config:
        file-extension: yaml
        namespace: 9027158d-ab5d-43f7-a691-3b7546cc79f9
        extension-configs:
          - data-id: redis.yml
          - data-id: mysql.yml
          - data-id: kafka.yml
          - data-id: json.yml
          - data-id: feign.yml
          - data-id: swagger.yml
#          - data-id: zipkin.yml
#          - data-id: admin.yml
      discovery:
        server-addr: ${host:*************:28848}
        namespace: 6cf15815-a3cb-4cba-a940-16026af4257c

  main:
    allow-bean-definition-overriding: true
  jackson:
    default-property-inclusion: always
  mybatis-plus:
    mapper-locations: classpath:mapper/*.xml
    configuration:
      map-underscore-to-camel-case: false
logging:
  level:
    com.gh.maintenance: debug
    root: info
    org: info
    io: info
    sun: info
snowflake:
    workId: 1
    dataId: 1
wx:
  mp:
    useRedis: false
    configs:
      - appId: wxde2cb9f1de0c479e # 第一个公众号的appid
        secret: b8bc7edc164b064c65a9fd4da368bcfd # 公众号的appsecret
        token: dongdong1997 # 接口配置里的Token值
        aesKey: QawCZnQDXPUBC6ShGlccN4TEIajsBiQDB4z7WoHYKPn  # 接口配置里的EncodingAESKey值
pool:
  corePool: 50
  maxPool: 500
  queueCapacity: 1000



