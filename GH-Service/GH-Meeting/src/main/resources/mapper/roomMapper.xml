<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gh.meeting.mapper.RoomMapper">
    <select id="getRoomList"   resultType="com.gh.meeting.model.dto.RoomDTO">
        SELECT r.*,d.`name` as deptName FROM ghmeeting_room r LEFT JOIN ghops_department d on r.deptId=d.id
        <where>
            <if test="keyword!=null and keyword!=''">
                and r.name like concat('%',#{keyword},'%')
            </if>
            <if test="projectId!=null">
                and r.projectId=#{projectId}
            </if>
            <if test="deptId!=null">
                and r.deptId=#{deptId}
            </if>
            <if test="status!=null">
                and r.roomStatus=#{status}
            </if>
        </where>
    </select>



</mapper>
