package com.gh.rtdb.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.gh.rtdb.model.dto.DevTemplateDTO;
import com.gh.rtdb.model.entity.DeviceTemplate;
import org.springframework.stereotype.Service;


public interface DeviceTemplateService extends IService<DeviceTemplate> {
    boolean exportTemplate(DevTemplateDTO devTemplateDTO);
    boolean importTemplate(Integer id,String iosvrKey );
    boolean renameTemplate(Integer id, String name);
    boolean  removeTemplate(String[] ids);
}
