package com.gh.rtdb.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gh.common.utils.PageResult;
import com.gh.rtdb.mapper.GateVariableMapper;
import com.gh.rtdb.model.entity.GateVariable;
import com.gh.rtdb.service.GateVariableService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@AllArgsConstructor
public class GateVariableServiceImpl extends ServiceImpl<GateVariableMapper, GateVariable> implements GateVariableService {

    private final GateVariableMapper mapper;
    @Override
    public PageResult<List<GateVariable>> getVariable(Integer projectId, String serverKey, String channelKey, String controllerKey, Integer page, Integer size) {
        Page page1=null;
        if(PageResult.isPage(page,size))
        {
            page1=new Page(page,size);
        }
        List<GateVariable> list = mapper.getVariableList(page1, projectId, server<PERSON>ey, channel<PERSON>ey, controllerKey);
        return PageResult.<List<GateVariable>>builder().data(list).total(page1==null?list.size():page1.getTotal()).build();
    }
}
