package com.gh.rtdb.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gh.common.utils.PageResult;
import com.gh.rtdb.mapper.AlarmHistoryMapper;
import com.gh.rtdb.model.dto.*;
import com.gh.rtdb.model.entity.AlarmHistory;
import com.gh.rtdb.model.vo.HistoryRecordVo;
import com.gh.rtdb.service.AlarmHistoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service
public class AlarmHistoryServiceImpl extends ServiceImpl<AlarmHistoryMapper, AlarmHistory>
implements AlarmHistoryService {

    @Autowired
    private AlarmHistoryMapper alarmHistoryMapper;

//    @CachePut(value = "alarm", key = "#history.getAlarmVariable()")
    public AlarmHistory SaveCache(AlarmHistory history) {
        boolean save = save(history);
        if (save) {
            return history;
        }
        return null;
    }

    public List<AlarmHistory> GetAlarmHistory(Boolean status) {
        QueryWrapper<AlarmHistory> queryWrapper = new QueryWrapper<>();
        if (null != status) {
            queryWrapper.eq("status", status);
        }
        return alarmHistoryMapper.selectList(queryWrapper);
    }

    public PageResult<List<HistoryRecordVo>> GetHistoryRecord(String keyword, Boolean status, Date bt, Date et, Integer deviceId, Integer projectId , Integer page, Integer size,Integer alarmSource,String identifier,Integer productId) {
        if (StrUtil.isNotBlank(keyword)) {
            keyword = "%" + keyword + "%";
        }
        if (null != page && null != size && page > 0 && size > 0) {
            Page<HistoryRecordVo> voPage = new Page<>(page, size);
            List<HistoryRecordVo> record = alarmHistoryMapper.getHistoryRecord(voPage, keyword, status, bt, et, deviceId,projectId,alarmSource,identifier,productId);
            return PageResult.<List<HistoryRecordVo>>builder()
                    .data(record)
                    .total(voPage.getTotal())
                    .build();

        } else {
            List<HistoryRecordVo> record = alarmHistoryMapper.getHistoryRecord(null, keyword, status, bt, et, deviceId,projectId,alarmSource,identifier,productId);
            return PageResult.<List<HistoryRecordVo>>builder()
                    .data(record)
                    .total(record.size())
                    .build();
        }
    }

    public List<WeekAlarmCount> GetWeekAlarmCount(Date bt, Date et,Integer alarmSource,Integer projectId) {
      return   alarmHistoryMapper.getWeekCount(bt,et,alarmSource,projectId);

    }





    /**
     * 报警源分类统计
     */
    public SourceLevel GetSourceAndLevelCount(Date bt,Date et,String tag)
    {
        List<LevelCount> levelCount=new ArrayList<>();
        if(StrUtil.isNotBlank(tag))
        {
            if(tag.equals("day"))
            {
               levelCount= alarmHistoryMapper.getDayLevelCount(bt, et);
            }
            else if(tag.equals("week"))
            {
                levelCount= alarmHistoryMapper.getWeekLevelCount(bt, et);
            }
            else if(tag.equals("month"))
            {
                levelCount= alarmHistoryMapper.getMonthLevelCount(bt, et);
            }
            else if(tag.equals("year"))
            {
                levelCount= alarmHistoryMapper.getYearLevelCount(bt, et);
            }
        }
        List<SourceCount> sourceCount = alarmHistoryMapper.getSourceCount(bt, et);
        return SourceLevel.builder().sourceCounts(sourceCount)
                .levelCounts(levelCount)
                .build();
    }

    @Override
    public List<AlarmInfoDto> getAlarmById(String uuid) {
        return alarmHistoryMapper.getAlarmById(uuid);
    }
}
