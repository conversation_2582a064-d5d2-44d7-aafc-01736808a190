package com.gh.rtdb.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gh.common.redis.RedisHelper;
import com.gh.rtdb.mapper.AlarmConfigMapper;
import com.gh.rtdb.model.entity.AlarmConfig;
import com.gh.rtdb.service.IAlarmConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CachePut;
import org.springframework.stereotype.Service;

@Service
public class AlarmConfigService extends ServiceImpl<AlarmConfigMapper, AlarmConfig> implements IAlarmConfigService {


    @Autowired
    private AlarmConfigMapper alarmConfigMapper;

    @Autowired
    private RedisHelper redisHelper;





    @Override
    @CachePut(value = "alarmConfig", key = "#alarmConfig.getDeviceId()+'_'+#alarmConfig.getIdentifier()")
    public AlarmConfig SaveCache(AlarmConfig alarmConfig) {
        save(alarmConfig);
//        List<DeviceStandard> standards = deviceStandardService.lambdaQuery().eq(DeviceStandard::getId, alarmConfig.getStandardId()).list();
        if(StrUtil.isNotEmpty(alarmConfig.getVariable()))
        {
//            if(StrUtil.isNotEmpty(standards.get(0).getVariable()))
//            {
                redisHelper.StringSet(0,"alarmConfigVariable:"+alarmConfig.getVariable(),"true");
//            }
        }
        return alarmConfig;
    }


    @Override
    public AlarmConfig UpdateCahce(AlarmConfig alarmConfig) {
        if(alarmConfig.getSceneId()==null)
        {
            alarmConfig.setSceneId(null);
        }
        boolean update = updateById(alarmConfig);
//        if (update) {
//            redisHelper.StringSet(0,"alarmConfig:"+alarmConfig.getStandardId(), JSONUtil.toJsonStr(alarmConfig));
//            return alarmConfig;
//        }
        return null;
    }
}
