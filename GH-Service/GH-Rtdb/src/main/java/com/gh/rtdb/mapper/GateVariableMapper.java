package com.gh.rtdb.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gh.rtdb.model.entity.GateVariable;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
@Mapper
public interface GateVariableMapper extends BaseMapper<GateVariable> {

    List<GateVariable>  getVariableList(Page page,Integer projectId,String serverKey,String channelKey,String controllerKey);
}
