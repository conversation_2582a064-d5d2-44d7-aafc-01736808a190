package com.gh.rtdb.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gh.common.model.entity.RunLog;
import com.gh.common.utils.PageResult;
import com.gh.rtdb.mapper.RunLogMapper;
import com.gh.rtdb.service.RunLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;


@Service
public class RunLogServiceImpl extends ServiceImpl<RunLogMapper, RunLog> implements RunLogService {
    @Autowired
    private RunLogMapper runLogMapper;

    @Override
    public PageResult<List<RunLog>> select(Integer type, Integer projectId, Integer menuId, Date bt, Date et, Integer tag, Integer userId, Integer deviceId , Integer page, Integer size) {
        Page<RunLog> runLogPage = null;
        if (PageResult.isPage(page, size)) {
            runLogPage = new Page<>(page, size);
        }
        List<RunLog> logPage = runLogMapper.getRunLogRecord(runLogPage, userId, type, projectId, menuId, tag, bt, et,deviceId);
        return PageResult.<List<RunLog>>builder().data(logPage)
                .total(runLogPage!=null?runLogPage.getTotal():logPage.size()).build();
    }

    @Override
    public PageResult<List<RunLog>> runLog(Integer menuId, Date bt, Date et, Integer page, Integer size) {
        Page<RunLog> runLogPage = null;
        if (PageResult.isPage(page, size)) {
            runLogPage = new Page<>(page, size);
        }
        List<RunLog> logPage = runLogMapper.getRunLog(runLogPage,null, menuId, bt, et);
        return PageResult.<List<RunLog>>builder().data(logPage)
                .total(runLogPage!=null?runLogPage.getTotal():logPage.size()).build();
    }

    @Override
    public PageResult<List<RunLog>> runAllLog(String keyword, Date bt, Date et, Integer page, Integer size, Integer projectId,Integer menuId,Integer deviceId) {
        Page<RunLog> runLogPage = null;
        if (PageResult.isPage(page, size)) {
            runLogPage = new Page<>(page, size);
        }
        List<RunLog> logPage = runLogMapper.getAllRunLog(runLogPage,projectId, keyword, bt, et,menuId,deviceId);
        return PageResult.<List<RunLog>>builder().data(logPage)
                .total(runLogPage!=null?runLogPage.getTotal():logPage.size()).build();
    }

    @Override
    public List<RunLog> getAllRunLogOptimized(String keyword, Date bt, Date et, Integer limit, Integer offset, Integer projectId, Integer menuId, Integer deviceId) {
        return runLogMapper.getAllRunLogOptimized(limit,offset, projectId, keyword, bt, et, menuId, deviceId);
    }

    @Override
    public Long countAllRunLogOptimized(Integer projectId, String keyword, Date bt, Date et, Integer menuId, Integer deviceId) {
        return runLogMapper.countAllRunLogOptimized(projectId, keyword, bt, et, menuId, deviceId);
    }
}
