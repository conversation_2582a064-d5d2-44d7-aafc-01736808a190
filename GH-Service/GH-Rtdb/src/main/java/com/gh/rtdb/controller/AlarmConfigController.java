package com.gh.rtdb.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.gh.common.exception.SystemEnumMsg;
import com.gh.common.model.dto.WriteCustomDataRequestV2;
import com.gh.common.model.dto.WriteCustomDataV2;
import com.gh.common.model.entity.DeviceStandard;
import com.gh.common.redis.RedisHelper;
import com.gh.common.utils.GHResponse;
import com.gh.rtdb.kafka.KafkaProduce;
import com.gh.rtdb.model.entity.AlarmConfig;
import com.gh.rtdb.service.DeviceStandardService;
import com.gh.rtdb.service.IAlarmConfigService;
import com.gh.rtdb.service.RuleConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;

@RestController
@RequestMapping(value = "/alarm-config")
@Api(tags = "报警配置")
public class AlarmConfigController {
    @Autowired
    private IAlarmConfigService alarmConfigService;

    @Autowired
    private RedisHelper redisHelper;

    @Autowired
    private DeviceStandardService deviceStandardService;

    @Autowired
    private RuleConfigService ruleConfigService;

    @Autowired
    private KafkaProduce kafkaProduce;


    @GetMapping
    public GHResponse<AlarmConfig> get(String identifier,Integer deviceId) {
        AlarmConfig alarmConfig = alarmConfigService
                .lambdaQuery().eq(AlarmConfig::getIdentifier, identifier)
                .eq(AlarmConfig::getDeviceId, deviceId).oneOpt().orElse(null);
        return GHResponse.ok(alarmConfig);
    }

    @GetMapping("all")
    public GHResponse getAll(Integer projectId) {
        List<AlarmConfig> configs = alarmConfigService
                .lambdaQuery()
                .eq(AlarmConfig::getProjectId, projectId).list();
        return GHResponse.ok(configs);
    }

    @PostMapping
//    @OperLog(category = "报警配置", description = "新增报警配置")
    public GHResponse save(@RequestBody @Valid List<AlarmConfig> alarmConfigs) {
        for (AlarmConfig alarmConfig : alarmConfigs) {

            List<AlarmConfig> list = this.alarmConfigService.lambdaQuery()
                    .eq(AlarmConfig::getDeviceId, alarmConfig.getDeviceId())
                    .eq(AlarmConfig::getIdentifier, alarmConfig.getIdentifier()).list();
            if ((null == alarmConfig.getId() || alarmConfig.getId() == 0 || alarmConfig.getId() == -1) && list.size() == 0) {
                alarmConfigService.SaveCache(alarmConfig).getId();
            } else {
                if (CollUtil.isNotEmpty(list)) {
                    alarmConfig.setId(list.get(0).getId());
                }
                boolean update = alarmConfigService.updateById(alarmConfig);
                if (update) {
//                    List<DeviceStandard> deviceStandards = deviceStandardService.lambdaQuery().eq(DeviceStandard::getId, alarmConfig.getStandardId()).list();
                    redisHelper.StringSet(0, "alarmConfig::" +alarmConfig.getDeviceId()+"_"+ alarmConfig.getIdentifier(), JSONUtil.toJsonStr(alarmConfig));
                    if (StrUtil.isNotEmpty(alarmConfig.getVariable())) {
                        redisHelper.StringSet(0, "alarmConfigVariable:" +alarmConfig.getVariable(), "true");
                    }
                }
            }

        }
        return GHResponse.ok(SystemEnumMsg.CREATE_SUCCESS.msg());
    }


    @DeleteMapping
//    @OperLog(category = "报警配置", description = "删除报警配置")
    public GHResponse del(@RequestBody AlarmConfig alarmConfig) {
        if (null != alarmConfig.getId()) {
            boolean remove = alarmConfigService.removeById(alarmConfig.getId());
            if (remove) {
                redisHelper.KeyDelete(0, "alarmConfig:" + alarmConfig.getStandardId());
                List<DeviceStandard> standards = deviceStandardService.lambdaQuery().eq(DeviceStandard::getId, alarmConfig.getStandardId()).list();
                if (CollUtil.isNotEmpty(standards)) {
                    if (StrUtil.isNotEmpty(standards.get(0).getVariable())) {
                        Boolean keyDelete = redisHelper.KeyDelete(0, "alarmConfigVariable:" + standards.get(0).getVariable());
                        if (keyDelete) {
                            return GHResponse.ok();
                        }
                    }
                }
            }
        }
        return GHResponse.failed(SystemEnumMsg.Delete_ERROR.msg());
    }


    @GetMapping("upload")
    public GHResponse createRuleData(Integer projectId) {
        String server = "";
        List<AlarmConfig> variableConfig = ruleConfigService.getRuleVariableConfig(projectId);
        if (CollUtil.isNotEmpty(variableConfig)) {
            for (AlarmConfig alarmConfig : variableConfig) {
                String variable = alarmConfig.getVariable();
                if (StrUtil.isNotEmpty(variable)) {
                    String[] split = variable.split(":");
                    server = split[0];
                    JSONObject data = new JSONObject();
                    data.set("ChlID", split[1]);
                    data.set("CtrlID", split[2]);
                    data.set("VarID", split[3]);
                    JSONObject HH = new JSONObject();
                    HH.set("enable", alarmConfig.getAlarmHHEnable() == null ? false : alarmConfig.getAlarmHHEnable());
                    HH.set("value", alarmConfig.getAlarmHH() == null ? 0 : alarmConfig.getAlarmHH());
                    HH.set("level", alarmConfig.getAlarmLevelHH() == null ? 0 : alarmConfig.getAlarmLevelHH());
                    JSONObject H = new JSONObject();
                    H.set("enable", alarmConfig.getAlarmHEnable() == null ? false : alarmConfig.getAlarmHEnable());
                    H.set("value", alarmConfig.getAlarmH() == null ? 0 : alarmConfig.getAlarmH());
                    H.set("level", alarmConfig.getAlarmLevelH() == null ? 0 : alarmConfig.getAlarmLevelH());

                    JSONObject LL = new JSONObject();
                    LL.set("enable", alarmConfig.getAlarmLLEnable() == null ? false : alarmConfig.getAlarmLLEnable());
                    LL.set("value", alarmConfig.getAlarmLL() == null ? 0 : alarmConfig.getAlarmLL());
                    LL.set("level", alarmConfig.getAlarmLevelLL() == null ? 0 : alarmConfig.getAlarmLevelLL());
                    JSONObject L = new JSONObject();
                    L.set("enable", alarmConfig.getAlarmLEnable() == null ? false : alarmConfig.getAlarmLEnable());
                    L.set("value", alarmConfig.getAlarmL() == null ? 0 : alarmConfig.getAlarmL());
                    L.set("level", alarmConfig.getAlarmLevelL() == null ? 0 : alarmConfig.getAlarmLevelL());
                    JSONObject state = new JSONObject();
                    if (BooleanUtil.isTrue(alarmConfig.getAlarmST0()) || BooleanUtil.isTrue(alarmConfig.getAlarmST1()) ||
                            BooleanUtil.isTrue(alarmConfig.getAlarmST2()) || BooleanUtil.isTrue(alarmConfig.getAlarmST3())) {
                        state.set("enable", true);
                    } else {
                        state.set("enable", false);
                    }
                    state.set("value0", alarmConfig.getAlarmST0() == null ? false : alarmConfig.getAlarmST0());
                    state.set("value1", alarmConfig.getAlarmST1() == null ? false : alarmConfig.getAlarmST1());
                    state.set("value2", alarmConfig.getAlarmST2() == null ? false : alarmConfig.getAlarmST2());
                    state.set("value3", alarmConfig.getAlarmST3() == null ? false : alarmConfig.getAlarmST3());
                    state.set("level", alarmConfig.getAlarmLevelST() == null ? 0 : alarmConfig.getAlarmLevelST());
                    data.set("HH", HH);
                    data.set("LL", LL);
                    data.set("L", L);
                    data.set("H", H);
                    data.set("state", state);
                    JSONObject msg = new JSONObject();
                    msg.set("data", data);
                    msg.set("code", 0);
                    msg.set("msgId", new Snowflake().nextId());
                    msg.set("msgTime", System.currentTimeMillis());
                    msg.set("key", alarmConfig.getDeviceName() + "/" + alarmConfig.getStdName());
                    //msg.set("Message", "");
                    //msg.set("RequestID", alarmConfig.getDeviceName() + "---" + alarmConfig.getStdName());
                    //msg.set("HostId", "");
                    //msg.set("ClientId", "");
                    //msg.set("ObjectID", "");
                    kafkaProduce.Produce("sys_mqtt_rule_set", split[0] + "&" + msg);
                    alarmConfigService.lambdaUpdate().set(AlarmConfig::getIsAsync, true)
                            .eq(AlarmConfig::getId, alarmConfig.getId()).update();

                }
            }
        }
        if (StrUtil.isNotEmpty(server)) {
            WriteCustomDataV2 customData = WriteCustomDataV2.builder().iosvrKey(server)
                    .chlKey("")
                    .cmdCode(209)
                    .ctrlKey("")
                    .varKey("").build();
            WriteCustomDataRequestV2 dataRequest = WriteCustomDataRequestV2.
                    builder()
                    .data(new ArrayList<>())
                    .build();
            dataRequest.data.add(customData);

            kafkaProduce.Produce("gh_mqtt_write_custom", server + "&" + JSONUtil.toJsonStr(dataRequest));

        }

        return GHResponse.ok();

    }

    @GetMapping("down")
    public GHResponse downConfigFromRedis(Integer projectId) {
        List<AlarmConfig> variableConfig = ruleConfigService.getRuleVariableConfig(projectId);
        if (CollUtil.isNotEmpty(variableConfig)) {
            variableConfig.forEach(alarmConfig -> {
                String variable = alarmConfig.getVariable();
                if (StrUtil.isNotEmpty(variable)) {

                    JSONObject object = redisHelper.getHash(14, "var:" + variable);

                    AlarmConfig config = JSONUtil.toBean(object, AlarmConfig.class);
                    BeanUtil.copyProperties(config, alarmConfig);
                    alarmConfigService.updateById(alarmConfig);
                }
            });
        }

        return GHResponse.ok();

    }

    @GetMapping("config-reply")
    public GHResponse getConfigReply(Integer projectId) {
        Set<String> alarm_set_reply = redisHelper.SetMembers(0, "alarm_set_reply");
        alarm_set_reply.forEach(s -> {
            redisHelper.SetRemove(0, "alarm_set_reply", s);
        });

        return GHResponse.ok(alarm_set_reply);

    }


    @ApiOperation("同步报警配置到redis调试使用")
    @GetMapping("sync")
    public GHResponse sync() {
        List<AlarmConfig> alarmConfigs = alarmConfigService.list();
        for (AlarmConfig alarmConfig : alarmConfigs) {
            List<DeviceStandard> deviceStandards = deviceStandardService.lambdaQuery().eq(DeviceStandard::getId, alarmConfig.getStandardId()).list();
            redisHelper.StringSet(0, "alarmConfig::" + alarmConfig.getStandardId(), JSONUtil.toJsonStr(alarmConfig));
            if ( StrUtil.isNotEmpty(deviceStandards.get(0).getVariable())) {
                redisHelper.StringSet(0, "alarmConfigVariable:" + deviceStandards.get(0).getVariable(), "true");
            }
        }
        return GHResponse.ok(SystemEnumMsg.CREATE_SUCCESS.msg());
    }
}
