package com.gh.rtdb;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

@SpringBootApplication
@ComponentScan(basePackages = "com.gh")
@EnableDiscoveryClient
@EnableAsync
@EnableFeignClients("com.gh")
@EnableScheduling
public class RtdbApplication {
    public static void main(String[] args) {
        SpringApplication.run(RtdbApplication.class,args);
    }
}
