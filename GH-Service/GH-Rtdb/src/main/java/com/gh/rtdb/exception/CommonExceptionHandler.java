package com.gh.rtdb.exception;

import cn.hutool.json.JSONObject;
import com.gh.common.exception.SystemEnumMsg;
import com.gh.common.utils.GHResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;

@Slf4j
@ControllerAdvice
public class CommonExceptionHandler {
    @ExceptionHandler(CommonException.class)
    public ResponseEntity HandlerException(CommonException e)
    {
        ErrorEnum errorEnum = e.getErrorEnum();
        JSONObject error = new JSONObject();
        error.set("msg",errorEnum.getMsg());
        error.set("code",errorEnum.getCode());
        error.set("success",false);
        error.set("data","");
        error.set("total",0);
        return ResponseEntity.status(errorEnum.getCode()).body(error);
    }
}
