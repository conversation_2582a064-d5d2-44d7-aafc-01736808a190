package com.gh.rtdb.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gh.common.exception.ExceptionEnum;
import com.gh.common.exception.GhCustomException;
import com.gh.common.exception.SystemEnumMsg;
import com.gh.common.redis.RedisHelper;
import com.gh.common.utils.GHResponse;
import com.gh.common.utils.PageResult;
import com.gh.log.annotation.OperLog;
import com.gh.rtdb.listener.UploadDeviceListener;
import com.gh.rtdb.model.dto.VariableExcelDto;
import com.gh.rtdb.model.dto.ServerDto;
import com.gh.rtdb.model.entity.GateServer;
import com.gh.rtdb.service.GateServerService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;


/**
 * 虚拟采集器
 */
@RequestMapping("/server")
@RestController
@Api(tags = "虚拟采集器")
public class ServerController {

    @Autowired
    private  GateServerService service;
    @Autowired
    private  RedisHelper redisHelper;
    @Autowired
    private  UploadDeviceListener listener;

    @Autowired
    public ServerController(GateServerService service, RedisHelper redisHelper) {
        this.service = service;
        this.redisHelper = redisHelper;
    }

    @PostMapping
    @ApiOperation("添加虚拟采集器")
//    @OperLog(category = "虚拟采集器管理",description = "添加虚拟采集器")
    public GHResponse Add(@RequestBody @Valid GateServer gateServer) {

        List<GateServer> list = service.lambdaQuery()
                .and(w->w.eq(GateServer::getKeyValue,gateServer.getKeyValue()).or().eq(GateServer::getName,gateServer.getName())).list();

        if (CollUtil.isNotEmpty(list)) {
            throw new GhCustomException(ExceptionEnum.KEY_EXISIT);
        }



        boolean insert = service.save(gateServer);
        if (insert) {
            redisHelper.SetAdd(4, "RTDBSET", "iosvr:" + gateServer.getKeyValue());
            JSONObject jsonObject = new JSONObject();
            jsonObject.set("ID", gateServer.getKeyValue());
            jsonObject.set("Name", gateServer.getName());
            redisHelper.setHash(11, "iosvr:" + gateServer.getKeyValue(), jsonObject);
            return GHResponse.ok(null, SystemEnumMsg.CREATE_SUCCESS.msg());
        }
        return GHResponse.failed(SystemEnumMsg.CREATE_ERROR.msg());
    }

    @PutMapping
    @ApiOperation("更新虚拟采集器")
//    @OperLog(category = "虚拟采集器管理",description = "更新虚拟采集器")
    public GHResponse Patch(@RequestBody @Valid GateServer gateServer) {

        List<GateServer> list = service.lambdaQuery()
                .and(w->w.eq(GateServer::getKeyValue,gateServer.getKeyValue()).or().eq(GateServer::getName,gateServer.getName()))
                .eq(GateServer::getProjectId, gateServer.getProjectId()).list();
        if (CollUtil.isNotEmpty(list) && !list.get(0).getId().equals(gateServer.getId())) {
            throw new GhCustomException(ExceptionEnum.PROJECT_REPEATE_ADD);
        }
        boolean update = service.updateById(gateServer);
        if (update) {
            return GHResponse.ok();
        }
        return GHResponse.failed(SystemEnumMsg.Update_ERROR.msg());
    }

    @DeleteMapping
    @ApiOperation("删除虚拟采集器")
//    @OperLog(category = "虚拟采集器管理",description = "删除虚拟采集器")
    public GHResponse Delete(@RequestParam("ids") String[] ids,Integer pr) {
        if (CollUtil.isNotEmpty(Arrays.asList(ids))) {
            boolean delete = service.removeByIds(Arrays.asList(ids));
            if (delete) {
                return GHResponse.ok();
            }
        }
        return GHResponse.failed(SystemEnumMsg.Delete_ERROR.msg());
    }


    @GetMapping("tree")
    @ApiOperation("查询虚拟采集器")
    public GHResponse<List<ServerDto>> SelectTree(Integer projectId) {
        List<ServerDto> tree = service.getServerTree(projectId);
        return GHResponse.ok(tree);
    }

    @GetMapping
    @ApiOperation("查询虚拟采集器")
    public GHResponse<List<GateServer>> Selects(Integer projectId, String name, Integer page, Integer size) {
        LambdaQueryChainWrapper<GateServer> query = service.lambdaQuery();
        if (null != projectId) {
            query.eq(GateServer::getProjectId, projectId);
        }
        if (StrUtil.isNotEmpty(name)) {
            query.like(GateServer::getName, "%" + name + "%");
        }
        if (PageResult.isPage(page, size)) {
            Page<GateServer> page1 = query.page(new Page<>(page, size));
            return GHResponse.ok(page1.getRecords(), page1.getTotal());
        }
        List<GateServer> list = query.list();
        return GHResponse.ok(list, list.size());
    }

    @PutMapping("sync")
    @ApiOperation("同步上传")
    public GHResponse<List<GateServer>> update(@RequestParam Integer projectId, @RequestParam String serverKey) {
        boolean sync = service.sync(projectId, serverKey);
        if (sync) {
            return GHResponse.ok(null, "同步成功");
        }
        return GHResponse.failed("同步失败");
    }

    @PostMapping("upload")
    @ApiOperation("青岛项目批量导入虚拟变量")
    public GHResponse Upload(@RequestParam("file") MultipartFile file) throws IOException {
        if (null == file || file.isEmpty()) {
            throw new GhCustomException(ExceptionEnum.FILE_EMPTY);
        }
        String extName = file.getOriginalFilename().split("\\.")[1];
        if (!extName.equals("xls") && !extName.equals("xlsx")) {
            throw new GhCustomException(ExceptionEnum.FILE_EXT_FORMAT);
        }
        EasyExcel.read(file.getInputStream(), VariableExcelDto.class,listener).sheet().doRead();


        return GHResponse.ok();
    }


}
