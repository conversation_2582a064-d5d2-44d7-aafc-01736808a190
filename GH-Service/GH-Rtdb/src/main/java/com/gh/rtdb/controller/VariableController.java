package com.gh.rtdb.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import com.gh.common.exception.ExceptionEnum;
import com.gh.common.exception.GhCustomException;
import com.gh.common.exception.SystemEnumMsg;
import com.gh.common.redis.RedisHelper;
import com.gh.common.utils.GHResponse;
import com.gh.common.utils.PageResult;
import com.gh.log.annotation.OperLog;
import com.gh.rtdb.model.entity.GateVariable;
import com.gh.rtdb.service.GateVariableService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Arrays;
import java.util.List;


/**
 * 虚拟变量
 */
@RequestMapping("/variable")
@RestController
@Api(tags = "虚拟变量")
public class VariableController {


    private final GateVariableService service;
    private final RedisHelper redisHelper;

    @Autowired
    public VariableController(GateVariableService service,RedisHelper redisHelper) {
        this.service = service;
        this.redisHelper=redisHelper;
    }

    @PostMapping
    @ApiOperation("添加虚拟变量")
//    @OperLog(category = "虚拟变量管理",description = "添加虚拟变量")
    public GHResponse Add(@RequestBody @Valid GateVariable gateVariable) {

        List<GateVariable> list = service.lambdaQuery()
                .eq(GateVariable::getServerKey,gateVariable.getServerKey())
                .eq(GateVariable::getChannelKey,gateVariable.getChannelKey())
                .eq(GateVariable::getControllerKey,gateVariable.getControllerKey())

                .eq(GateVariable::getProjectId,gateVariable.getProjectId())
                .and(w->w.eq(GateVariable::getKeyValue,gateVariable.getKeyValue()).or().eq(GateVariable::getName,gateVariable.getName()))
                .list();

        if(CollUtil.isNotEmpty(list))
        {
            throw new GhCustomException(ExceptionEnum.PROJECT_REPEATE_ADD);
        }

        boolean insert = service.save(gateVariable);
        if (insert) {
            String key=  gateVariable.getServerKey()+":"+
                    gateVariable.getChannelKey()+":"+
                    gateVariable.getControllerKey()+":"+
                    gateVariable.getKeyValue();
            redisHelper.SetAdd(4, StrUtil.format("RTDBSET:{}:{}:{}",
                    gateVariable.getServerKey(), gateVariable.getChannelKey(), gateVariable.getControllerKey()), "var:" + key);
            JSONObject jsonObject=new JSONObject();
            jsonObject.set("ID",gateVariable.getKeyValue());
            jsonObject.set("Name",gateVariable.getName());
            jsonObject.set("Value","0");
            redisHelper.setHash(14, "var:" + key,jsonObject);

            return GHResponse.ok(null, SystemEnumMsg.CREATE_SUCCESS.msg());
        }
        return GHResponse.failed(SystemEnumMsg.CREATE_ERROR.msg());
    }

    @PutMapping
    @ApiOperation("更新虚拟变量")
//    @OperLog(category = "虚拟变量管理",description = "更新虚拟变量")
    public GHResponse Patch(@RequestBody @Valid GateVariable gateVariable) {
        List<GateVariable> list = service.lambdaQuery()
                .and(w->w.eq(GateVariable::getKeyValue,gateVariable.getKeyValue()).or().eq(GateVariable::getName,gateVariable.getName()))
                .eq(GateVariable::getServerKey,gateVariable.getServerKey())
                .eq(GateVariable::getChannelKey,gateVariable.getChannelKey())
                .eq(GateVariable::getControllerKey,gateVariable.getControllerKey())
                .eq(GateVariable::getProjectId,gateVariable.getProjectId())
                .list();
        if(CollUtil.isNotEmpty(list)&&!list.get(0).getId().equals(gateVariable.getId()))
        {
            throw new GhCustomException(ExceptionEnum.PROJECT_REPEATE_ADD);
        }
        boolean update = service.updateById(gateVariable);
        if (update) {
            return GHResponse.ok();
        }
        return GHResponse.failed(SystemEnumMsg.Update_ERROR.msg());
    }

    @DeleteMapping
    @ApiOperation("删除虚拟变量")
//    @OperLog(category = "虚拟变量管理",description = "删除虚拟变量")
    public GHResponse Delete(@RequestParam("ids") String[] ids)
    {
        if(CollUtil.isNotEmpty(Arrays.asList(ids)))
        {
            boolean delete = service.removeByIds(Arrays.asList(ids));
            if(delete)
            {
                return GHResponse.ok();
            }
        }
        return GHResponse.failed(SystemEnumMsg.Delete_ERROR.msg());
    }

    @GetMapping
    @ApiOperation("查询虚拟变量")
    public GHResponse<List<GateVariable>> Selects(Integer projectId, String serverKey, String channelKey, String controllerKey, Integer page, Integer size)
    {
        PageResult<List<GateVariable>> result = service.getVariable(projectId, serverKey, channelKey, controllerKey, page, size);
        List<GateVariable> list =result.getData() ;
        list.forEach(gateVariable -> {
            String value = redisHelper.HashGetFields(14, "var:" + gateVariable.getServerKey() + ":" + gateVariable.getChannelKey()
                    + ":" + gateVariable.getControllerKey() + ":" + gateVariable.getKeyValue(), "Value");
            String time = redisHelper.HashGetFields(14, "var:" + gateVariable.getServerKey() + ":" + gateVariable.getChannelKey()
                    + ":" + gateVariable.getControllerKey() + ":" + gateVariable.getKeyValue(), "time");
            gateVariable.setValue(value);
            gateVariable.setTime(time);
        });
        return GHResponse.ok(list,result.getTotal());
    }
}
