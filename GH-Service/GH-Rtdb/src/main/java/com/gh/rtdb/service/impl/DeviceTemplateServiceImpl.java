package com.gh.rtdb.service.impl;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.io.IORuntimeException;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gh.common.redis.RedisHelper;
import com.gh.rtdb.exception.CommonException;
import com.gh.rtdb.exception.ErrorEnum;
import com.gh.rtdb.mapper.DeviceTemplateMapper;
import com.gh.rtdb.model.dto.DevTemplateDTO;
import com.gh.rtdb.model.entity.DeviceTemplate;
import com.gh.rtdb.service.DeviceTemplateService;
import com.gh.rtdb.service.FileService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.Executor;

@Service
@Slf4j
public class DeviceTemplateServiceImpl extends ServiceImpl<DeviceTemplateMapper, DeviceTemplate> implements DeviceTemplateService {
    @Autowired
    private DeviceManagement deviceManagement;
    @Autowired
    private FileService fileService;
    @Autowired
    DeviceTemplateMapper deviceTemplateMapper;
    @Autowired
    private RedisHelper redisHelper;
    @Autowired
    Executor taskExecutor;
/* 设备物模板数据结构
    {
        "name:":"ioserver",
        "desc":"xxxxxxx",
        "channels"=[
                {
                    "name":"channel1",
                    "id"："1",
                    "controllers":
                        [
                            {
                                "name":"con1"
                                "id":"1"
                                "variables":[
                                            {
                                                "name":"var1"
                                                "id":"1"
                                            }
                                       ]
                             }
                       ]
                 }
             ]
    }
*/
   //自模板文件导入到redis的ioserver
    @Override
    public boolean exportTemplate(DevTemplateDTO devTemplateDTO) {
        try {
            String fileName= StrUtil.sub(devTemplateDTO.getTitle(),0,8) +System.currentTimeMillis()+".json";
            //查询物模板是否重复
            QueryWrapper<DeviceTemplate> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda()
                    .eq(DeviceTemplate::getTitle, devTemplateDTO.getTitle());
            List<DeviceTemplate> devs = deviceTemplateMapper.selectList(queryWrapper);

            if (!devs.isEmpty()) throw new CommonException(ErrorEnum.Error_Template_Repeat);

            //自REDIS导出模板文件
            String io = devTemplateDTO.getIosvrKey();
            JSONObject root = new JSONObject();

            List<JSONObject> chlist = new ArrayList<>();
            root.set("name", devTemplateDTO.getTitle());
            root.set("desc", "采集器物模板");

            List<String> chlKeys = deviceManagement.ChlGetAll(io);
            if (chlKeys.size() > 0) {
                chlKeys.forEach(k -> {
                    Map<String, String> chlProps = deviceManagement.ChlGetPropertyAll_V2(io, k);
                    JSONObject cha = JSONUtil.parseObj(chlProps);
                    chlist.add(cha);
                    root.set("channels", chlist);

                    List<JSONObject> conlist = new ArrayList<>();


                    List<String> conKeys = deviceManagement.CtrlGetAll(io, k);
                    conKeys.forEach(c -> {
                        Map<String, String> conProps = deviceManagement.CtrlGetPropertyAll_V2(io, k, c);
                        JSONObject con = JSONUtil.parseObj(conProps);
                        conlist.add(con);
                        cha.set("controllers", conlist);

                        List<JSONObject> varlist = new ArrayList<>();
                        List<String> varKeys = deviceManagement.VarGetAll(io, k, c);

                        CountDownLatch latch = new CountDownLatch(varKeys.size());
                        varKeys.forEach(v ->
                                //new Thread(() -> {
                                taskExecutor.execute(()-> {
                                            try {
                                                Map<String, String> varProps = deviceManagement.VarGetPropertyAll_V2(io, k, c, v);
                                                JSONObject var = JSONUtil.parseObj(varProps);
                                                varlist.add(var);
                                                con.set("variables", varlist);

                                            } finally {
                                                latch.countDown();
                                            }
                                        })

                                //}).start()
                         );

                        try {
                            latch.await();
                        } catch (InterruptedException e) {
                            e.printStackTrace();
                        }

                    });
                });
            }
            byte[] file=JSONUtil.toJsonStr(root).getBytes();
            fileService.saveFile(fileName,file , FileService.TEMPLATE_FOLDER);

            //在数据库增加模板文件记录
            DeviceTemplate deviceTemplate = new DeviceTemplate();
            deviceTemplate.setTitle((devTemplateDTO.getTitle()));
            deviceTemplate.setFileName(fileName);
            deviceTemplate.setCreateDate(DateTime.now());
            deviceTemplate.setLastDate(DateTime.now());
            deviceTemplate.setFileSize(file.length);
            deviceTemplate.setDescription(devTemplateDTO.getDescription());
            deviceTemplate.setProjectId(devTemplateDTO.getProjectId());
            deviceTemplateMapper.insert(deviceTemplate);
            return true;

        } catch (CommonException e) {
            throw e;
        }
        catch (Exception e) {
            e.printStackTrace();
            throw new CommonException(ErrorEnum.Error_Template);
        }
    }
    //自模板文件导入到redis的ioserver
    @Override
    public boolean importTemplate(Integer id,String iosvrKey ) {
        try {
            //导入前清除原来的IOSERVER数据
            clearRedisServer(iosvrKey);
            try {
                Thread.sleep(500);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
            DeviceTemplate template = this.getById(id);
            String fileName = fileService.getPath() + template.getFileName();

            File file=new File(fileName);

            JSONObject jsonObject = JSONUtil.readJSONObject(file, Charset.forName("UTF-8"));
            JSONArray chanArray=jsonObject.getJSONArray("channels");
            //遍历通道数据
            if(chanArray!=null && !chanArray.isEmpty())
            {
                log.info("导入模板文件：{}",fileName);
                chanArray.stream().forEach(o->{
                    JSONObject chJson=JSONUtil.parseObj(o);
                    //解析并保存通道属性
                    String chlId=chJson.getStr("ID");
                    String chKey=StrUtil.format("chl:{}:{}",iosvrKey,chlId);

                    JSONObject chlProp=new JSONObject();
                    chJson.keySet().forEach(k->{
                        if(!k.equals("controllers")) {
                            String value = chJson.getStr(k);
                            chlProp.set(k, value.toString());
                        }

                    });
                    //增加通道
                    redisHelper.SetAdd(4,StrUtil.format("RTDBSET:{}", iosvrKey),chKey);
                    redisHelper.setHash(12, chKey,chlProp);
                    //解析并导入控制器数据
                    JSONArray conArray = chJson.getJSONArray("controllers");
                    if(conArray!=null && !conArray.isEmpty())
                    {
                        conArray.stream().forEach(c-> {
                            JSONObject conJson=JSONUtil.parseObj(c);
                            String conId=conJson.getStr("ID");
                            //解析并保存控制器属性
                            String conKey = StrUtil.format("ctrl:{}:{}:{}", iosvrKey, chlId,conId);
                            JSONObject conProp = new JSONObject();
                            conJson.keySet().forEach(k -> {
                                if (!k.equals("variables")) {
                                    String value = conJson.getStr(k);
                                    conProp.set(k, value.toString());
                                }

                            });
                            redisHelper.SetAdd(4, StrUtil.format("RTDBSET:{}:{}", iosvrKey,chlId),conKey);
                            redisHelper.setHash(13, conKey, conProp);
                            //解析并导入变量数据
                            JSONArray varArray = conJson.getJSONArray("variables");
                            if(varArray!=null && !varArray.isEmpty())
                            {
                                varArray.stream().forEach(v-> {
                                    JSONObject varJson=JSONUtil.parseObj(v);
                                    String varId=varJson.getStr("ID");
                                    //解析并保存变量属性
                                    String varKey = StrUtil.format("var:{}:{}:{}:{}", iosvrKey, chlId,conId,varId);

                                    redisHelper.SetAdd(4, StrUtil.format("RTDBSET:{}:{}:{}", iosvrKey,chlId,conId),varKey);
                                    redisHelper.setHash(14, varKey, varJson);
                                });
                            }


                        });
                    }

                });
            }
            return true;
        } catch (IORuntimeException e) {
            throw new CommonException(ErrorEnum.Error_File_Load);
        }
    }

    @Override
    public boolean renameTemplate(Integer id, String name) {
        try {
            DeviceTemplate deviceTemplate = deviceTemplateMapper.selectById(id);
            deviceTemplate.setTitle(name);
            deviceTemplate.setLastDate(new Date());
            deviceTemplateMapper.updateById(deviceTemplate);
            return true;
        }catch(Exception e) {
            return false;
        }
    }
    @Override
    public  boolean  removeTemplate(String[] ids)
    {
        try {

            QueryWrapper<DeviceTemplate> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda()
                    .in(DeviceTemplate::getId,ids);
            List<DeviceTemplate> devs = deviceTemplateMapper.selectList(queryWrapper);
            devs.forEach(d->{
                fileService.removeFile(d.getFileName(),FileService.TEMPLATE_FOLDER);
            });
            deviceTemplateMapper.delete(queryWrapper);
            return true;
        }catch(Exception e) {

            return false;
        }
    }
    private void clearRedisServer(String key) {
        //清除db4中通道、控制器、变量SET
        redisHelper.KeyDeletePattern(4, "RTDBSET:" + key + "*");
        //redisHelper.KeyDelete(11, "iosvr:" + key);  //保留采集器
        redisHelper.KeyDeletePattern(12, "chl:" + key + "*");
        redisHelper.KeyDeletePattern(13, "ctrl:" + key + "*");
        redisHelper.KeyDeletePattern(14, "var:" + key + "*");
    }

}
