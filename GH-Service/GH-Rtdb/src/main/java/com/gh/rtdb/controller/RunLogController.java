package com.gh.rtdb.controller;

import com.gh.common.model.entity.RunLog;
import com.gh.common.utils.GHResponse;
import com.gh.common.utils.PageResult;
import com.gh.rtdb.service.RunLogService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.List;

@RestController
@RequestMapping("/runLog")
@Api(tags = "智慧 策略 手动 模式运行记录")
public class RunLogController {

    @Autowired
    private RunLogService runLogService;

    @GetMapping
    @ApiOperation("查询智控历史数据")
    public GHResponse<List<RunLog>> Selects(Integer type, Integer projectId, Integer menuId,
                                            Integer tag,
                                            Integer userId,
                                            Integer deviceId,
                                            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")Date bt,
                                            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")Date et,
                                            Integer page, Integer size)
    {
        PageResult<List<RunLog>> select = runLogService.select(type, projectId, menuId, bt, et, tag,userId,deviceId,page, size);

        return GHResponse.ok(select.getData(),select.getTotal());
    }
    @GetMapping("manual")
    @ApiOperation("查询智控历史数据")
    public GHResponse<List<RunLog>> manualLog( Integer menuId,

                                            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")Date bt,
                                            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")Date et,
                                            Integer page, Integer size)
    {
        PageResult<List<RunLog>> select = runLogService.runLog(menuId, bt, et, page, size);

        return GHResponse.ok(select.getData(),select.getTotal());
    }


    @GetMapping("all")
    @ApiOperation("所有设备日志记录")
    public GHResponse<List<RunLog>> manualLog( String  keyword, Integer projectId,

                                               @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")Date bt,
                                               @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")Date et,
                                               Integer page, Integer size,Integer menuId,Integer deviceId)
    {
        PageResult<List<RunLog>> select = runLogService.runAllLog(keyword, bt, et, page, size,projectId,menuId,deviceId);

        return GHResponse.ok(select.getData(),select.getTotal());
    }



    @GetMapping("page")
    @ApiOperation("所有设备日志记录")
    public GHResponse<List<RunLog>> manualLogPage( String  keyword, Integer projectId,

                                               @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")Date bt,
                                               @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")Date et,
                                               @RequestParam(defaultValue = "1") Integer page,@RequestParam(defaultValue = "10") Integer size,Integer menuId,Integer deviceId)
    {
        List<RunLog> select = runLogService.getAllRunLogOptimized(keyword, bt, et, (page-1)*size, size,projectId,menuId,deviceId);

        long count=runLogService.countAllRunLogOptimized(projectId, keyword, bt, et, menuId, deviceId);
        return GHResponse.ok(select,count);
    }


    @GetMapping("one")
    @ApiOperation("所有设备日志记录")
    public GHResponse<RunLog> runLogById(String requestId)
    {
         RunLog one = runLogService.lambdaQuery().eq(RunLog::getRequestId, requestId)
                 .oneOpt()
                 .orElse(null);
        return GHResponse.ok(one);
    }




}
