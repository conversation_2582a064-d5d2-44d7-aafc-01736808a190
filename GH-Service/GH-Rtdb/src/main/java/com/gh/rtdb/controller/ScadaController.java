package com.gh.rtdb.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.XmlUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.gh.common.model.dto.*;
import com.gh.common.model.entity.RunLog;
import com.gh.common.redis.RedisHelper;
import com.gh.common.utils.GHResponse;
import com.gh.common.utils.RequestUtil;
import com.gh.rtdb.exception.CommonException;
import com.gh.rtdb.exception.ErrorEnum;
import com.gh.rtdb.kafka.KafkaProduce;
import com.gh.rtdb.mapper.RunLogMapper;
import com.gh.rtdb.model.dto.*;
import com.gh.rtdb.model.entity.GateWay;
import com.gh.rtdb.model.vo.DevIOServer;
import com.gh.rtdb.model.vo.DevVariable;
import com.gh.rtdb.model.vo.VarPropVo;
import com.gh.rtdb.model.vo.WriteVO;
import com.gh.rtdb.service.GateService;
import com.gh.rtdb.service.RunLogService;
import com.gh.rtdb.service.impl.DeviceManagement;
import com.gh.scene.feign.client.SceneFeignClient;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.w3c.dom.Document;
import org.w3c.dom.Element;

import javax.servlet.http.HttpServletRequest;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.transform.OutputKeys;
import javax.xml.transform.Transformer;
import javax.xml.transform.TransformerFactory;
import javax.xml.transform.dom.DOMSource;
import javax.xml.transform.stream.StreamResult;
import java.io.ByteArrayOutputStream;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@RestController
@RequestMapping(value = "/api")
@Api(tags = "实时数据处理")
@Slf4j
public class ScadaController {

    @Autowired
    private RedisHelper redisHelper;

    @Autowired
    private DeviceManagement deviceManagement;
    @Autowired
    private KafkaProduce produce;

    @Autowired
    private GateService gateService;

    @Autowired
    private SceneFeignClient sceneFeignClient;

    @Autowired
    private RunLogService runLogService;


    @Autowired
    private RunLogMapper runLogMapper;

    @Autowired
    private Executor taskExecutor;
    /**
     * 获取ioServer
     *
     * @param projectId
     * @return
     */
    @GetMapping("/{projectId}")

    public ResponseEntity<GHMNSResponse> Get(@PathVariable(value = "projectId", required = false) Integer projectId) {
        GHMNSResponse responseModel = new GHMNSResponse();
        responseModel.setSuccess(true);
        responseModel.setRequestID(UUID.randomUUID().toString());
        List<String> keys = gateService.list(Wrappers.<GateWay>lambdaQuery().eq(GateWay::getProjectId, projectId))
                .stream().map(gateWay -> gateWay.getGateId()).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(keys)) {
            List<String> ioSvrKeys = redisHelper.SetMembers(4, "RTDBSET").stream().map(t -> (StrUtil.isNotBlank(t) && t.contains(":")&&t.split(":").length==2) ? t.split(":")[1] : "")
                    .collect(Collectors.toList());
            if (CollUtil.isNotEmpty(ioSvrKeys)) {
                ioSvrKeys = ioSvrKeys.stream().filter(k -> keys.contains(k)).collect(Collectors.toList());
                List<JSONObject> ioValues = deviceManagement.IOServerGetValues(ioSvrKeys);
                List<DevIOServer> ioModels = ioValues.stream().filter(t -> StrUtil.isNotBlank(t.toString()))
                        .map(t -> JSONUtil.toBean(t, DevIOServer.class))
                        .sorted(Comparator.comparing(DevIOServer::getName))
                        .collect(Collectors.toList());
                responseModel.setSuccess(true);
                responseModel.setData(ioModels);
            }
        }
        return ResponseEntity.ok(responseModel);
    }

    /**
     * 获取通道
     *
     * @param io
     * @param chl
     * @return
     */
    @GetMapping("/{io}/{chl}")
    public ResponseEntity<GHMNSResponse> Get(@PathVariable("io") String io, @PathVariable(value = "chl", required = false) String chl) {
        GHMNSResponse responseModel = new GHMNSResponse();
        responseModel.setSuccess(true);
        responseModel.setRequestID(UUID.randomUUID().toString());
        List<String> chlKeys = deviceManagement.ChlGetAll(io);
        if (chlKeys.size() > 0) {
            List<JSONObject> chlValues = deviceManagement.ChlGetValues(io, chlKeys)
                    .stream().filter(jsonObject -> jsonObject.get("Name")!=null).collect(Collectors.toList());
            List<JSONObject> dev1 = chlValues.stream().sorted(Comparator.comparing(jsonObject -> jsonObject.get("Name").toString()))
                    .collect(Collectors.toList());

            responseModel.setSuccess(true);

            responseModel.setData(dev1);
        }
        return ResponseEntity.ok(responseModel);
    }

    /**
     * 获取控制器
     *
     * @param io
     * @param chl
     * @param ctrl
     * @return
     */
    @GetMapping("{io}/{chl}/{ctrl}")
    public ResponseEntity<GHMNSResponse> Get(@PathVariable("io") String io, @PathVariable("chl") String chl,
                                             @PathVariable(value = "ctrl", required = false) String ctrl) {
        GHMNSResponse responseModel = new GHMNSResponse();

        List<String> ctrlKeys = deviceManagement.CtrlGetAll(io, chl);
        if (ctrlKeys.size() > 0) {
            List<JSONObject> ctrlValues = deviceManagement.CtrlGetValues(io, chl, ctrlKeys)
                    .stream().filter(jsonObject -> jsonObject.get("Name")!=null).collect(Collectors.toList());
//            List<DevController> ctrlModels = ctrlValues.stream().filter(t -> StrUtil.isNotBlank(t.toString()))
//                    .map(t -> JSONUtil.toBean(t, DevController.class))
//                    .sorted(Comparator.comparing(DevController::getName))
//                    .collect(Collectors.toList());
            List<JSONObject> dev1 = ctrlValues.stream().sorted(Comparator.comparing(jsonObject -> jsonObject.get("Name").toString()))
                    .collect(Collectors.toList());
            responseModel.setSuccess(true);
            responseModel.setData(dev1);
        }
        return ResponseEntity.ok(responseModel);
    }

    /**
     * 获取变量
     *
     * @param io
     * @param chl
     * @param ctrl
     * @param var
     * @return
     */
    @GetMapping("{io}/{chl}/{ctrl}/{var}")
    public ResponseEntity<GHMNSResponse> Get(@PathVariable("io") String io, @PathVariable("chl") String chl,
                                             @PathVariable("ctrl") String ctrl, @PathVariable("var") String var) {
        GHMNSResponse responseModel = new GHMNSResponse();
        responseModel.setRequestID(UUID.randomUUID().toString());
        if (!var.equals("var"))//获取单个变量值,可以用Jobject优化
        {
            DevVariable varModel = new DevVariable();
            Object[] fields = new Object[]{"Value"};
            List<Object> values = deviceManagement.VarGetPropertys_V2(io, chl, ctrl, var, CollUtil.toList(fields));

            varModel.setValue(values.get(0).toString());


            responseModel.setSuccess(true);
            responseModel.setData(varModel);
        } else//获取所有变量值
        {
            List<String> varKeys = deviceManagement.VarGetAll(io, chl, ctrl);
            if (varKeys.size() > 0) {
                List<JSONObject> devs = new ArrayList<>();
                varKeys.forEach(k -> {
//                    DevVariable varModel = new DevVariable();
//                    Object[] fields = new Object[]{"ID", "Name", "Value"};
                    Map<String, String> map = deviceManagement.VarGetPropertyAll_V2(io, chl, ctrl, k);
//                    varModel.setID(Optional.ofNullable(values.get(0)).orElse("").toString());
//                    varModel.setName(Optional.ofNullable(values.get(1)).orElse("").toString());
//                    varModel.setValue(Optional.ofNullable(values.get(2)).orElse("").toString());
                    JSONObject object = new JSONObject();
                    object.putAll(map);
                    devs.add(object);
                });
                List<JSONObject> dev1 = devs.stream()
                        .filter(jsonObject -> jsonObject.get("Name")!=null)
                        .sorted(Comparator.comparing(jsonObject -> jsonObject.get("Name").toString()))
                        .collect(Collectors.toList());
                responseModel.setSuccess(true);
                responseModel.setData(dev1);
            }
        }
        return ResponseEntity.ok(responseModel);
    }


    /**
     * 获取变量某个属性值
     * @param io
     * @param chl
     * @param ctrl
     * @param var
     * @param prop
     * @return
     */
    @GetMapping("{io}/{chl}/{ctrl}/{var}/{prop}")
    public ResponseEntity<GHMNSResponse> GetProp(@PathVariable("io") String io, @PathVariable("chl") String chl, @PathVariable("ctrl") String ctrl, @PathVariable("var") String var, @PathVariable("prop") String prop) {
        GHMNSResponse responseModel = new GHMNSResponse();
        responseModel.setRequestID(UUID.randomUUID().toString());
        DevVariable varModel = new DevVariable();
        if (StrUtil.isEmpty(prop)) {
            prop = "Value";
        }
        String value = deviceManagement.VarGetPropertys_V2(io, chl, ctrl, var, prop);
        varModel.setValue(value);
        responseModel.setSuccess(true);
        responseModel.setData(varModel);
        return ResponseEntity.ok(responseModel);
    }

    /**
     * 获取一组变量值
     *
     * @param value
     * @return
     */
    @PostMapping("/LiveData")
    public ResponseEntity<LiveDataResponse> LiveData(@RequestBody LiveDataRequest value) {
        LiveDataResponse responseModel = new LiveDataResponse();
        List<LiveData> liveData = new ArrayList<>();
        value.getRequestItems().forEach(entity -> {
            String prop = StrUtil.isEmpty(entity.getPropName()) ? "Value" : entity.getPropName();
            String valueRedis="";
            Integer stateRedis=0;
            Integer typeRedis=0;
            if(StrUtil.isNotEmpty(entity.getPropName()))
            {
                if(entity.getPropName().equalsIgnoreCase("ioState"))
                {
                    prop="OnlineState";
                    entity.setPropName("OnlineState");
                    valueRedis= redisHelper.HashGetFields(11,"iosvr:"+entity.getIosvrKey(),prop);
                }else if(entity.getPropName().equalsIgnoreCase("chlState"))
                {
                    prop="Active";
                    entity.setPropName("Active");
                    valueRedis= redisHelper.HashGetFields(12,"chl:"+entity.getIosvrKey()+":"+entity.getChlKey(),prop);
                }else if (entity.getPropName().equalsIgnoreCase("ctrlState"))
                {
                    prop="Active";
                    entity.setPropName("Active");
                    valueRedis= redisHelper.HashGetFields(13,"ctrl:"+entity.getIosvrKey()+":"+entity.getChlKey()+":"+entity.getCtrlKey(),prop);
                }else
                {
                    List<Object> fls=new ArrayList<>();
                    fls.add(prop);
                    fls.add("ValueType");
                    fls.add("Active");
                    List<Object> rets= redisHelper.HashGetFields(14,"var:"+ entity.getIosvrKey()+":"+entity.getChlKey()+":"+entity.getCtrlKey()+":"+entity.getVarKey(),fls);
                    if(rets.get(0)!=null)
                        valueRedis= rets.get(0).toString();
                    if(rets.get(1)!=null)
                        typeRedis=GetCode(rets.get(1).toString());
                    if(rets.get(2)!=null)
                        stateRedis=rets.get(2).toString().toLowerCase().equals("true")?1:0;
                }
            }
            LiveData data = LiveData.builder().area(value.getArea())
                    .id(entity.getId())
                    .itemStatus(stateRedis)
                    .level(value.getLevel())
                    .timestamp(DateUtil.now())
                    .value(valueRedis)
                    .success(true)
                    .propName(entity.getPropName())
                    .valueType(typeRedis).build();
            liveData.add(data);
        });
        responseModel.setCode(200);
        responseModel.setData(liveData);
        responseModel.setClientId(value.getClientId());
        responseModel.setBatchDefinitionId(value.getBatchDefinitionId());
//        if(value.isRealTime()&&value.RequestItems.size()>0)
//        {
//            value.RequestItems.stream().collect(Collectors.groupingBy(LiveDataRequestItem::getIosvrKey))
//                    .forEach((k,v)->{
//                        List<String> data = v.stream().map(liveDataRequestItem -> liveDataRequestItem.getChlKey() + ":" + liveDataRequestItem.getCtrlKey() + ":" + liveDataRequestItem.getVarKey()).collect(Collectors.toList());
//                        produce.Produce("variable_realtime_set",k+"&"+JSONUtil.toJsonStr(data));
//                    });
//
//        }
        return ResponseEntity.ok(responseModel);
    }

    @PostMapping("/realTime")
    public GHResponse realTime(@RequestBody LiveDataRequest value) {

        if(value.RequestItems.size()>0)
        {
            value.RequestItems.stream().collect(Collectors.groupingBy(LiveDataRequestItem::getIosvrKey))
                    .forEach((k,v)->{
                        List<String> data = v.stream().map( liveDataRequestItem ->  liveDataRequestItem.getChlKey() + ":" + liveDataRequestItem.getCtrlKey() + ":" + liveDataRequestItem.getVarKey()).collect(Collectors.toList());
                        MqttMsg mqttMsg = MqttMsg.builder()
                                .data(JSONUtil.toJsonStr(data))
                                .build();
                        produce.Produce("variable_realtime_set",k+"&"+JSONUtil.toJsonStr(mqttMsg));
                    });
        }
        return GHResponse.ok();
    }





    /**
     * 变量写值
     *
     * @return
     */
    @PostMapping("writeVariable")
//    @OperLog(category = "RTDB服务",description = "变量写值")
    public ResponseEntity<JSONObject> Post(@RequestBody WriteDataRequestV1 writeDataRequestV1) {
        try {
            if (null != writeDataRequestV1) {
                Map<String, List<WriteDataV1>> map = new HashMap<>();

                if (null != writeDataRequestV1 && CollUtil.isNotEmpty(writeDataRequestV1.getRequestItems())) {

                    writeDataRequestV1.getRequestItems().forEach(writeData -> {
                        boolean containsKey = map.containsKey(writeData.getIosvrKey());
                        if (!containsKey) {
                            map.put(writeData.getIosvrKey(), new ArrayList<>());
                        }
                        map.get(writeData.getIosvrKey()).add(writeData);
                    });
                }
                if (CollUtil.isNotEmpty(map)) {
                    map.keySet().forEach(s -> {
                        WriteDataRequestV1 build = WriteDataRequestV1.builder().build();
                        BeanUtil.copyProperties(writeDataRequestV1, build);
                        build.setRequestItems(map.get(s));
                        produce.Produce("gh_mqtt_write", s + "&" + JSONUtil.toJsonStr(build.toV2()));
                        map.get(s).forEach(item -> {
                            String variable=item.getIosvrKey()+":"+item.getChlKey()+":"+item.getCtrlKey()+":"+item.getVarKey();
                            List<RunLog> runLogs = runLogMapper.getDeviceStdByVar(variable, null);
                            String oldValue = redisHelper.HashGetFields(14, "var:" + variable, "Value");
                            if(CollUtil.isNotEmpty(runLogs))
                            {
                                runLogs.forEach(runLog -> {
                                    runLog.setUserId(RequestUtil.getUserId());
                                    runLog.setLogTime(DateUtil.date());
                                    runLog.setNewValue(item.getValue());
                                    runLog.setOldValue(oldValue);
                                    runLog.setVariable(variable);
                                    runLog.setType(4);
                                    runLog.setProjectId(writeDataRequestV1.getProjectId()!=null? writeDataRequestV1.getProjectId():2);
                                    runLogMapper.insert(runLog);
                                });
                            }else
                            {
                                RunLog runLog = RunLog.builder().logTime(DateUtil.date())
                                        .userId(RequestUtil.getUserId())
                                        .newValue(item.getValue())
                                        .oldValue(oldValue)
                                        .variable(variable)
                                        .projectId(writeDataRequestV1.getProjectId()!=null? writeDataRequestV1.getProjectId():2)
                                        .type(4)
                                        .build();
                                runLogMapper.insert(runLog);
                            }

                        });
                    });
                }
                return ResponseEntity.ok(new JSONObject().set("success", true));
            }

        } catch (Exception e) {
            throw new CommonException(ErrorEnum.Error_Kafka_Send);
        }
        return ResponseEntity.ok(new JSONObject().set("success", false));
    }
    @PostMapping("write")
//    @OperLog(category = "RTDB服务",description = "变量写值")
    public GHResponse write(@RequestBody List<WriteVO> values) {
        if(CollUtil.isNotEmpty(values))
        {
            Map<String,WriteDataRequestV2> map=new HashMap<>();
            for(WriteVO vo:values)
            {
                if(StrUtil.isNotEmpty(vo.getValue())&&StrUtil.isNotEmpty(vo.getVariable())
                &&vo.getVariable().split(":").length==4)
                {
                    String[] data = vo.getVariable().split(":");
                    if(!map.containsKey(data[0]))
                    {
                        WriteDataRequestV2 writeDataRequestV2=new WriteDataRequestV2();
                        writeDataRequestV2.setData(new ArrayList<>());
                        map.put(data[0],writeDataRequestV2);
                    }
                    WriteDataRequestV2 requestV2 = map.get(data[0]);
                    List<WriteDataV2> v2List = requestV2.getData();
                    String uuid= RandomUtil.randomString(32);
                    WriteDataV2 writeDataV2 = WriteDataV2.builder()
                            .batchDefinitionId("")
                            .clientId("")
                            .level(0)
                            .desc("")
                            .propName("Value")
                            .iosvrKey(data[0])
                            .chlKey(data[1])
                            .ctrlKey(data[2])
                            .varKey(data[3])
                            .value(vo.getValue())
                            .id(uuid)
                            .build();
                    v2List.add(writeDataV2);
                    requestV2.setData(v2List);
                    //平顶山项目
                    RunLog runLog = RunLog.builder()
                            .deviceName(null)
                            .logTime(DateUtil.date())
                            .newValue(vo.getValue())
                            .oldValue(null)
                            .standardName(null)
                            .userId(1)
                            .type(3)
                            .deviceId(null)
                            .requestId(uuid)
                            .projectId(2)
                            .tag(2)
                            .variable(vo.getVariable())
                            .menuId(null).build();
                    runLogMapper.insert(runLog);
                }
            }
            if(CollUtil.isNotEmpty(map))
            {
                map.keySet().forEach(key->{
                    produce.Produce("gh_mqtt_write",  key+ "&" + JSONUtil.toJsonStr(map.get(key)));
                });
                return GHResponse.ok("操作成功");
            }
            return GHResponse.failed("操作失败");
        }
        else
        {
            return GHResponse.failed("参数数组不能为空");
        }

    }

    /**
     * 变量写值 自定义格式
     *
     * @return
     */
    @PostMapping("writeCustom")
//    @OperLog(category = "RTDB服务",description = "自定义写值")
    public ResponseEntity<JSONObject> PostCustom(@RequestBody WriteCustomDataRequestV1 data) {
        try {
            WriteCustomDataRequestV2 wr2=data.toV2();
            for (WriteCustomDataV2 writeCustomData : wr2.getData()) {
                Object iosvrKey = writeCustomData.getIosvrKey();
                if (null != iosvrKey) {
                    produce.Produce("gh_mqtt_write_custom", iosvrKey + "&" + JSONUtil.toJsonStr(data));
                }
            }
        } catch (Exception e) {
            throw new CommonException(ErrorEnum.Error_Kafka_Send);
        }
        return ResponseEntity.ok(new JSONObject().set("success", true));
    }



    /**
     * 变量写值 自定义格式
     *
     * @return
     */
    @GetMapping("loadConfig/{io}")
    public ResponseEntity<byte[]> LoadConfig(@PathVariable("io") String io, HttpServletRequest request) {
        ByteArrayOutputStream ret = new ByteArrayOutputStream();
        DocumentBuilderFactory fac = DocumentBuilderFactory.newInstance();
        try {

            Document doc = XmlUtil.createDocumentBuilderFactory().newDocumentBuilder().newDocument();
            doc.setXmlStandalone(true);
            Element root = doc.createElement("Channels");
            doc.appendChild(root);


            List<String> chlKeys = deviceManagement.ChlGetAll(io);
            if (chlKeys.size() > 0) {
                chlKeys.forEach(k -> {
                            Map<String, String> chlProps = deviceManagement.ChlGetPropertyAll_V2(io, k);
                            Element elCh = doc.createElement(chlProps.get("PlugID"));
                            root.appendChild(elCh);
                            chlProps.forEach((name, value) -> {
                                elCh.setAttribute(name, value);
                            });

                            List<String> conKeys = deviceManagement.CtrlGetAll(io, k);
                            conKeys.forEach(c -> {
                                Map<String, String> conProps = deviceManagement.CtrlGetPropertyAll_V2(io, k, c);
                                Element elCon = doc.createElement("Controller");
                                elCh.appendChild(elCon);
                                conProps.forEach((name, value) -> {
                                    elCon.setAttribute(name, value);
                                });
                                List<String> varKeys = deviceManagement.VarGetAll(io, k, c);
                                log.error("B-----" + c + "---" + DateTime.now());
                                CountDownLatch latch = new CountDownLatch(varKeys.size());
                                //线程计数器（等待所有线程执行完统一返回）
                                varKeys.forEach(v ->

                                        taskExecutor.execute(() -> {
                                            try {
                                                Map<String, String> varProps = deviceManagement.VarGetPropertyAll_V2(io, k, c, v);
                                                Element elVar = doc.createElement("Variable");
                                                elCon.appendChild(elVar);
                                                varProps.forEach((name, value) -> {
                                                    elVar.setAttribute(name, value);
                                                });
                                            } finally {
                                                latch.countDown();

                                            }

                                        })

                                );
                                try {
                                    latch.await(10, TimeUnit.SECONDS);
                                } catch (InterruptedException e) {
                                    e.printStackTrace();
                                }

                            });
                        }
                );


                Transformer transformer = TransformerFactory.newInstance().newTransformer();

                transformer.setOutputProperty(OutputKeys.ENCODING, "UTF-8");
                transformer.setOutputProperty("version", "1.0");
                transformer.setOutputProperty("{http://xml.apache.org/xslt}indent-amount", "2");
                transformer.setOutputProperty(OutputKeys.INDENT, "yes");

                DOMSource source = new DOMSource(doc);
                transformer.transform(source, new StreamResult(ret));

                HttpHeaders headers = new HttpHeaders();
                headers.add("Content-Disposition", "attchement;filename=" + io + ".xml");
                HttpStatus StatusCode = HttpStatus.OK;

                ResponseEntity<byte[]> entity = new ResponseEntity<byte[]>(ret.toByteArray(), headers, StatusCode);

                return entity;

            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return ResponseEntity.ok(ret.toByteArray());
    }




    /**
     * 获取通道下所有变量的属性
     *
     * @param io
     * @param chan
     * @param ctrl
     * @return
     */
    @GetMapping("GetCtrlVars/{io}/{chan}/{ctrl}")
    public ResponseEntity<GHMNSResponse> GetCtrlVars(@PathVariable("io") String io, @PathVariable("chan") String chan,
                                                     @PathVariable("ctrl") String ctrl) {
        GHMNSResponse model = new GHMNSResponse();
        List<JSONObject> json = new ArrayList<>();
        List<String> vars = deviceManagement.VarGetAll(io, chan, ctrl);
        if (vars.size() > 0) {
            vars.forEach(vid -> {
                Map<String, String> props = deviceManagement.VarGetPropertyAll_V2(io, chan, ctrl, vid);
                JSONObject pJson = JSONUtil.createObj();
                props.forEach((key, value) -> pJson.set(key, value));
                json.add(pJson);
            });
            model.setSuccess(true);
            model.setData(json);
        } else {
            model.setSuccess(false);
            // model.message = "未查询到数据！";
        }
        return ResponseEntity.ok(model);
    }

    /**
     * 国铁获取状态
     *
     * @return
     */
    @GetMapping("/active/{projectId}")
    public GHResponse<JSONObject> getServerStatus(@PathVariable("projectId") int projectId) {
        //获取project关连的IOServer
        List<String> keys = gateService.list(Wrappers.<GateWay>lambdaQuery().eq(GateWay::getProjectId, projectId))
                .stream().map(gateWay -> gateWay.getGateId()).collect(Collectors.toList());


        JSONObject data = new JSONObject();
        data.set("name", "采集服务器");

        List<JSONObject> ioList = new ArrayList<>();
        Set<String> servers = redisHelper.SetMembers(4, "RTDBSET");

        if (CollUtil.isNotEmpty(servers)) {
            servers.forEach(s -> {
                if(s.split(":").length==2) {
                    if (keys.contains(s.split(":")[1])) {
                        JSONObject object = redisHelper.getHash(11, s);
                        Object name = object.get("Name");
                        Object active = object.get("OnlineState");
                        JSONObject ioObject = new JSONObject();
                        ioObject.set("name", name != null ? name.toString() : "");
                        ioObject.set("active", active != null ? active.toString() : "false");
                        JSONObject itemStyle = new JSONObject();
                        String color = active != null && active.toString().equals("true") ? "green" : "red";
                        itemStyle.set("color", color);
                        itemStyle.set("borderColor", color);
                        ioObject.set("itemStyle", itemStyle);

                        ioList.add(ioObject);
                        //通道
                        Set<String> chls = redisHelper.SetMembers(4, "RTDBSET:" + s.split(":")[1]);
                        List<JSONObject> chlList = new ArrayList<>();
                        chls.forEach(c -> {

                            JSONObject chlObject = redisHelper.getHash(12, c);
                            JSONObject oChl = new JSONObject();
                            oChl.set("name", chlObject.get("Name") == null ? "" : chlObject.get("Name").toString());
                            oChl.set("active", chlObject.get("Active") == null ? "false" : chlObject.get("Active").toString());
                            String chActive = chlObject.get("Active") == null ? "false" : chlObject.get("Active").toString();
                            JSONObject itemStyleCh = new JSONObject();
                            String colorCh = chActive != null && chActive.toLowerCase().equals("true") ? "green" : "red";
                            itemStyleCh.set("color", colorCh);
                            itemStyleCh.set("borderColor", colorCh);
                            oChl.set("itemStyle", itemStyleCh);
                            //控制器
                            Set<String> controls = redisHelper.SetMembers(4, StrUtil.replace(c, "chl", "RTDBSET"));

                            List<JSONObject> controlList = new ArrayList<>();
                            controls.forEach(control -> {
                                JSONObject oCn = new JSONObject();
                                JSONObject conObject = redisHelper.getHash(13, control);
                                oCn.set("name", conObject.get("Name") == null ? "" : conObject.get("Name").toString());
                                oCn.set("active", conObject.get("Active") == null ? "false" : conObject.get("Active").toString());
                                String conActive = conObject.get("Active") == null ? "false" : conObject.get("Active").toString();
                                JSONObject itemStyleCon = new JSONObject();
                                String colorCon = conActive != null && conActive.toLowerCase().equals("true") ? "green" : "red";
                                itemStyleCon.set("color", colorCon);
                                itemStyleCon.set("borderColor", colorCon);
                                oCn.set("itemStyle", itemStyleCon);
                                controlList.add(oCn);
                            });
                            oChl.set("children", controlList);
                            chlList.add(oChl);

                        });
                        ioObject.set("children", chlList);
                    }
                }
            });

        }
        data.set("children", ioList);
        return GHResponse.ok(data);
    }


    @PostMapping("edit")
    @ApiModelProperty("编辑属性")
//    @OperLog(category = "RTDB服务",description = "编辑属性")
    public ResponseEntity editProp(@RequestBody VarPropVo data) {
        redisHelper.setHash(data.getDb(), data.getKey(), data.getData());
        return ResponseEntity.ok(new JSONObject().set("success", true));
    }

    @GetMapping("group")
    @ApiOperation("手动控制组控制")
//    @OperLog(category = "RTDB服务",description = "手动控制组控制")
    public ResponseEntity ManualCtrl(Integer id, Boolean cmd) {
        Integer type = 1;//启动
        if (!cmd) {
            type = 2;//控制组
        }
        GHResponse<List<GroupVarDto>> response = sceneFeignClient.SelectGroupVar(id, type);
        if (null != response && CollUtil.isNotEmpty(response.getData())) {
            WriteDataRequestV2 writeDataRequest = WriteDataRequestV2.builder().build();
            List<WriteDataV2> requestItems = new ArrayList<>();
            List<RunLog> runLogs= new ArrayList<>();
            response.getData().forEach(groupVarDto -> {
                String variable = groupVarDto.getVariable();
                if (StrUtil.isNotEmpty(variable) && StrUtil.isNotEmpty(groupVarDto.getVarValue())) {
                    String[] varSplit = variable.split(":");
                    String uuid=RandomUtil.randomString(32);
                    WriteDataV2 writeData = WriteDataV2.builder()
                            .batchDefinitionId("scada_write")
                            .clientId("scada_write").level(1)
                            .chlKey(varSplit[1])
                            .iosvrKey(varSplit[0])
                            .ctrlKey(varSplit[2])
                            .varKey(varSplit[3])
                            .propName("Value")
                            .id(uuid)
                            .value(groupVarDto.getVarValue()).build();
                    requestItems.add(writeData);

                    RunLog runLog = RunLog.builder()
                            .deviceName(null)
                            .logTime(DateUtil.date())
                            .newValue(groupVarDto.getVarValue())
                            .oldValue(null)
                            .standardName(null)
                            .userId(1)
                            .type(3)
                            .deviceId(null)
                            .requestId(uuid)
                            .projectId(2)
                            .tag(2)
                            .variable(variable)
                            .menuId(null).build();
                    runLogs.add(runLog);
                }
            });
            runLogService.saveBatch(runLogs);
            writeDataRequest.setData(requestItems);
            if (CollUtil.isNotEmpty(writeDataRequest.getData()))
                produce.Produce("gh_mqtt_write", writeDataRequest.getData().get(0).getIosvrKey() + "&" + JSONUtil.toJsonStr(writeDataRequest));
        }
        return ResponseEntity.ok(new JSONObject().set("success", true));
    }


    @PostMapping("del")
    @ApiModelProperty("根据ioserver清空redis内容")
//    @OperLog(category = "RTDB服务",description = "根据ioserver清空redis内容")
    public ResponseEntity delServer(@RequestParam String key) {
        redisHelper.KeyDeletePattern(4, "RTDBSET:" + key + "*");
        redisHelper.KeyDelete(11,"iosvr:"+key);
        redisHelper.KeyDeletePattern(12, "chl:" + key + "*");
        redisHelper.KeyDeletePattern(13, "ctrl:" + key + "*");
        redisHelper.KeyDeletePattern(14, "var:" + key + "*");
        return ResponseEntity.ok(new JSONObject().set("success", true));
    }

    private  int  GetCode(String TypeName) {
        switch (TypeName) {
            case "SByte":
            case "Byte":
            case "Int16":
            case "Int32":
            case "Int64":
            case "UInt16":
            case "UInt32":
            case "UInt64":
            case "Decimal":
                return 0;
            case "Single":
            case "Double":
                return 1;
            case "Boolean":
                return 2;
            default:
                return 3;
        }
    }
}
