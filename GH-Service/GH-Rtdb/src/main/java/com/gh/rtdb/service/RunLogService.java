package com.gh.rtdb.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.gh.common.model.entity.RunLog;
import com.gh.common.utils.PageResult;

import java.util.Date;
import java.util.List;


public interface RunLogService extends IService<RunLog> {
     PageResult<List<RunLog>> select(Integer type, Integer projectId, Integer menuId, Date bt, Date et, Integer tag, Integer userId, Integer deviceId, Integer page, Integer size);

     PageResult<List<RunLog>> runLog( Integer menuId, Date bt, Date et, Integer page, Integer size);


     PageResult<List<RunLog>> runAllLog( String keyword, Date bt, Date et, Integer page, Integer size,Integer projectId,Integer menuId,Integer deviceId);


     List<RunLog> getAllRunLogOptimized(String keyword, Date bt, Date et, Integer limit, Integer offset,Integer projectId,Integer menuId,Integer deviceId);

     Long countAllRunLogOptimized(Integer projectId, String keyword, Date bt, Date et,Integer menuId,Integer deviceId);

}
