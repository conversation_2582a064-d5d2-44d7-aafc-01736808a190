package com.gh.rtdb.model.dto;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class LiveData {
    private String id;
    private Integer itemStatus;
    private Object value;
    private Integer valueType;
    private String timestamp;
    private Integer level;
    private Integer area;
    private boolean success;
    private String desc;
    private String propName;
}
