package com.gh.rtdb.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.gh.common.utils.PageResult;
import com.gh.rtdb.model.entity.GateVariable;

import java.util.List;

public interface GateVariableService extends IService<GateVariable> {
    PageResult<List<GateVariable>> getVariable(Integer projectId, String serverKey, String channelKey, String controllerKey, Integer page, Integer size);
}
