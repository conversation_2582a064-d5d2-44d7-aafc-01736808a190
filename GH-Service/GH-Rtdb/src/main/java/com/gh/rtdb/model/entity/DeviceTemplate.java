package com.gh.rtdb.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;
@Data
@TableName(value = "GHRTDB_TEMPLATE")
public class DeviceTemplate {
    @TableId(value = "id", type=IdType.AUTO)
    private Integer id;

    @TableField("title")
    private String title;

    @TableField("description")
    private String description;

    @TableField("fileName")
    private String fileName;

    @TableField("fileSize")
    private Integer fileSize;

    @TableField("createDate")
    private Date createDate;

    @TableField("lastDate")
    private Date lastDate;

    @TableField("projectId")
    private Integer projectId;
}
