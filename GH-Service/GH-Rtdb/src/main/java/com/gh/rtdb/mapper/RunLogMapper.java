package com.gh.rtdb.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gh.common.model.entity.RunLog;
import org.apache.ibatis.annotations.Mapper;

import java.util.Date;
import java.util.List;
@Mapper
public interface RunLogMapper extends BaseMapper<RunLog> {

    List<RunLog> getRunLogRecord(Page<RunLog> page, Integer userId, Integer type, Integer projectId, Integer menuId,
                                 Integer tag,
                                 Date bt,
                                 Date et,Integer deviceId);

    List<RunLog> getDeviceStdByVar(String variable,Integer projectId);

    List<RunLog> getRunLog(Page<RunLog> page, Integer projectId, Integer menuId, Date bt, Date et);
    List<RunLog> getAllRunLog(Page<RunLog> page, Integer projectId, String keyword, Date bt, Date et,Integer menuId,Integer deviceId);
    
    List<RunLog> getAllRunLogOptimized(Integer limit ,Integer offset, Integer projectId, String keyword, Date bt, Date et,Integer menuId,Integer deviceId);

    Long countAllRunLogOptimized(Integer projectId, String keyword, Date bt, Date et,Integer menuId,Integer deviceId);


}
