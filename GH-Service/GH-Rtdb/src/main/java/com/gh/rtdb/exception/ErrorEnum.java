package com.gh.rtdb.exception;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@AllArgsConstructor
@Getter
public enum  ErrorEnum {
    Error_Internal(500,"内部异常"),
    Error_Empty_Socket_Client(400,"客户为空"),
    Error_Kafka_Send(500,"消息发送失败"),
    Error_Template_Repeat(500,"物模板标题重复"),
    Error_Template(500,"物模板保存失败"),
    Error_File_Save(500,"文件保存失败"),
    Error_File_Load(500,"文件下载失败");
    private  Integer code;
    private  String msg;
}
