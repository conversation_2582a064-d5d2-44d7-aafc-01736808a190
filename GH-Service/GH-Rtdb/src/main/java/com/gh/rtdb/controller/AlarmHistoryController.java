package com.gh.rtdb.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.gh.common.utils.GHResponse;
import com.gh.common.utils.PageResult;
import com.gh.rtdb.model.dto.AlarmInfoDto;
import com.gh.rtdb.model.vo.HistoryRecordVo;
import com.gh.rtdb.model.dto.SourceLevel;
import com.gh.rtdb.model.dto.WeekAlarmCount;
import com.gh.rtdb.service.impl.AlarmHistoryServiceImpl;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@RestController
@RequestMapping(value = "/alarm-history")
@Api(tags = "历史数据处理")
public class AlarmHistoryController {

    @Autowired
    private AlarmHistoryServiceImpl alarmHistoryService;


    @ApiOperation("查询历史数据")
    @GetMapping
    public GHResponse<List<HistoryRecordVo>> Select(@RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date bt,
                                                    @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date et,
                                                    @RequestParam(required = false) String keyword,
                                                    @RequestParam(required = false) Boolean status,
                                                    @RequestParam(required = false) Integer deviceId,
                                                    String identifier,Integer productId,
                                                    Integer projectId,Integer alarmSource,
                                                    @RequestParam(required = false) Integer page, @RequestParam(required = false) Integer size) {
        PageResult<List<HistoryRecordVo>> result = alarmHistoryService.GetHistoryRecord(keyword, status, bt, et, deviceId, projectId, page, size,alarmSource,identifier,productId);
        return GHResponse.ok(result.getData(), result.getTotal());
    }

    @ApiOperation("历史数据统计分析")
    @GetMapping("analysis")
    public GHResponse<SourceLevel> analysis(@RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date bt,
                                            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date et,
                                            String tag) {
        SourceLevel sourceLevel = alarmHistoryService.GetSourceAndLevelCount(bt, et, tag);
        return GHResponse.ok(sourceLevel);
    }

    @ApiOperation("一周统计")
    @GetMapping("week")
    public GHResponse<List<WeekAlarmCount>> week(Integer projectId,Integer alarmSource) {
        DateTime bt = DateUtil.beginOfWeek(DateUtil.date());
        DateTime et = DateUtil.endOfWeek(DateUtil.date());

        List<WeekAlarmCount> alarmCounts = alarmHistoryService.GetWeekAlarmCount(bt, et,alarmSource,projectId);
        List<WeekAlarmCount> count = new ArrayList<>();
        for (int i = 0; i <= 6; i++                   ) {
            count.add(WeekAlarmCount.builder().count(0).date(i).build());
        }
        if (CollUtil.isNotEmpty(alarmCounts)) {
            alarmCounts.forEach(weekAlarmCount -> {
                if (weekAlarmCount.getDate() == 0) {
                    count.get(6).setCount(weekAlarmCount.getCount());
                } else {
                    count.get(weekAlarmCount.getDate() - 1).setCount(weekAlarmCount.getCount());
                }
            });
        }

        return GHResponse.ok(count);
    }

    @ApiOperation("查询历史信息关联的设备指标")
    @GetMapping("info")
    public GHResponse<AlarmInfoDto> SelectAlarmInfo(String uuid) {
        List<AlarmInfoDto> alarmById = alarmHistoryService.getAlarmById(uuid);
        if (CollUtil.isNotEmpty(alarmById)) {
            return GHResponse.ok(alarmById.get(0), 1);
        } else {
            return GHResponse.ok(null);
        }
    }

}
