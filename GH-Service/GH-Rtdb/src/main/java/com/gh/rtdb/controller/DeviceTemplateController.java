package com.gh.rtdb.controller;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gh.common.exception.SystemEnumMsg;
import com.gh.common.utils.GHResponse;
import com.gh.common.utils.PageResult;
import com.gh.log.annotation.OperLog;
import com.gh.rtdb.exception.CommonException;
import com.gh.rtdb.exception.ErrorEnum;
import com.gh.rtdb.model.dto.DevTemplateDTO;
import com.gh.rtdb.model.entity.DeviceTemplate;
import com.gh.rtdb.model.vo.DevTemplateVO;
import com.gh.rtdb.service.DeviceTemplateService;
import com.gh.rtdb.service.FileService;
import com.gh.rtdb.utils.ServletUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.List;
@Slf4j
@RequestMapping("/template")
@RestController
@Api(tags = "物模板管理")
public class DeviceTemplateController {

     @Autowired
     private DeviceTemplateService deviceTemplateService;
     @Autowired
     private FileService fileService;
     //将模板文件导出到Redis
     @GetMapping("import/{id}/{iosvrKey}")
     @ApiOperation("导入模板文件到REDIS中的采集器")
//     @OperLog(category = "物模板管理",description = "导入模板文件到REDIS中的采集器")
     public GHResponse importTemplate(@PathVariable("id") Integer id,@PathVariable("iosvrKey") String iosvrKey )
     {
       if(deviceTemplateService.importTemplate(id,iosvrKey))
           return GHResponse.ok();
         return GHResponse.failed();
     }
     //将指定的IOServer自Redis中导出到文件
    @PostMapping("export")
    @ApiOperation("将REDIS中的采集器导出为模板文件")
//    @OperLog(category = "物模板管理",description = "将REDIS中的采集器导出为模板文件")
    public GHResponse  exportTemplate( @RequestBody DevTemplateDTO devTemplateDTO)
    {
        if(deviceTemplateService.exportTemplate(devTemplateDTO))
            return GHResponse.ok();
        return GHResponse.failed();
    }
    @GetMapping("selects")
    @ApiOperation("查询模板")
    public GHResponse<List<DevTemplateVO>> selects(Integer projectId, String title, Integer page, Integer size)
    {

        LambdaQueryChainWrapper<DeviceTemplate> query = deviceTemplateService.lambdaQuery()
                .eq(null!=projectId,DeviceTemplate::getProjectId,projectId)
                .like(StrUtil.isNotEmpty(title),DeviceTemplate::getTitle,title);

        if(PageResult.isPage(page,size))
        {
            Page<DeviceTemplate> page1 = query.page(new Page<>(page,size));

            return GHResponse.ok( new DevTemplateVO(page1.getRecords()).list(),page1.getTotal());
        }
        List<DevTemplateVO> list =new  DevTemplateVO(query.list()).list();
        return GHResponse.ok(list,list.size());
    }
    @DeleteMapping("delete")
    @ApiOperation("删除模板")
//@OperLog(category = "物模板管理",description = "删除模板")
    public GHResponse delete(@RequestParam("ids") String[] ids)
    {
        if(CollUtil.isNotEmpty(Arrays.asList(ids)))
        {
            boolean delete = deviceTemplateService.removeTemplate(ids);

            if(delete)
            {
                return GHResponse.ok();
            }
        }
        return GHResponse.failed(SystemEnumMsg.Delete_ERROR.msg());
    }
    @PutMapping("rename")
    @ApiOperation("重命名模板")
     public  GHResponse rename(
            @RequestParam("id") Integer id,
            @RequestParam("name") String name) {
       if(deviceTemplateService.renameTemplate(id, name))
       {
           return GHResponse.ok();
       }else {
           return   GHResponse.failed(SystemEnumMsg.Update_ERROR.msg());
       }
    }

    @GetMapping("download/{id}")
    @ApiOperation("下载模板文件")
    public void download(@PathVariable("id") Integer id, HttpServletResponse response) {
        try {
            DeviceTemplate template= deviceTemplateService.getById(id);
            String fileName = template.getFileName();
            byte[] file = fileService.getFile(fileName, FileService.TEMPLATE_FOLDER);
            ServletUtils.respondFileInfo(fileName, file.length, response);
            ServletUtils.respondBytes(file, response);
        } catch (Exception e) {
             throw new CommonException(ErrorEnum.Error_File_Load);
        }
    }
    private int getPageNum(int start, int length) {
        int pageNum = (start / length) + 1;
        return pageNum;
    }
}
