package com.gh.rtdb.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.gh.common.redis.RedisHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Component
public class DeviceManagement {
    @Autowired
    private RedisHelper redisHelper;

    public Map<String, String> IOServerGetPropertyAll_V2(String key) {
        return redisHelper.HashGetObject(11, key);
    }

    public List<JSONObject> IOServerGetValues(List<String> ioServerIDs) {
        List<String> ks = ioServerIDs.stream().map(t -> StrUtil.format("iosvr:{}", t)).collect(Collectors.toList());
        List<JSONObject> lis = new ArrayList<>();
        ks.forEach(k -> {
            Map<String, String> dic =IOServerGetPropertyAll_V2(k) ;
            if (dic.keySet().contains("ID") && dic.keySet().contains("Name")) {
                JSONObject obj = JSONUtil.createObj();

                obj.putAll(dic);
                lis.add(obj);
            }

        });
        return lis;
    }

    public List<String> ChlGetAll(String ioServerID) {
        Set<String> members = redisHelper.SetMembers(4, StrUtil.format("RTDBSET:{}", ioServerID));
        return members.stream().map(t -> StrUtil.isNotBlank(t) ? t.split(":")[2] : "")
                .collect(Collectors.toList());
    }

    public Map<String, String> ChlGetPropertyAll_V2(String ioServerID, String chrlID) {
        String key = StrUtil.format("chl:{}:{}", ioServerID, chrlID);
        return redisHelper.HashGetObject(12, key);
    }

    public List<JSONObject> ChlGetValues(String ioServerID, List<String> keys) {
        List<JSONObject> data = new ArrayList<>();
        keys.forEach(k -> {
            Map<String, String> dic = ChlGetPropertyAll_V2(ioServerID, k);
            dic.put("IOServerID",ioServerID);
            JSONObject object = new JSONObject();
            object.putAll(dic);
            data.add(object);
        });
        return data;
    }

    public List<String> CtrlGetAll(String ioServerID, String chlName) {
        // "ctrl:{0}:{1}:{2}"; //0 ioServerID, 1 通道名称, 2 控制器名称
        return redisHelper.SetMembers(4, StrUtil.format("RTDBSET:{}:{}", ioServerID, chlName))
                .stream().map(t -> StrUtil.isNotBlank(t) ? t.split(":")[3] : "")
                .collect(Collectors.toList());
    }

    public Map<String, String> CtrlGetPropertyAll_V2(String ioServerID, String chlID, String ctrlID) {
        String key = StrUtil.format("ctrl:{}:{}:{}", ioServerID, chlID, ctrlID);
        return redisHelper.HashGetObject(13, key);
    }

    public List<JSONObject> CtrlGetValues(String ioServerID, String chlID, List<String> keys) {
        List<JSONObject> data = new ArrayList<>();
        keys.forEach(k -> {
            Map<String, String> dic = CtrlGetPropertyAll_V2(ioServerID, chlID, k);
            dic.put("IOServerID",ioServerID);
            dic.put("ChlID",chlID);
            JSONObject object = new JSONObject();
            object.putAll(dic);
            data.add(object);
        });
        return data;
    }

    public List<Object> VarGetPropertys_V2(String ioServerID, String chlID, String ctrlID, String varID, List<Object> fields) {
        String key = StrUtil.format("var:{}:{}:{}:{}", ioServerID, chlID, ctrlID, varID);
        return redisHelper.HashGetFields(14, key, fields);
    }

    public String VarGetPropertys_V2(String ioServerID, String chlID, String ctrlID, String varID, String field) {
        String key = StrUtil.format("var:{}:{}:{}:{}", ioServerID, chlID, ctrlID, varID);
        return  redisHelper.HashGetFields(14, key, field);
    }

    public List<String> VarGetAll(String ioServerID, String chlName, String ctrlName) {
        // "var:{0}:{1}:{2}:{3}"; //0 ioServerID, 1 通道名称, 2 控制器名称, 3 变量名称
        return redisHelper.SetMembers(4, StrUtil.format("RTDBSET:{}:{}:{}", ioServerID, chlName, ctrlName))
                .stream()
                .map(t -> StrUtil.isNotBlank(t) ? t.split(":")[4] : "")
                .collect(Collectors.toList());
    }

    public Map<String, String> VarGetPropertyAll_V2(String ioServerID, String chlID, String ctrlID, String varID) {
        String key = StrUtil.format("var:{}:{}:{}:{}", ioServerID, chlID, ctrlID, varID);
        return redisHelper.HashGetObject(14, key);
    }
}
