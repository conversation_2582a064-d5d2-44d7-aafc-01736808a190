package com.gh.rtdb.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.gh.rtdb.model.dto.VariableExcelDto;
import com.gh.rtdb.model.dto.ServerDto;
import com.gh.rtdb.model.entity.GateServer;

import java.util.List;

public interface GateServerService extends IService<GateServer> {
     List<ServerDto> getServerTree(Integer project);
     boolean sync(Integer projectId,String serverKey);
     boolean sync(VariableExcelDto excelDto);
}
