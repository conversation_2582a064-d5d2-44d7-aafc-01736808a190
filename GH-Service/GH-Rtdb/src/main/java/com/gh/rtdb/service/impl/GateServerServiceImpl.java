package com.gh.rtdb.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gh.common.redis.RedisHelper;
import com.gh.rtdb.mapper.GateServerMapper;
import com.gh.rtdb.model.dto.VariableExcelDto;
import com.gh.rtdb.model.dto.ServerDto;
import com.gh.rtdb.model.entity.GateChannel;
import com.gh.rtdb.model.entity.GateControl;
import com.gh.rtdb.model.entity.GateServer;
import com.gh.rtdb.model.entity.GateVariable;
import com.gh.rtdb.service.GateChannelService;
import com.gh.rtdb.service.GateControllerService;
import com.gh.rtdb.service.GateServerService;
import com.gh.rtdb.service.GateVariableService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class GateServerServiceImpl extends ServiceImpl<GateServerMapper, GateServer> implements GateServerService {

    @Autowired
    private GateServerMapper mapper;
    @Autowired
    private RedisHelper redisHelper;

    @Autowired
    private GateVariableService variableService;

    @Autowired
    private GateChannelService channelService;
    @Autowired
    private GateControllerService controllerService;


    @Override
    public List<ServerDto> getServerTree(Integer project) {
        return mapper.getServer(project);
    }

    @Override
    public boolean sync(Integer projectId, String serverKey) {
        syncServer(projectId,serverKey);
        syncChannel(projectId,serverKey);
        syncController(projectId,serverKey);
        syncVariable(projectId,serverKey);
        return true;
    }

    @Override
    public boolean sync(VariableExcelDto excelDto) {
        GateServer gateServer = GateServer.builder().keyValue(excelDto.getServerKey()).name(excelDto.getServerName())
                .projectId(excelDto.getProjectId()).build();
        GateChannel gateChannel = GateChannel.builder().keyValue(excelDto.getChannelKey()).name(excelDto.getChannelName())
                .serverKey(excelDto.getServerKey())
                .projectId(excelDto.getProjectId()).protocol(excelDto.getProtocol()).build();

        GateControl gateControl = GateControl.builder().keyValue(excelDto.getControllerKey()).name(excelDto.getControllerName())
                .serverKey(excelDto.getServerKey())
                .channelKey(excelDto.getChannelKey())
                .projectId(excelDto.getProjectId()).protocol(excelDto.getProtocol()).build();

        GateVariable variable = GateVariable.builder().keyValue(excelDto.getVariableKey()).name(excelDto.getVariableName())
                .serverKey(excelDto.getServerKey())
                .channelKey(excelDto.getChannelKey())
                .controllerKey(excelDto.getControllerKey())
                .projectId(excelDto.getProjectId()).build();
        saveOrUpdate(gateServer, Wrappers.<GateServer>lambdaQuery().eq(GateServer::getKeyValue,gateServer.getKeyValue()));
        channelService.saveOrUpdate(gateChannel,Wrappers.<GateChannel>lambdaQuery().eq(GateChannel::getServerKey,gateChannel.getServerKey())
                .eq(GateChannel::getKeyValue,gateChannel.getKeyValue()));
        controllerService.saveOrUpdate(gateControl,Wrappers.<GateControl>lambdaQuery().eq(GateControl::getServerKey,gateControl.getServerKey())
                .eq(GateControl::getChannelKey,gateControl.getChannelKey()).eq(GateControl::getKeyValue,gateControl.getKeyValue()));
        variableService.saveOrUpdate(variable,Wrappers.<GateVariable>lambdaQuery().eq(GateVariable::getServerKey,variable.getServerKey())
                .eq(GateVariable::getChannelKey,variable.getChannelKey()).eq(GateVariable::getControllerKey,variable.getControllerKey())
        .eq(GateVariable::getKeyValue,variable.getKeyValue()));
        return true;
    }

    private void syncServer(Integer projectId,String serverKey)
    {
        List<GateServer> list = lambdaQuery().eq(GateServer::getProjectId, projectId).eq(GateServer::getKeyValue, serverKey).list();
        if(CollUtil.isNotEmpty(list))
        {
            list.forEach(gateServer -> {
                setServer(gateServer.getKeyValue(),gateServer.getName());
            });

        }
    }
    private void syncChannel(Integer projectId,String serverKey)
    {
        List<GateChannel> list =channelService.lambdaQuery().eq(GateChannel::getProjectId, projectId).eq(GateChannel::getServerKey, serverKey).list();
        if(CollUtil.isNotEmpty(list))
        {
            list.forEach(gateChannel -> {
               setChannelService(gateChannel.getServerKey(),gateChannel.getKeyValue(),gateChannel.getName());
            });

        }
    }
    private void syncController(Integer projectId,String serverKey)
    {
        List<GateControl> list = controllerService.lambdaQuery().eq(GateControl::getProjectId, projectId).eq(GateControl::getServerKey, serverKey).list();
        if(CollUtil.isNotEmpty(list))
        {
            list.forEach(gateControl -> {
               setControllerService(gateControl.getServerKey(),gateControl.getChannelKey(),gateControl.getKeyValue(),gateControl.getName());
            });

        }
    }
    private void syncVariable(Integer projectId,String serverKey)
    {
        List<GateVariable> list = variableService.lambdaQuery().eq(GateVariable::getProjectId, projectId).eq(GateVariable::getServerKey, serverKey).list();
        if(CollUtil.isNotEmpty(list))
        {
            list.forEach(gateVariable -> {
               setVariableService(gateVariable.getServerKey(),gateVariable.getChannelKey(),gateVariable.getControllerKey(),
                       gateVariable.getKeyValue(),gateVariable.getName());
            });

        }
    }

    private void setServer(String key,String name)
    {
        redisHelper.SetAdd(4, "RTDBSET", "iosvr:" + key);
        JSONObject jsonObject=new JSONObject();
        jsonObject.set("ID",key);
        jsonObject.set("Name",name);
        redisHelper.setHash(11, "iosvr:" + key,jsonObject );
    }
    private void setChannelService(String serverKey,String channelKey,String name )
    {
        String key=  serverKey+":"+
                channelKey;
        redisHelper.SetAdd(4,
                StrUtil.format("RTDBSET:{}", serverKey), "chl:" + key);
        JSONObject jsonObject=new JSONObject();
        jsonObject.set("ID",channelKey);
        jsonObject.set("Name",name);
        redisHelper.setHash(12, "chl:" + key, jsonObject);
    }
    private void setControllerService(String serverKey,String channelKey,String controllerKey,String name)
    {
        String key=  serverKey+":"+
                channelKey+":"+
                controllerKey;
        redisHelper.SetAdd(4,
                StrUtil.format("RTDBSET:{}:{}", serverKey,channelKey)
                , "ctrl:" +key);
        JSONObject jsonObject=new JSONObject();
        jsonObject.set("ID",controllerKey);
        jsonObject.set("Name",name);
        redisHelper.setHash(13, "ctrl:" + key, jsonObject);
    }
    private void setVariableService(String serverKey,String channelKey,String controllerKey,String variableKey,String name)
    {
        String key=  serverKey+":"+
                channelKey+":"+
                controllerKey+":"+
                variableKey;
        redisHelper.SetAdd(4, StrUtil.format("RTDBSET:{}:{}:{}",
                serverKey, channelKey, controllerKey), "var:" + key);
        JSONObject jsonObject=new JSONObject();
        jsonObject.set("ID",variableKey);
        jsonObject.set("Name",name);
        jsonObject.set("Value","0");
        redisHelper.setHash(14, "var:" + key,jsonObject);
    }
}
