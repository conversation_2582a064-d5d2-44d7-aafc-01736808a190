package com.gh.rtdb.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gh.common.exception.ExceptionEnum;
import com.gh.common.exception.GhCustomException;
import com.gh.common.exception.SystemEnumMsg;
import com.gh.common.redis.RedisHelper;
import com.gh.common.utils.GHResponse;
import com.gh.common.utils.PageResult;
import com.gh.log.annotation.OperLog;
import com.gh.rtdb.model.entity.GateControl;
import com.gh.rtdb.service.GateControllerService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Arrays;
import java.util.List;


/**
 * 虚拟控制器
 */
@RequestMapping("/controller")
@RestController
@Api(tags = "虚拟控制器")
public class DeviceController {

    private final GateControllerService service;
    private final RedisHelper redisHelper;

    @Autowired
    public DeviceController(GateControllerService service,RedisHelper redisHelper) {
        this.service = service;
        this.redisHelper=redisHelper;
    }

    @PostMapping
    @ApiOperation("添加虚拟控制器")
//    @OperLog(category = "虚拟控制器管理",description = "添加虚拟控制器")
    public GHResponse Add(@RequestBody @Valid GateControl gateControl) {

        List<GateControl> list = service.lambdaQuery()
                .and(w->w.eq(GateControl::getKeyValue,gateControl.getKeyValue()).or().eq(GateControl::getName,gateControl.getName()))
                .eq(GateControl::getServerKey,gateControl.getServerKey())
                .eq(GateControl::getChannelKey,gateControl.getChannelKey())
                .eq(GateControl::getProjectId,gateControl.getProjectId()).list();

        if(CollUtil.isNotEmpty(list))
        {
            throw new GhCustomException(ExceptionEnum.PROJECT_REPEATE_ADD);
        }

        boolean insert = service.save(gateControl);
        if (insert) {
            String key=  gateControl.getServerKey()+":"+
                    gateControl.getChannelKey()+":"+
                    gateControl.getKeyValue();
            redisHelper.SetAdd(4,
                    StrUtil.format("RTDBSET:{}:{}", gateControl.getServerKey(), gateControl.getChannelKey())
                    , "ctrl:" + key);
            JSONObject jsonObject=new JSONObject();
            jsonObject.set("ID",gateControl.getKeyValue());
            jsonObject.set("Name",gateControl.getName());
            redisHelper.setHash(13, "ctrl:" + key, jsonObject);
            return GHResponse.ok(null, SystemEnumMsg.CREATE_SUCCESS.msg());
        }
        return GHResponse.failed(SystemEnumMsg.CREATE_ERROR.msg());
    }

    @PutMapping
    @ApiOperation("更新虚拟控制器")
//    @OperLog(category = "虚拟控制器管理",description = "更新虚拟控制器")
    public GHResponse Patch(@RequestBody @Valid GateControl gateControl) {

        List<GateControl> list = service.lambdaQuery().eq(GateControl::getKeyValue, gateControl.getKeyValue())
                .eq(GateControl::getServerKey,gateControl.getServerKey())
                .eq(GateControl::getChannelKey,gateControl.getChannelKey())
                .eq(GateControl::getProjectId,gateControl.getProjectId()).list();

        if(CollUtil.isNotEmpty(list)&&!list.get(0).getId().equals(gateControl.getId()))
        {
            throw new GhCustomException(ExceptionEnum.PROJECT_REPEATE_ADD);
        }
        boolean update = service.updateById(gateControl);
        if (update) {
            return GHResponse.ok();
        }
        return GHResponse.failed(SystemEnumMsg.Update_ERROR.msg());
    }

    @DeleteMapping
    @ApiOperation("删除虚拟控制器")
//    @OperLog(category = "虚拟控制器管理",description = "删除虚拟控制器")
    public GHResponse Delete(@RequestParam("ids") String[] ids)
    {
        if(CollUtil.isNotEmpty(Arrays.asList(ids)))
        {
            boolean delete = service.removeByIds(Arrays.asList(ids));
            if(delete)
            {
                return GHResponse.ok();
            }
        }
        return GHResponse.failed(SystemEnumMsg.Delete_ERROR.msg());
    }

    @GetMapping
    @ApiOperation("查询虚拟控制器")
    public GHResponse<List<GateControl>> Selects(Integer projectId, String serverKey, String channelKey, Integer page, Integer size)
    {
        LambdaQueryChainWrapper<GateControl> query = service.lambdaQuery();
        if(null!=projectId)
        {
            query.eq(GateControl::getProjectId,projectId);
        }
        if(StrUtil.isNotEmpty(serverKey))
        {
            query.eq(GateControl::getServerKey,serverKey);
        }
        if(StrUtil.isNotEmpty(channelKey))
        {
            query.eq(GateControl::getChannelKey,channelKey);
        }

        if(PageResult.isPage(page,size))
        {
            Page<GateControl> page1 = query.page(new Page<>(page, size));
            return GHResponse.ok(page1.getRecords(),page1.getTotal());
        }
        List<GateControl> list = query.list();
        return GHResponse.ok(list,list.size());
    }
}
