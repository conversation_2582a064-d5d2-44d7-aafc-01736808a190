package com.gh.rtdb.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gh.common.model.entity.DeviceStandard;
import com.gh.rtdb.mapper.AlarmConfigMapper;
import com.gh.rtdb.mapper.DevcieStandardMapper;
import com.gh.rtdb.model.entity.AlarmConfig;
import com.gh.rtdb.service.DeviceStandardService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class DeviceStandardServiceImpl extends ServiceImpl<DevcieStandardMapper, DeviceStandard> implements DeviceStandardService {
    @Autowired
    private AlarmConfigMapper mapper;

    @Override
    public List<AlarmConfig> getExistCondition(String variable, String conditionName,Integer projectId) {
        return mapper.getExistCondition(variable,conditionName,projectId);
    }
}
