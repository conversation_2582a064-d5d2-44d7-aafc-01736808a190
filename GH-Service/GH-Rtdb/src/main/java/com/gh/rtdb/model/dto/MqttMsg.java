package com.gh.rtdb.model.dto;

import cn.hutool.core.lang.Snowflake;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
//@NoArgsConstructor
@AllArgsConstructor
public class MqttMsg {
    public MqttMsg()
    {
        this.msgId= new Snowflake().nextId();
        this.mgsTime= System.currentTimeMillis();
        this.code="";
        this.data="";
    }
    public long  msgId;
    public long  mgsTime;
    public String code;
    public Object data;

}
