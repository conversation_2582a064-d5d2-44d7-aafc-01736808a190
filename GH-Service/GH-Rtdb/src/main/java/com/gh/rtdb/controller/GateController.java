package com.gh.rtdb.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.gh.common.exception.ExceptionEnum;
import com.gh.common.exception.GhCustomException;
import com.gh.common.exception.SystemEnumMsg;
import com.gh.common.utils.GHResponse;
import com.gh.common.utils.PassUtil;
import com.gh.rtdb.model.entity.GateWay;
import com.gh.rtdb.service.GateService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;


/**
 * 网关接入认证，添加ioserver
 */
@RequestMapping("/gate")
@RestController
@Api(tags = "ioserver设置")
public class GateController {

    private final GateService service;

    @Autowired
    public GateController(GateService service) {
        this.service = service;
    }

    @PostMapping
    @ApiOperation("添加ioserver")
    @PreAuthorize("hasAnyAuthority('sys_admin','sys_project_admin','sys_gate_add')")
    public GHResponse Add(@RequestBody @Valid GateWay gateWay) {
        List<GateWay> list = service.list(Wrappers.<GateWay>lambdaQuery()
//                .eq(GateWay::getProjectId,gateWay.getProjectId())
                .eq(GateWay::getGateId, gateWay.getGateId()));
        if(CollUtil.isNotEmpty(list))
        {
            throw new GhCustomException(ExceptionEnum.PROJECT_REPEATE_ADD);
        }
        byte[] key = PassUtil.GenKey();
        gateWay.setSalt(key);
        gateWay.setPassword(PassUtil.Encrypt(gateWay.getPassword(), key));
        boolean insert = service.save(gateWay);
        if (insert) {
            return GHResponse.ok(null, SystemEnumMsg.CREATE_SUCCESS.msg());
        }
        return GHResponse.failed(SystemEnumMsg.CREATE_ERROR.msg());
    }

    @PutMapping
    @ApiOperation("更新ioserver")
    @PreAuthorize("hasAnyAuthority('sys_admin','sys_project_admin','sys_gate_update')")
    public GHResponse Patch(@RequestBody @Valid GateWay gateWay) {
        List<GateWay> list = service.list(Wrappers.<GateWay>lambdaQuery()
                .ne(GateWay::getId,gateWay.getId())
//                .eq(GateWay::getProjectId,gateWay.getProjectId())
                .eq(GateWay::getGateId, gateWay.getGateId()));
        if(CollUtil.isNotEmpty(list))
        {
            throw new GhCustomException(ExceptionEnum.PROJECT_REPEATE_ADD);
        }
        if(StrUtil.isNotEmpty(gateWay.getPassword()))
        {
            byte[] key = PassUtil.GenKey();
            gateWay.setSalt(key);
            gateWay.setPassword(PassUtil.Encrypt(gateWay.getPassword(), key));
        }
        boolean update = service.updateById(gateWay);
        if (update) {
            return GHResponse.ok();
        }
        return GHResponse.failed(SystemEnumMsg.Update_ERROR.msg());
    }

    @DeleteMapping
    @ApiOperation("删除ioserver")
    @PreAuthorize("hasAnyAuthority('sys_admin','sys_project_admin','sys_gate_del')")
    public GHResponse Delete(@RequestParam("ids") Integer[] ids)
    {
        if(CollUtil.isNotEmpty(Arrays.asList(ids)))
        {
            boolean delete = service.removeByIds(Arrays.asList(ids));
            if(delete)
            {
                return GHResponse.ok();
            }
        }
        return GHResponse.failed(SystemEnumMsg.Delete_ERROR.msg());
    }



    @GetMapping
    @ApiOperation("查询ioserver")
    public GHResponse<List<GateWay>> Selects(Integer projectId)
    {
        List<GateWay> list = service.list(Wrappers.<GateWay>lambdaQuery().eq(GateWay::getProjectId, projectId))
                .stream().map(g->{
                    String pass = PassUtil.Decrypt(g.getPassword(), g.getSalt());
                    GateWay gateWay = GateWay.builder().build();
                    BeanUtil.copyProperties(g,gateWay);
                    gateWay.setPassword(pass);
                    return  gateWay;
                }).collect(Collectors.toList());
        return GHResponse.ok(list);
    }

    @GetMapping("check")
    @ApiOperation("ioserver检查")
    public GHResponse<List<GateWay>> Check(String appId,String appKey)
    {
        List<GateWay> list = service.list(Wrappers.<GateWay>lambdaQuery().eq(GateWay::getGateId, appId)
        .eq(GateWay::getGateKey,appKey));
        if(CollUtil.isNotEmpty(list))
        {
            return GHResponse.ok();
        }
        return GHResponse.failed();
    }
}
