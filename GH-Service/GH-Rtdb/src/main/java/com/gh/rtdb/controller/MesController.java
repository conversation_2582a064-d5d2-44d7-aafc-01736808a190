package com.gh.rtdb.controller;

import cn.hutool.core.convert.Convert;
import cn.hutool.json.JSONObject;
import com.gh.common.model.dto.InviteInfo;
import com.gh.common.redis.RedisHelper;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Set;

@RestController
@RequestMapping(value = "/msg")
@Api(tags = "项目邀请相关  feign调用")
public class MesController {

    @Autowired
    private RedisHelper redisHelper;


    /**
     * 用户邀请
     *
     * @param inviteInfo
     */
    @PostMapping
    public void post(@RequestBody InviteInfo inviteInfo) {
        if (null != inviteInfo.getUserId()) {
            Set<String> set = redisHelper.PopSet(0, "user:" + inviteInfo.getUserId());
            set.forEach(s -> {
//                SocketIOClient client = server.getClient(UUID.fromString(s));
//                if (null != client) {
//                    client.sendEvent("AdviceMes", inviteInfo);
//                }
            });
        }
    }

    /**
     * 发送项目删除消息   管理员移除项目成员信息
     *
     * @param jsonObject
     */
    @PostMapping("all")
    public void send(@RequestBody JSONObject jsonObject) {
        if (null != jsonObject && null != jsonObject.get("userId")) {
            Integer userId = Convert.toInt(jsonObject.get("userId"), 0);
            Set<String> set = redisHelper.PopSet(0, "user:" + userId);
            set.forEach(s -> {
//                SocketIOClient client = server.getClient(UUID.fromString(s));
//                if(null!=client)
//                {
//
//                    client.sendEvent("AdviceMes", jsonObject);
//                }
            });
        }
    }
}
