package com.gh.rtdb.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gh.common.exception.ExceptionEnum;
import com.gh.common.exception.GhCustomException;
import com.gh.common.exception.SystemEnumMsg;
import com.gh.common.redis.RedisHelper;
import com.gh.common.utils.GHResponse;
import com.gh.common.utils.PageResult;
import com.gh.log.annotation.OperLog;
import com.gh.rtdb.model.entity.GateChannel;
import com.gh.rtdb.service.GateChannelService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Arrays;
import java.util.List;


/**
 * 虚拟通道
 */
@RequestMapping("/channel")
@RestController
@Api(tags = "虚拟通道")
public class ChannelController {

    private final GateChannelService service;
    private final RedisHelper redisHelper;

    @Autowired
    public ChannelController(GateChannelService service,RedisHelper redisHelper) {
        this.service = service;
        this.redisHelper=redisHelper;
    }

    @PostMapping
    @ApiOperation("添加通道")
//    @OperLog(category = "通道管理",description = "添加通道")
    public GHResponse Add(@RequestBody @Valid GateChannel gateChannel) {

        List<GateChannel> list = service.lambdaQuery()
                .and(w->w.eq(GateChannel::getKeyValue,gateChannel.getKeyValue()).or().eq(GateChannel::getName,gateChannel.getName()))
                .eq(GateChannel::getServerKey,gateChannel.getServerKey())
                .eq(GateChannel::getProjectId,gateChannel.getProjectId()).list();

        if(CollUtil.isNotEmpty(list))
        {
            throw new GhCustomException(ExceptionEnum.PROJECT_REPEATE_ADD);
        }

        boolean insert = service.save(gateChannel);
        if (insert) {
            String key=  gateChannel.getServerKey()+":"+
                    gateChannel.getKeyValue();
            redisHelper.SetAdd(4,
                    StrUtil.format("RTDBSET:{}", gateChannel.getServerKey()), "chl:" + key);
            JSONObject jsonObject=new JSONObject();
            jsonObject.set("ID",gateChannel.getKeyValue());
            jsonObject.set("Name",gateChannel.getName());
            redisHelper.setHash(12, "chl:" + key, jsonObject);

            return GHResponse.ok(null, SystemEnumMsg.CREATE_SUCCESS.msg());
        }
        return GHResponse.failed(SystemEnumMsg.CREATE_ERROR.msg());
    }

    @PutMapping
    @ApiOperation("更新通道")
//    @OperLog(category = "通道管理",description = "更新通道")
    public GHResponse Patch(@RequestBody @Valid GateChannel gateChannel) {

        List<GateChannel> list = service.lambdaQuery()
                .and(w->w.eq(GateChannel::getKeyValue,gateChannel.getKeyValue()).or().eq(GateChannel::getName,gateChannel.getName()))
                .eq(GateChannel::getServerKey,gateChannel.getServerKey())
                .eq(GateChannel::getProjectId, gateChannel.getProjectId()).list();
        if(CollUtil.isNotEmpty(list)&&!list.get(0).getId().equals(gateChannel.getId()))
        {
            throw new GhCustomException(ExceptionEnum.PROJECT_REPEATE_ADD);
        }
        boolean update = service.updateById(gateChannel);
        if (update) {
            return GHResponse.ok();
        }
        return GHResponse.failed(SystemEnumMsg.Update_ERROR.msg());
    }

    @DeleteMapping
    @ApiOperation("删除通道")
//    @OperLog(category = "通道管理",description = "删除通道")
    public GHResponse Delete(@RequestParam("ids") String[] ids)
    {
        if(CollUtil.isNotEmpty(Arrays.asList(ids)))
        {
            boolean delete = service.removeByIds(Arrays.asList(ids));
            if(delete)
            {
                return GHResponse.ok();
            }
        }
        return GHResponse.failed(SystemEnumMsg.Delete_ERROR.msg());
    }

    @GetMapping
    @ApiOperation("查询通道")
    public GHResponse<List<GateChannel>> Selects(Integer projectId,String serverKey, Integer page, Integer size)
    {
        LambdaQueryChainWrapper<GateChannel> query = service.lambdaQuery();
        if(null!=projectId)
        {
            query.eq(GateChannel::getProjectId,projectId);
        }
        if(StrUtil.isNotEmpty(serverKey))
        {
            query.eq(GateChannel::getServerKey,serverKey);
        }

        if(PageResult.isPage(page,size))
        {
            Page<GateChannel> page1 = query.page(new Page<>(page, size));
            return GHResponse.ok(page1.getRecords(),page1.getTotal());
        }
        List<GateChannel> list = query.list();
        return GHResponse.ok(list,list.size());
    }



}
