<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gh.rtdb.mapper.RuleConfigMapper">

    <resultMap id="config" type="com.gh.rtdb.model.entity.RuleConfig">
        <id column="id" property="id"></id>
        <result property="productId" column="productId"></result>
        <result property="enableScene" column="enableScene"></result>
        <result property="enableProcess" column="enableProcess"></result>
        <result property="flowKey" column="flowKey"></result>
        <result property="sceneId" column="sceneId"></result>
        <result property="standardId" column="standardId"></result>
        <result property="ruleId" column="ruleId"></result>
        <result property="deviceId" column="deviceId"></result>
        <collection property="ruleCondition" column="id" ofType="com.gh.rtdb.model.entity.RuleCondition">
            <id column="cid" property="id"></id>
            <result property="conditionValue" column="conditionValue"></result>
            <result property="expression" column="expression"></result>
            <result property="operator" column="operator"></result>
            <result property="ruleConfigId" column="ruleConfigId"></result>
        </collection>
    </resultMap>

    <select id="getRuleConfig"  resultMap="config">
        SELECT r.productId ,r.enableScene,r.enableProcess,r.flowKey,r.sceneId,r.deviceId,
        r.id,r.standardId,r.ruleId,c.conditionValue,c.expression,c.id as cid,c.operator,c.ruleConfigId

        FROM ghrule_ruleconfig r
        LEFT JOIN ghrule_rulecondition c on r.id=c.ruleConfigId
        left join ghrule_rule on r.ruleId=ghrule_rule.id
        <where>
            ghrule_rule.enable=1
            <if test="ruleId!=null">
                and r.ruleId=#{ruleId}
            </if>

        </where>

    </select>

<!--    <select id="getRuleVariableConfig" resultType="com.gh.rtdb.model.entity.AlarmConfig">-->
<!--        SELECT c.*,ds.variable FROM ghalarm_config c LEFT JOIN ghdm_devicestandard ds on c.standardId=ds.id-->
<!--        WHERE c.serverType=1 and (c.isAsync=0 or c.isAsync is null)-->
<!--        <if test="projectId!=null">-->
<!--            and c.projectId=#{projectId}-->
<!--        </if>-->
<!--    </select>-->
    <select id="getRuleVariableConfig" resultType="com.gh.rtdb.model.entity.AlarmConfig">
        SELECT c.*,ds.variable FROM ghalarm_config c LEFT JOIN ghdm_devicestandard ds on c.standardId=ds.id
        WHERE c.serverType=1
        <if test="projectId!=null">
            and c.projectId=#{projectId}
        </if>
    </select>

</mapper>
