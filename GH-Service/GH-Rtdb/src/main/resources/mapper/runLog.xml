<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gh.rtdb.mapper.RunLogMapper">
    <select id="getRunLogRecord"  resultType="com.gh.common.model.entity.RunLog">
        SELECT ghops_runlog.* ,ghauth_user.`name` as username from ghops_runlog LEFT JOIN ghauth_user ON
        userId=ghauth_user.id
        <where>
            <if test="userId!=null">
                and ghops_runlog.userId=#{userId}
            </if>
            <if test="bt!=null">
                and  logTime <![CDATA[>=]]> #{bt}
            </if>
            <if test="et!=null">
                and  logTime <![CDATA[<=]]> #{et}
            </if>
            <if test="type!=null">
                and ghops_runlog.type= #{type}
            </if>
            <if test="projectId!=null">
                and ghops_runlog.projectId= #{projectId}
            </if>
            <if test="menuId!=null">
                and ghops_runlog.menuId= #{menuId}
            </if>
            <if test="tag!=null">
                and ghops_runlog.tag= #{tag}
            </if>
            <if test="deviceId!=null">
                and ghops_runlog.deviceId= #{deviceId}
            </if>
        </where>
        order  by logTime desc
    </select>

    <select id="getDeviceStdByVar" resultType="com.gh.common.model.entity.RunLog">
        SELECT d.`name` as deviceName,s.`name` as standardName,ds.deviceId FROM ghdm_devicestandard ds
        LEFT JOIN ghdm_standard s on ds.standardId=s.id
        LEFT JOIN ghdm_device d on d.id=ds.deviceId
        <where>
            <if test="variable !='' and variable !=null">
                and ds.variable=#{variable}
            </if>
            <if test="projectId!=null">
                and ds.projectId= #{projectId}
            </if>
        </where>
    </select>



    <!--    平顶山手动控制记录-->
    <select id="getRunLog"  resultType="com.gh.common.model.entity.RunLog">
        SELECT r.logTime,r.newValue,d.`name` as deviceName,s.`name` as standardName ,u.`name` as username from ghops_runlog r LEFT JOIN ghauth_user u ON
        userId=u.id
        LEFT JOIN ghdm_devicestandard ds on r.variable=ds.variable
        LEFT JOIN ghdm_standard s on s.id=ds.standardId
        LEFT JOIN ghdm_device d on ds.deviceId=d.id
        LEFT JOIN ghops_menuproduct mt on mt.productId=d.productId
        <where>
            <if test="bt!=null">
                and  logTime <![CDATA[>=]]> #{bt}
            </if>
            <if test="et!=null">
                and  logTime <![CDATA[<=]]> #{et}
            </if>
            <if test="projectId!=null">
                and r.projectId= #{projectId}
            </if>
            <if test="menuId!=null">
                and mt.menuId= #{menuId}
            </if>
        </where>
        order  by logTime desc
    </select>


    <select id="getAllRunLog"  resultType="com.gh.common.model.entity.RunLog">
        SELECT rl.*,s.`name` as standardName1,d.`name` as deviceName1,u.`name` as username,m.`name` as menuName 
        FROM (
            SELECT * FROM ghops_runlog 
            <where>
                <if test="bt!=null">
                    and logTime <![CDATA[>=]]> #{bt}
                </if>
                <if test="et!=null">
                    and logTime <![CDATA[<=]]> #{et}
                </if>
                <if test="projectId!=null">
                    and projectId= #{projectId}
                </if>
            </where>
            ORDER BY logTime DESC 
            LIMIT 10
        ) rl
        LEFT JOIN ghdm_device d on rl.deviceId=d.id
        LEFT JOIN ghdm_productstd s on s.identifier=rl.identifier and d.productId=s.productId
        LEFT JOIN ghauth_user u on rl.userId=u.id
        LEFT JOIN ghops_menuproduct mdt on mdt.productId=d.productId
        LEFT JOIN ghops_menu m on m.id=mdt.menuId
        <where>
            <if test="deviceId!=null">
                and d.id= #{deviceId}
            </if>
            <if test="menuId!=null">
                and mdt.menuId= #{menuId}
            </if>
            <if test="keyword !='' and keyword !=null">
                and (rl.deviceName like #{keyword} or d.name like #{keyword})
            </if>
        </where>
    </select>

    <select id="getAllRunLogOptimized"  resultType="com.gh.common.model.entity.RunLog">
        SELECT rl.*,s.`name` as standardName1,d.`name` as deviceName1,u.`name` as username,m.`name` as menuName 
        FROM (
            SELECT r.* FROM ghops_runlog r
            <where>
                <if test="bt!=null">
                    and r.logTime <![CDATA[>=]]> #{bt}
                </if>
                <if test="et!=null">
                    and r.logTime <![CDATA[<=]]> #{et}
                </if>
                <if test="projectId!=null">
                    and r.projectId= #{projectId}
                </if>
                <if test="deviceId!=null">
                    and r.deviceId= #{deviceId}
                </if>
                <if test="menuId!=null">
                    and EXISTS (
                        SELECT 1 FROM ghdm_device d1 
                        JOIN ghops_menuproduct mdt1 ON mdt1.productId=d1.productId
                        WHERE d1.id = r.deviceId AND mdt1.menuId= #{menuId}
                    )
                </if>
            </where>
            ORDER BY r.logTime DESC 
            LIMIT #{limit},#{offset}
        ) rl
        LEFT JOIN ghdm_device d on rl.deviceId=d.id
        LEFT JOIN ghdm_productstd s on s.identifier=rl.identifier and d.productId=s.productId
        LEFT JOIN ghauth_user u on rl.userId=u.id
        LEFT JOIN ghops_menuproduct mdt on mdt.productId=d.productId
        LEFT JOIN ghops_menu m on m.id=mdt.menuId
        <where>
            <if test="keyword !='' and keyword !=null">
                and (rl.deviceName like #{keyword} or d.name like #{keyword})
            </if>
        </where>
        ORDER BY rl.logTime DESC
    </select>

    <select id="countAllRunLogOptimized" resultType="java.lang.Long">
        SELECT COUNT(1) 
        FROM ghops_runlog r
        <where>
            <if test="bt!=null">
                and r.logTime <![CDATA[>=]]> #{bt}
            </if>
            <if test="et!=null">
                and r.logTime <![CDATA[<=]]> #{et}
            </if>
            <if test="projectId!=null">
                and r.projectId= #{projectId}
            </if>
            <if test="deviceId!=null">
                and r.deviceId= #{deviceId}
            </if>
            <if test="menuId!=null">
                and EXISTS (
                    SELECT 1 FROM ghdm_device d 
                    JOIN ghops_menuproduct mdt ON mdt.productId=d.productId
                    WHERE d.id = r.deviceId AND mdt.menuId= #{menuId}
                )
            </if>
            <if test="keyword !='' and keyword !=null">
                and r.deviceName like #{keyword}
            </if>
        </where>
    </select>

    

</mapper>
