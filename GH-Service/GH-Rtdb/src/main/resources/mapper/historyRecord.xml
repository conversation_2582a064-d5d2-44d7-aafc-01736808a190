<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gh.rtdb.mapper.AlarmHistoryMapper">
    <select id="getHistoryRecord"  resultType="com.gh.rtdb.model.vo.HistoryRecordVo">
        SELECT alarmGuid,alarmDesc,alarmValue,alarmLevel,alarmSource,GHAlarm_History.createTime,GHAlarm_History.`status`,alarmVariable,GHDM_Device.`name` as deviceName,
        ghdm_productstd.`name` as standardName,alarmVideo,sceneId,alarmVideoUrl,alarmSnapUrl
        ,GHAlarm_History.updateTime,operateContent,t.`name` as productName
        FROM GHAlarm_History
        LEFT JOIN GHDM_Device ON GHAlarm_History.deviceId=GHDM_Device.id
        LEFT JOIN ghdm_product t on ghdm_device.productId=t.id

        LEFT JOIN ghdm_productstd ON GHAlarm_History.identifier=ghdm_productstd.identifier and ghdm_productstd.productId=t.id
        <where>
            <if test="keyword!=null and keyword!=''">
                 GHDM_Device.`name` like #{keyword}
            </if>
            <if test="status!=null">
                and GHAlarm_History.`status`=#{status}
            </if>
            <if test="bt!=null">
                and  GHAlarm_History.createTime <![CDATA[>=]]> #{bt}
            </if>
            <if test="et!=null">
                and GHAlarm_History.createTime <![CDATA[<=]]> #{et}
            </if>
            <if test="deviceId!=null">
                and GHAlarm_History.deviceId= #{deviceId}
            </if>
            <if test="productId!=null">
                and t.id= #{productId}
            </if>
            <if test="identifier!=null and identifier!=''">
                and GHAlarm_History.identifier= #{identifier}
            </if>
            <if test="projectId!=null">
                and GHAlarm_History.projectId= #{projectId}
            </if>
            <if test="alarmSource!=null">
                and GHAlarm_History.alarmSource= #{alarmSource}
            </if>

        </where>
        order by createTime desc
    </select>

    <select id="getSourceCount"  resultType="com.gh.rtdb.model.dto.SourceCount">
        SELECT sourceId,COUNT(sourceId) as count FROM GHAlarm_History
        <where>
            <if test="bt!=null">
                and createTime <![CDATA[>=]]> #{bt}
            </if>
            <if test="et!=null">
                and createTime <![CDATA[<=]]> #{et}
            </if>

        </where>
        GROUP BY sourceId
    </select>

    <resultMap id="levelCount" type="com.gh.rtdb.model.dto.LevelCount">
        <result column="alarmLevel" property="alarmLevel"></result>
        <collection property="levelTimeCounts" column="alarmLevel" ofType="com.gh.rtdb.model.dto.LevelTimeCount">
            <result column="date" property="date"></result>
            <result column="count" property="count"></result>
        </collection>
    </resultMap>
    <select id="getDayLevelCount" resultMap="levelCount">
        SELECT alarmLevel,COUNT(alarmLevel) as count, DATE_FORMAT(createTime,'%k') as date FROM GHAlarm_History
        <where>
            <if test="bt!=null">
                and createTime <![CDATA[>=]]> #{bt}
            </if>
            <if test="et!=null">
                and createTime <![CDATA[<=]]> #{et}
            </if>
        </where>
        GROUP BY alarmLevel,DATE_FORMAT(createTime,'%k')
    </select>
    <select id="getWeekLevelCount"  resultMap="levelCount">
        SELECT alarmLevel,COUNT(alarmLevel) as count, DATE_FORMAT(createTime,'%w') as date FROM GHAlarm_History
        <where>
            <if test="bt!=null">
                and createTime <![CDATA[>=]]> #{bt}
            </if>
            <if test="et!=null">
                and createTime <![CDATA[<=]]> #{et}
            </if>
        </where>
        GROUP BY alarmLevel,DATE_FORMAT(createTime,'%w')
    </select>
    <select id="getMonthLevelCount"  resultMap="levelCount">
        SELECT alarmLevel,COUNT(alarmLevel) as count, DATE_FORMAT(createTime,'%e') as date FROM GHAlarm_History
        <where>
            <if test="bt!=null">
                and createTime <![CDATA[>=]]> #{bt}
            </if>
            <if test="et!=null">
                and createTime <![CDATA[<=]]> #{et}
            </if>
        </where>
        GROUP BY alarmLevel,DATE_FORMAT(createTime,'%e')
    </select>
    <select id="getYearLevelCount"  resultMap="levelCount">
        SELECT alarmLevel,COUNT(alarmLevel) as count, DATE_FORMAT(createTime,'%c') as date FROM GHAlarm_History
        <where>
            <if test="bt!=null">
                and createTime <![CDATA[>=]]> #{bt}
            </if>
            <if test="et!=null">
                and createTime <![CDATA[<=]]> #{et}
            </if>
        </where>
        GROUP BY alarmLevel,DATE_FORMAT(createTime,'%c')
    </select>

    <select id="getWeekCount"  resultType="com.gh.rtdb.model.dto.WeekAlarmCount">
        SELECT COUNT(*) as count, DATE_FORMAT(createTime,'%w') as date FROM GHAlarm_History
        <where>
            <if test="bt!=null">
                and createTime <![CDATA[>=]]> #{bt}
            </if>
            <if test="et!=null">
                and createTime <![CDATA[<=]]> #{et}
            </if>
            <if test="alarmSource!=null">
                and alarmSource= #{alarmSource}
            </if>
            <if test="projectId!=null">
                and projectId= #{projectId}
            </if>
        </where>
        GROUP BY DATE_FORMAT(createTime,'%w')
    </select>


    <select id="getAlarmById" resultType="com.gh.rtdb.model.dto.AlarmInfoDto">
        SELECT s.`name` as standardName ,d.`name` as deviceName,h.alarmVideoUrl,h.alarmSnapUrl,c.points,h.auto FROM ghalarm_history h
        LEFT JOIN ghdm_device d on h.deviceId=d.id
        LEFT JOIN ghdm_devicestandard ds on ds.deviceId=h.deviceId and h.standardId=ds.id
        LEFT JOIN ghdm_standard s on ds.standardId=s.id
        LEFT JOIN ghgis_config c on c.deviceId=d.id or c.deviceId=CONCAT('cam_',d.id)
    <where>
        <if test="uuid!=null and uuid!=''">
            h.alarmGuid=#{uuid}
        </if>
    </where>
    </select>

</mapper>
