<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gh.rtdb.mapper.GateVariableMapper">

    <select id="getVariableList"  resultType="com.gh.rtdb.model.entity.GateVariable">
        SELECT v.*,c.protocol FROM ghgate_variable v LEFT JOIN ghgate_channel c ON v.channelKey=c.keyValue and v.serverKey=c.serverKey
        where v.serverKey=#{serverKey} and v.channelKey=#{channelKey} and v.controllerKey=#{controllerKey} and v.projectId=#{projectId}
    </select>


</mapper>