<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gh.rtdb.mapper.GateServerMapper">

    <resultMap id="ServerDto" type="com.gh.rtdb.model.dto.ServerDto">
        <id column="id" property="id"></id>
        <result column="keyValue" property="key"></result>
        <result column="name" property="name"></result>
        <collection property="children" column="keyValue" ofType="com.gh.rtdb.model.dto.ChannelDto">
            <id column="cid" property="id"></id>
            <result column="ckey" property="key"></result>
            <result column="cname" property="name"></result>
            <result column="cserverKey" property="serverKey"></result>
            <result column="cprotocol" property="protocol"></result>
            <collection property="children" column="ckey" ofType="com.gh.rtdb.model.dto.ControllerDto">
                <id column="lid" property="id"></id>
                <result column="lkey" property="key"></result>
                <result column="lname" property="name"></result>
                <result column="lserverKey" property="serverKey"></result>
                <result column="lchannelKey" property="channelKey"></result>
                <result column="lprotocol" property="protocol"></result>
                <collection property="children" column="lkey" ofType="com.gh.rtdb.model.dto.VariableDto">
                    <id column="vid" property="id"></id>
                    <result column="vkey" property="key"></result>
                    <result column="vname" property="name"></result>
                    <result column="vserverKey" property="serverKey"></result>
                    <result column="vchannelKey" property="channelKey"></result>
                    <result column="vcontrollerKey" property="controllerKey"></result>
                </collection>
            </collection>
        </collection>
    </resultMap>


    <select id="getServer" resultMap="ServerDto">
        SELECT s.id,s.keyValue ,s.`name` ,c.id as cid ,c.keyValue as ckey,c.name as cname, c.serverKey as cserverKey,c.protocol as cprotocol,l.protocol as lprotocol,
        l.id as lid,l.`name` as lname ,l.keyValue as lkey,l.serverKey as lserverKey,l.channelKey as lchannelKey,
        v.id as vid,v.`name` as vname,v.keyValue as vkey,v.serverKey as vserverKey,v.channelKey as vchannelKey,v.controllerKey as vcontrollerKey
        FROM ghgate_server s LEFT JOIN ghgate_channel c ON s.keyValue=c.serverKey and s.projectId=c.projectId
        LEFT JOIN ghgate_controller l ON l.channelKey=c.keyValue and s.projectId=l.projectId AND l.serverKey=s.keyValue
        LEFT JOIN ghgate_variable v ON v.controllerKey=l.keyValue and s.projectId=v.projectId and v.serverKey=s.keyValue
        <where>
            <if test="projectId!=null">
                and s.projectId=#{projectId}
            </if>
        </where>
    </select>


</mapper>