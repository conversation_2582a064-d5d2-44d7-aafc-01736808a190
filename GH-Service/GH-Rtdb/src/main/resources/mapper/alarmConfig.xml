<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gh.rtdb.mapper.AlarmConfigMapper">
    <!--    String variable, String conditionName, String conditionValue-->
    <select id="getExistCondition" resultType="com.gh.rtdb.model.entity.AlarmConfig">
        SELECT c.* FROM ghdm_devicestandard ds
        LEFT JOIN ghalarm_config c on c.standardId=ds.id
        <where>
             c.conditionName!='' and c.conditionName is not null
            <if test="variable !='' and variable !=null">
                and ds.variable=#{variable}
            </if>
            <if test="conditionName !='' and conditionName !=null">
                and c.conditionName=#{conditionName}
            </if>
            <if test="projectId !=null">
                and c.projectId=#{projectId}
            </if>
        </where>
    </select>




</mapper>