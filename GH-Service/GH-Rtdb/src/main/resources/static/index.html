<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8"/>
    <title>spring-boot-demo-websocket-socketio</title>
    <link href="bootstrap.css" rel="stylesheet">
    <link href="https://cdn.bootcss.com/layer/2.3/skin/layer.css" rel="stylesheet">
    <style>
        body {
            padding: 20px;
        }

        #console {
            height: 400px;
            overflow: auto;
        }

        .username-msg {
            color: orange;
        }

        .connect-msg {
            color: green;
        }

        .disconnect-msg {
            color: red;
        }

        .broadcast {
            color: red;
        }

        .send-msg {
            color: #888
        }

        .sys-msg {
            color: #888
        }

    </style>

    <script src="js/socket.io/socket.io.js"></script>
    <script src="js/moment.min.js"></script>
    <script src="js/jquery-1.10.1.min.js"></script>
    <script src="https://cdn.bootcss.com/axios/0.19.0-beta.1/axios.min.js"></script>
    <script src="https://cdn.bootcss.com/layer/2.3/layer.js"></script>
    <script>
        const token = 'user' + Math.floor((Math.random() * 1000) + 1);
        const url = `http://127.0.0.1:32000`;
        const socket = io.connect(url);
        socket.on('connect', function () {
            output(`<span class="connect-msg">系统通知: ${token}成功连接至websocket服务器</span>`);
            let data= { batchDefinitionId:"11", clientId:"11", requestItems:[{
                   id: "11",
                   iosvrKey: "dong",
                   chlKey: "1",
                   ctrlKey: "1",
                   varKey: "2",
                   realTime: false
               }] }
            socket.emit('Subscribe', JSON.stringify(data));
        });



        socket.on('live', function (data) {
            output(`<span class="disconnect-msg">系统通知:${data} </span>`);
        });


        socket.on('disconnect', function () {
            output(`<span class="disconnect-msg">系统通知: ${token}已从websocket服务器断开连接</span>`);
        });

        socket.on('onVarsChangedCallback', function (data) {
            output(`<span class="broadcast">${data}</span>`);
        });
        function output(message) {
            let currentTime = "<span class='time'>" + moment().format('YYYY-MM-dd HH:mm:ss.SSS') + "</span>";
            let element = $("<div>" + currentTime + " " + message + "</div>");
            $('#console').prepend(element);
        }








    </script>
</head>
<body>
<h1>spring-boot-demo-websocket-socketio</h1>
<br/>
<div id="console" class="well">
</div>
<form class="well form-inline" onsubmit="return false;">
    <input id="msg" class="input-xlarge" type="text" placeholder="随便输点啥"/>
    <input id="to" class="input-xlarge" type="text" placeholder="私聊发给谁"/>
    <button type="button" onClick="sendJoin()" class="btn" id="join">加入群聊</button>
    <button type="button" onClick="sendGroup()" class="btn" id="send">群聊</button>
    <button type="button" onClick="sendChat()" class="btn" id="chat">私聊</button>
    <button type="button" onClick="sendBroadcast()" class="btn">广播消息</button>
    <button type="button" onClick="sendConnect()" class="btn">连接</button>
    <button type="button" onClick="sendDisconnect()" class="btn">断开</button>
</form>
</body>
</html>