server:
  port: 9005
spring:
  application:
    name: rtdb-service
  kafka:
    consumer:
      group-id: rtdb2 #集群要注意
      max-poll-records: 200
      value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
    producer:
      value-serializer: org.apache.kafka.common.serialization.StringSerializer

  cloud:
    nacos:
      server-addr: ${host:*************:28848}
      config:
        file-extension: yaml
        namespace: 9027158d-ab5d-43f7-a691-3b7546cc79f9
        extension-configs:
          - data-id: mysql.yml
          - data-id: redis.yml
          - data-id: json.yml
          - data-id: swagger.yml
          - data-id: kafka.yml
          - data-id: admin.yml
        #  - data-id: zipkin.yml
      discovery:
        server-addr: ${host:*************:28848}
        namespace: 734f1747-1d86-4f76-9cef-91fc7ea262b6



logging:
  level:
    com.gh.rtdb.mapper: debug



mybatis-plus:
  global-config:
    db-config:
      update-strategy: ignored

files:
  store:
    dir: /data



