<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.gh</groupId>
    <artifactId>GH-Rtdb</artifactId>
    <version>1.0</version>
    <packaging>jar</packaging>

    <parent>
        <groupId>com.gh</groupId>
        <artifactId>GH-Service</artifactId>
        <version>1.0</version>
    </parent>

    <properties>
        <orika-core.version>1.5.4</orika-core.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>

        <dependency>
            <groupId>com.corundumstudio.socketio</groupId>
            <artifactId>netty-socketio</artifactId>
            <version>1.7.18</version>
        </dependency>

        <dependency>
            <groupId>com.gh</groupId>
            <artifactId>GH-Maintenance-Api</artifactId>
            <version>1.0</version>
        </dependency>

        <dependency>
            <groupId>com.gh</groupId>
            <artifactId>GH-Scene-Api</artifactId>
            <version>1.0</version>
        </dependency>

        <dependency>
            <groupId>com.gh</groupId>
            <artifactId>GH-Common-Redis</artifactId>
        </dependency>

        <dependency>
            <groupId>com.gh</groupId>
            <artifactId>GH-Common-Model</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.kafka</groupId>
            <artifactId>spring-kafka</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-alibaba-nacos-discovery</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-alibaba-nacos-config</artifactId>
        </dependency>

<!--        <dependency>-->
<!--            <groupId>org.springframework.cloud</groupId>-->
<!--            <artifactId>spring-cloud-starter-zipkin</artifactId>-->
<!--        </dependency>-->

<!--        <dependency>-->
<!--            <groupId>de.codecentric</groupId>-->
<!--            <artifactId>spring-boot-admin-starter-client</artifactId>-->
<!--        </dependency>-->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
        </dependency>
        <dependency>
            <groupId>ma.glasnost.orika</groupId>
            <artifactId>orika-core</artifactId>
            <version>${orika-core.version}</version>
        </dependency>
        <dependency>
            <groupId>com.gh</groupId>
            <artifactId>GH-Log</artifactId>
            <version>1.0</version>
        </dependency>

        <dependency>
            <groupId>com.gh</groupId>
            <artifactId>GH-Common-Filter</artifactId>
        </dependency>


    </dependencies>
    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
<!--            <plugin>-->
<!--                <groupId>com.spotify</groupId>-->
<!--                <artifactId>docker-maven-plugin</artifactId>-->
<!--                <version>1.2.2</version>-->
<!--                <executions>-->
<!--                    <execution>-->
<!--                        <id>build-image</id>-->
<!--                        <phase>package</phase>-->
<!--                        <goals>-->
<!--                            <goal>build</goal>-->
<!--                        </goals>-->
<!--                    </execution>-->
<!--                </executions>-->
<!--                <configuration>-->
<!--                    <serverId>docker</serverId>-->
<!--                    <dockerHost>http://*************:2375</dockerHost>-->
<!--                    <imageName>*************:26667/njgh/rtdb:v1</imageName>-->
<!--                    <imageTags>v1</imageTags>-->
<!--                    <dockerDirectory>src/main/docker</dockerDirectory>-->
<!--                    <resources>-->
<!--                        <resource>-->
<!--                            <targetPath>/</targetPath>-->
<!--                            <directory>${project.build.directory}</directory>-->
<!--                            <include>GH-Rtdb-1.0.jar</include>-->
<!--                        </resource>-->
<!--                    </resources>-->
<!--                    <registryUrl>*************:26667/njgh</registryUrl>-->
<!--                </configuration>-->
<!--            </plugin>-->
        </plugins>
    </build>

</project>
