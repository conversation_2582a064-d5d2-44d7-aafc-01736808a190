package com.ghy.scada.controller;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ghy.scada.dto.CategoryDTO;
import com.ghy.scada.dto.CategoryQueryDTO;
import com.ghy.scada.dto.CategoryTreeDTO;
import com.ghy.scada.dto.DiagramTreeDTO;
import com.ghy.scada.entity.Category;
import com.ghy.scada.entity.DiagramFile;
import com.ghy.scada.infrastructure.exception.ServiceDataException;
import com.ghy.scada.infrastructure.web.response.UnifiedResponseBody;
import com.ghy.scada.service.CategoryService;
import com.ghy.scada.service.DiagramFileService;
import com.ghy.scada.service.ProjectService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 图形目录分类
 */
@RequestMapping("/Home")
@RestController
@UnifiedResponseBody
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Api(tags = "组态图分类管理")
@Slf4j
public class CategoryController {

    private final CategoryService categoryService;

    private final DiagramFileService diagramFileService;

    @ApiOperation("新增或更新分类")
    @PostMapping("/CategoryDetail/{id}")
    public void saveOrUpdate(@Valid @RequestBody Category categoryDTO, @PathVariable("id") Integer id) {
        log.info("=================+"+JSONUtil.toJsonStr(categoryDTO));
        if (id == -1) {
            categoryService.lambdaQuery().eq(Category::getName, categoryDTO.getName()).eq(Category::getProjectId, categoryDTO.getProjectId()).oneOpt().ifPresent(category -> {
                Map<String, String > message =new HashMap<>();
                message.put("AreaName","名称已经存在");
                throw new ServiceDataException(message);
            });
            categoryDTO.setID(null);
            categoryService.save(categoryDTO);
        } else {
            categoryService.lambdaQuery().eq(Category::getName, categoryDTO.getName()).eq(Category::getProjectId, categoryDTO.getProjectId())
                    .ne(Category::getID,id)
                    .oneOpt().ifPresent(category -> {
                Map<String, String > message =new HashMap<>();
                message.put("AreaName","名称已经存在");
                throw new ServiceDataException(message);
            });
            categoryService.updateById(categoryDTO);
        }
    }

    @ApiOperation("查询分类以树返回")
    @PostMapping("/LoadCategory")
    public List<CategoryTreeDTO> getTree(@RequestParam(name = "projectId", defaultValue = ProjectService.DEFAULT_PROJECT_ID) Integer projectId) {
        List<Category> rootCategorys = categoryService.lambdaQuery().eq(Category::getParentID, -1)
                .and(q -> q.eq(Category::getProjectId, projectId).or().eq(Category::getPublicFlag,true)).list();
        return this.getCategoryTreeDTO(rootCategorys);
    }


    @ApiOperation("查询所有分类")
    @PostMapping("/GetAllCategory")
    public List<CategoryQueryDTO> getAll(@RequestParam(name = "projectId", defaultValue = ProjectService.DEFAULT_PROJECT_ID) Integer projectId) {
        List<Category> categories = categoryService.lambdaQuery()
                .and(q -> q.eq(Category::getPublicFlag, true).or().eq(Category::getProjectId, projectId)).list();
        List<CategoryQueryDTO> categoryQueryDTOS = categories.stream().map(category -> {
            CategoryQueryDTO categoryQueryDTO = new CategoryQueryDTO().setId(category.getID()).setPId(category.getParentID()).setName(category.getName());
            return categoryQueryDTO;
        }).collect(Collectors.toList());
        return categoryQueryDTOS;
    }

    @ApiOperation("删除分类")
    @PostMapping("/DeleteCategory")
    public void remove(String data) {
        categoryService.remove(data);
    }

    private List<CategoryTreeDTO> getChildCategory(CategoryDTO rootCategoryDTO) {
        List<Category> childCategorys = categoryService.lambdaQuery().eq(Category::getParentID, rootCategoryDTO.getID()).list();
        return this.getCategoryTreeDTO(childCategorys);
    }

    private List<CategoryTreeDTO> getCategoryTreeDTO(List<Category> categories) {
        List<CategoryDTO> childCategoryDTOS = new CategoryDTO(categories).list();
        List<CategoryTreeDTO> categoryTreeDTOS = childCategoryDTOS.stream().map(categoryDTO -> {
            CategoryTreeDTO categoryTreeDTO = new CategoryTreeDTO().setText(categoryDTO.getName()).setHref("#" + categoryDTO.getID()).setKey(categoryDTO.getID()).setPkey(categoryDTO.getParentID()).setNodes(this.getChildCategory(categoryDTO));
            return categoryTreeDTO;
        }).collect(Collectors.toList());
        return categoryTreeDTOS;
    }


    @ApiOperation("查询分类以树返回")
    @PostMapping("/LoadCategory/tree")
    public List<DiagramTreeDTO> getCTree(@RequestParam(name = "projectId", defaultValue = ProjectService.DEFAULT_PROJECT_ID) Integer projectId) {
        List<Category> rootCategorys = categoryService.lambdaQuery()
                .and(q -> q.eq(Category::getProjectId, projectId).or().eq(Category::getPublicFlag, true))
                .list();
        List<DiagramFile> files = diagramFileService.lambdaQuery().
                and(q -> q.eq(DiagramFile::getProjectId, projectId).or().eq(DiagramFile::getPublicFlag, true))
                        .list();
        return this.getCategoryTree(rootCategorys, -1, files);
    }

    private List<DiagramTreeDTO> getCategoryTree(List<Category> categories, Integer parentId, List<DiagramFile> files) {
        List<DiagramTreeDTO> categoryTreeDTOS = categories.stream().filter(category -> category.getParentID().equals(parentId)).map(categoryDTO -> {
            DiagramTreeDTO categoryTreeDTO = new DiagramTreeDTO();
            List<DiagramTreeDTO> fileList = files.stream().filter(file -> file.getCategoryId() != null && file.getCategoryId().equals(categoryDTO.getID())).map(file -> {
                DiagramTreeDTO fileMap = new DiagramTreeDTO();
                fileMap.setLabel(file.getName()).setValue(file.getId()).setTagFolder(false);
                return fileMap;
            }).collect(Collectors.toList());
            categoryTreeDTO.setLabel(categoryDTO.getName()).setValue(categoryDTO.getID()).setTagFolder(true);
            List<DiagramTreeDTO> children = this.getCategoryTree(categories, categoryDTO.getID(), files);
            children.addAll(fileList);
            categoryTreeDTO.setChildren(children);
            return categoryTreeDTO;
        }).collect(Collectors.toList());
        return categoryTreeDTOS;
    }


}
