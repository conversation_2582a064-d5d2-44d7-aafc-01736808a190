package com.ghy.scada.controller;

import com.ghy.scada.dto.FigureKindTreeDTO;
import com.ghy.scada.dto.ImgKTreeDTO;
import com.ghy.scada.dto.ImgKindDTO;
import com.ghy.scada.entity.ImgKind;
import com.ghy.scada.infrastructure.web.response.UnifiedResponseBody;
import com.ghy.scada.service.ImgKindService;
import com.ghy.scada.service.ProjectService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.stream.Collectors;


/**
 * 图元类型
 */
@RequestMapping("/Home")
@RestController
@UnifiedResponseBody
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Api(tags="图片分类管理")
public class ImgKindController {

    private final ImgKindService imgKindService;
    @ApiOperation("查询所有图片分类")
    @PostMapping("/GetImgKind")
    public List<ImgKindDTO> getAll(@RequestParam(name = "projectId", defaultValue = ProjectService.DEFAULT_PROJECT_ID) Integer projectId) {
       // return new ImgKindDTO(imgKindService.lambdaQuery().eq(projectId!=null,ImgKind::getProjectId, projectId).list()).list();
       //图元分类所有项目共享
        return new ImgKindDTO(imgKindService.list()).list();

    }
    @ApiOperation("查询图片分类树")
    @PostMapping("/GetImgKindTree")
    public List<FigureKindTreeDTO> getTree(@RequestParam(name = "projectId", defaultValue = ProjectService.DEFAULT_PROJECT_ID) Integer projectId) {
        List<ImgKind> rootFigureKinds = imgKindService.lambdaQuery()
                .eq(ImgKind::getParentID, -1)
                //.eq(ImgKind::getProjectId, projectId)
                .list();
        return getFigureKindTreeDTOS(rootFigureKinds);
        // TODO 查询本项目的所有目录以及其他项目可见的部分，以项目结构显示，需前端配合
        //目录所有项目共享，目录下的图元区分项目
    }
    @ApiOperation("新增或更新图片分类")
    @PostMapping("/ImgKindDetail/{id}")
    public void saveOrUpdate(
            @Valid ImgKindDTO figureKindDTO,
            @PathVariable("id") Integer id
    ) {
        if (id == -1) {
            imgKindService.save(figureKindDTO);
        } else {
            imgKindService.update(figureKindDTO);
        }
    }
    @ApiOperation("删除图片分类")
    @PostMapping("/DeleteImgKind")
    public void remove(
            @RequestParam(name = "id", defaultValue = "") Integer id,
            @RequestParam(name = "folder", defaultValue = "") String folder,
            @RequestParam(name = "projectId", defaultValue = ProjectService.DEFAULT_PROJECT_ID) Integer projectId
    ) {
        imgKindService.remove(id, folder, projectId);
    }

    @ApiOperation("查询图片分类树")
    @PostMapping("/img/tree")
    public List<ImgKTreeDTO> getFKTree(@RequestParam(name = "projectId", defaultValue = ProjectService.DEFAULT_PROJECT_ID) Integer projectId) {
        List<ImgKTreeDTO> rootFigureKinds = imgKindService.getFigureKTree(projectId);

        return rootFigureKinds;
    }



    private List<FigureKindTreeDTO> getChildFigureDTOS(ImgKindDTO rootFigureKindDTO) {
        List<ImgKind> childFigureKinds = imgKindService.lambdaQuery()
                .eq(ImgKind::getParentID, rootFigureKindDTO.getID())
                .list();
        return getFigureKindTreeDTOS(childFigureKinds);
    }

    private List<FigureKindTreeDTO> getFigureKindTreeDTOS(List<ImgKind> rootFigureKinds) {
        List<ImgKindDTO> rootFigureKindDTOS = new ImgKindDTO(rootFigureKinds).list();
        List<FigureKindTreeDTO> figureKindTreeDTOS = rootFigureKindDTOS.stream().map(rootFigureKindDTO -> {
            FigureKindTreeDTO figureKindTreeDTO = new FigureKindTreeDTO()
                    .setText(rootFigureKindDTO.getKindName())
                    .setHref("#" + rootFigureKindDTO.getID())
                    .setKey(rootFigureKindDTO.getID())
                    .setPkey(rootFigureKindDTO.getParentID())
                    .setFolder(rootFigureKindDTO.getFolderName())
                    .setNodes(this.getChildFigureDTOS(rootFigureKindDTO));
            return figureKindTreeDTO;
        }).collect(Collectors.toList());
        return figureKindTreeDTOS;
    }




}
