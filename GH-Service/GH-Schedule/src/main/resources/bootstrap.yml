spring:
  application:
    name: schedule-service
  cloud:
    nacos:
      server-addr: 36.152.149.51:28848
      config:
        file-extension: yaml
        namespace: 6cf15815-a3cb-4cba-a940-16026af4257c
        extension-configs:
          - data-id: redis.yml
          - data-id: mysql.yml
          - data-id: json.yml
          - data-id: swagger.yml
      discovery:
        server-addr: 36.152.149.51:28848
        namespace: 6cf15815-a3cb-4cba-a940-16026af4257c
  mvc:
    servlet:
      load-on-startup: 1
server:
  port: 9027

mybatis-plus:
  global-config:
    db-config:
      update-strategy: ignored
