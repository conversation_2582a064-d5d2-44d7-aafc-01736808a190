<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.gh</groupId>
    <artifactId>GH-Video</artifactId>
    <version>1.0</version>
    <packaging>jar</packaging>

    <parent>
        <groupId>com.gh</groupId>
        <artifactId>GH-Service</artifactId>
        <version>1.0</version>
    </parent>

    <dependencies>
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
        </dependency>


        <dependency>
            <groupId>com.gh</groupId>
            <artifactId>GH-Log</artifactId>
            <version>1.0</version>
        </dependency>

        <dependency>
            <groupId>javax.validation</groupId>
            <artifactId>validation-api</artifactId>
        </dependency>


        <dependency>
            <groupId>io.netty</groupId>
            <artifactId>netty-common</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-alibaba-nacos-discovery</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-alibaba-nacos-config</artifactId>
        </dependency>

<!--        <dependency>-->
<!--            <groupId>org.springframework.cloud</groupId>-->
<!--            <artifactId>spring-cloud-starter-zipkin</artifactId>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>de.codecentric</groupId>-->
<!--            <artifactId>spring-boot-admin-starter-client</artifactId>-->
<!--        </dependency>-->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
        </dependency>


        <dependency>
            <groupId>com.gh</groupId>
            <artifactId>GH-Common-Redis</artifactId>
        </dependency>

        <dependency>
            <groupId>com.gh</groupId>
            <artifactId>GH-Resource-Api</artifactId>
            <version>1.0</version>
        </dependency>

        <dependency>
            <groupId>com.gh</groupId>
            <artifactId>GH-Basic-Api</artifactId>
            <version>1.0</version>
        </dependency>

        <dependency>
            <groupId>com.gh</groupId>
            <artifactId>GH-Common-Filter</artifactId>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>com.squareup.okhttp3</groupId>-->
<!--            <artifactId>okhttp</artifactId>-->
<!--        </dependency>-->

        <dependency>
            <groupId>net.sf.kxml</groupId>
            <artifactId>kxml2</artifactId>
            <version>2.3.0</version>
        </dependency>


        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp</artifactId>
            <version>4.9.3</version>
        </dependency>

        <dependency>
            <groupId>com.burgstaller</groupId>
            <artifactId>okhttp-digest</artifactId>
            <version>2.1</version>

        </dependency>


        <dependency>
            <groupId>com.github.03</groupId>
            <artifactId>onvif</artifactId>
            <version>1.0.9</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/src/main/resources/lib/onvif-1.0.9.jar</systemPath>
        </dependency>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>

    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <executable>true</executable>
                    <fork>true</fork>
                    <includeSystemScope>true</includeSystemScope>
                </configuration>
            </plugin>
<!--            <plugin>-->
<!--                <groupId>com.spotify</groupId>-->
<!--                <artifactId>docker-maven-plugin</artifactId>-->
<!--                <version>1.2.2</version>-->
<!--                <executions>-->
<!--                    <execution>-->
<!--                        <id>build-image</id>-->
<!--                        <phase>package</phase>-->
<!--                        <goals>-->
<!--                            <goal>build</goal>-->
<!--                        </goals>-->
<!--                    </execution>-->
<!--                </executions>-->
<!--                <configuration>-->
<!--                    <serverId>docker</serverId>-->
<!--                    <dockerHost>http://*************:2375</dockerHost>-->
<!--                    <imageName>*************:26667/njgh/video:v1</imageName>-->
<!--                    <dockerDirectory>src/main/docker</dockerDirectory>-->
<!--                    <resources>-->
<!--                        <resource>-->
<!--                            <targetPath>/</targetPath>-->
<!--                            <directory>${project.build.directory}</directory>-->
<!--                            <include>GH-Video-1.0.jar</include>-->
<!--                        </resource>-->
<!--                    </resources>-->
<!--                    <registryUrl>*************:26667/njgh</registryUrl>-->
<!--                </configuration>-->
<!--            </plugin>-->
        </plugins>
    </build>

</project>
