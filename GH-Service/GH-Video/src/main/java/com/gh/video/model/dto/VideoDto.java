package com.gh.video.model.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName("camera")
public class VideoDto {
    @ExcelProperty(index =0 )
    private String serverIp;
    @ExcelProperty(index =1 )
    private Integer serverPort;

    @ExcelProperty(index =14 )
    private String camToken;

    @ExcelProperty(index =7 )
    private Integer serverId;
    @ExcelProperty(index =8 )
    private Integer areaId;
    @ExcelProperty(index =2 )
    private String strName;
    @ExcelProperty(index =18 )
    private String strUrl;
    @ExcelProperty(index =19 )
    private String nType;
    @ExcelProperty(index =5 )
    private String strUser;
    @ExcelProperty(index =6 )
    private String strPasswd;
    @ExcelProperty(index =3 )
    private String strSrcIpAddress;
    @ExcelProperty(index =4 )
    private String strSrcPort;
    private String videoType="H5_ONVIF";
    private String lating;
    private Boolean ptz;
    @ExcelProperty(index =9 )
    private Integer menuId;
    @ExcelProperty(index =10 )
    private String areaName;
    @ExcelProperty(index =11 )
    private String menuName;
    @ExcelProperty(index =12 )
    private Integer projectId;
    @ExcelProperty(index =13 )
    private Integer productId;

    @ExcelProperty(index =16 )
    private Integer serverType;

    @ExcelProperty(index =15 )
    private String mediaServerId;


    @ExcelProperty(index =17 )
    private Integer ptzEnable;






}
