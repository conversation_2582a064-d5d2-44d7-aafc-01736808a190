package com.gh.video.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.gh.common.utils.PageResult;
import com.gh.video.model.entity.Server;

import java.util.List;

public interface ServerService extends IService<Server> {
    PageResult<List<Server>> select(Integer id, Integer projectId , String keyword, String ip, Integer page, Integer size);

    boolean UpdateFromRemoteServer(Integer serverId,Integer productId);

}
