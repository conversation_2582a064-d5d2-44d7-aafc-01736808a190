package com.gh.video.model.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * value: zlm 流媒体
 * User: zhangkeguang
 * Date: 2022-11-08-09:01
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName("media_server")
public class ZLMMediaServer {

    @ApiModelProperty(value = "ID")
    @TableId
    private String id;


    private String name;

    @ApiModelProperty(value = "IP")
    private String ip;

    @ApiModelProperty(value = "hook使用的IP（zlm访问WVP使用的IP）")
    private String hookIp;

    @ApiModelProperty(value = "SDP IP")
    private String sdpIp;

    @ApiModelProperty(value = "流IP")
    private String streamIp;

    @ApiModelProperty(value = "HTTP端口")
    private int httpPort;

    @ApiModelProperty(value = "HTTPS端口")
    private int httpSSlPort;

    @ApiModelProperty(value = "RTMP端口")
    private int rtmpPort;

    @ApiModelProperty(value = "RTMPS端口")
    private int rtmpSSlPort;

    @ApiModelProperty(value = "RTP收流端口（单端口模式有用）")
    private int rtpProxyPort;

    @ApiModelProperty(value = "RTSP端口")
    private int rtspPort;

    @ApiModelProperty(value = "RTSPS端口")
    private int rtspSSLPort;

    @ApiModelProperty(value = "是否开启自动配置ZLM")
    private boolean autoConfig;

    @ApiModelProperty(value = "ZLM鉴权参数")
    private String secret;

    @ApiModelProperty(value = "keepalive hook触发间隔,单位秒")
    private int hookAliveInterval;

    @ApiModelProperty(value = "是否使用多端口模式")
    private boolean rtpEnable;

    @ApiModelProperty(value = "状态")
    private boolean status;

    @ApiModelProperty(value = "多端口RTP收流端口范围")
    private String rtpPortRange;

    @ApiModelProperty(value = "RTP发流端口范围")
    private String sendRtpPortRange;

    @ApiModelProperty(value = "assist服务端口")
    private int recordAssistPort;

    @ApiModelProperty(value = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private String createTime;

    @ApiModelProperty(value = "更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateTime;

    @ApiModelProperty(value = "上次心跳时间")
    private String lastKeepaliveTime;

    @ApiModelProperty(value = "是否是默认ZLM")
    private boolean defaultServer;

//    @ApiModelProperty(value = "SSRC信息")
//    private SsrcConfig ssrcConfig;

    @ApiModelProperty(value = "当前使用到的端口")
    private int currentPort;

    private Integer projectId;



    /**
     * 每一台ZLM都有一套独立的SSRC列表
     * 在ApplicationCheckRunner里对mediaServerSsrcMap进行初始化
     */
//    @Schema(value = "ID")
//    private HashMap<String, SsrcConfig> mediaServerSsrcMap;
}
