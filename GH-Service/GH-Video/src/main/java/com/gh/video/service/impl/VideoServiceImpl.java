package com.gh.video.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gh.basic.feign.client.BasicFeignClient;
import com.gh.basic.feign.model.entity.Area;
import com.gh.common.exception.ExceptionEnum;
import com.gh.common.exception.GhCustomException;
import com.gh.common.utils.GHResponse;
import com.gh.common.utils.PageResult;
import com.gh.common.utils.RequestUtil;
import com.gh.resource.feign.client.ResourceFeignClient;
import com.gh.resource.feign.dto.Device;
import com.gh.video.mapper.VideoMapper;
import com.gh.video.model.dto.CamDTO;
import com.gh.video.model.dto.DelCam;
import com.gh.video.model.entity.Server;
import com.gh.video.model.entity.Video;
import com.gh.video.model.vo.AreaCamTreeVo;
import com.gh.video.model.vo.CamVo;
import com.gh.video.service.H5Service;
import com.gh.video.service.ServerService;
import com.gh.video.service.VideoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
public class VideoServiceImpl extends ServiceImpl<VideoMapper, Video> implements VideoService {
    @Autowired
    private VideoMapper videoMapper;
    @Autowired
    private H5Service h5Service;
    @Autowired
    private ServerService serverService;
    @Autowired
    private BasicFeignClient basicFeignClient;

    @Autowired
    private ResourceFeignClient deviceFeignClient;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateCamToDevice(Integer serverId, Integer productId, Integer serverType, String mediaServerId) {
       try{
        List<Video> videos = queryVideos(serverId, serverType, mediaServerId);

        if (CollUtil.isEmpty(videos)) {
            log.info("未发现摄像头数据: serverId={}, serverType={}, mediaServerId={}", serverId, serverType, mediaServerId);
            return true;
        }
           List<Device> deviceDtos = videos.stream()
                .map(this::convertToDeviceDto)
                .collect(Collectors.toList());
        log.info("同步摄像头数据: deviceDtos={}", JSONUtil.toJsonStr(deviceDtos));
        GHResponse ghResponse = deviceFeignClient.addDoorDevices(deviceDtos);
        return ghResponse.getSuccess();
        } catch (Exception e) {
            log.error("同步摄像头数据失败: serverId={}, error={}", serverId, e.getMessage());
            return false;
        }
    }

    @Override
    public boolean AddCam(Video video, String ip, Integer port) {
        Server server = serverService.lambdaQuery().eq(Server::getId, video.getServerId())
                .one();
        if (StrUtil.isEmpty(ip)) {
            ip = server.getIp();
            port = Convert.toInt(server.getPort(), 2500);
        }
        if (null != port && StrUtil.isNotEmpty(ip)) {
            if (video.getServerType() != 2) {
                h5Service.AddH5Cam(video, ip, port.toString(), server.getSession());
            }
            if (video.getServerId() != null) {
                boolean insert = this.saveOrUpdate(video,
                        Wrappers.<Video>lambdaQuery().eq(Video::getStrName, video.getStrName()));
                if (insert) {
                    return true;
                }
            }
        } else {
            throw new GhCustomException(ExceptionEnum.SERVER_IMPORT_ERROR);
        }
        return false;
    }

    @Override
    // @Transactional
    public boolean DelCam(List<DelCam> delCams) {
        if (CollUtil.isNotEmpty(delCams)) {
            delCams.forEach(cam -> {
                PageResult<List<Server>> pageListResult = serverService.select(cam.getServerId(), null, null, null,
                        null, null);
                if (CollUtil.isNotEmpty(pageListResult.getData())) {
                    UpdateWrapper<Video> updateWrapper = new UpdateWrapper<>();
                    if (StrUtil.isNotBlank(cam.getToken())) {
                        updateWrapper.eq("camToken", cam.getToken());
                    }
                    if (null != cam.getServerId()) {
                        updateWrapper.eq("serverId", cam.getServerId());
                    }
                    videoMapper.delete(updateWrapper);
                    try {
                        h5Service.OnlyDelH5(pageListResult.getData().get(0), cam.getToken());
                    } catch (Exception e) {
                        log.error(e.getMessage());
                    }
                }
            });
            return true;
        }
        return false;
    }

    @Override
    public boolean UpdateCam(Video video) {
        if (StrUtil.isBlank(video.getCamToken())) {
            throw new GhCustomException(ExceptionEnum.CAM_TOKEN_EMPTY);
        }
        PageResult<List<Server>> pageListResult = serverService.select(video.getServerId(), null, null, null, null,
                null);
        if (CollUtil.isNotEmpty(pageListResult.getData())) {
            if (video.getVideoType().equals("H5_CLOUD")) {

                return updateVideo(video);
            }
            h5Service.OnlyDelH5(pageListResult.getData().get(0), video.getCamToken());
            boolean update = updateVideo(video);
            if (update) {
                h5Service.AddH5Cam(video, pageListResult.getData().get(0).getIp(),
                        pageListResult.getData().get(0).getPort(), pageListResult.getData().get(0).getSession());
                return true;
            }
        }
        return false;
    }

    @Override
    public PageResult<List<CamVo>> SelectCam(Integer projectId, Integer menuId, Integer serverId, Integer[] areaId,
            String keyword, String token, Integer page, Integer size) {

        Page<CamVo> camVoPage = null;
        if (PageResult.isPage(page, size)) {
            camVoPage = new Page<>(page, size);
        }
        if (StrUtil.isNotEmpty(keyword)) {
            keyword = "%" + keyword + "%";
        }
        List<CamVo> camVos;
        if (RequestUtil.Admin() || RequestUtil.ProjectAdmin()) {
            camVos = videoMapper.GetCam(camVoPage, projectId, menuId, serverId,
                    areaId == null ? null : Arrays.asList(areaId), keyword, token);
        } else {
            camVos = videoMapper.GetRoleCam(camVoPage, projectId, menuId, serverId,
                    areaId == null ? null : Arrays.asList(areaId), keyword, RequestUtil.getUserId(), token);
        }
        return PageResult.<List<CamVo>>builder()
                .data(camVos).total(camVoPage != null ? camVoPage.getTotal() : camVos.size()).build();
    }

    /**
     * 查询区域里的摄像机
     *
     * @param projectId
     * @return
     */
    @Override
    public List<AreaCamTreeVo> GetTreeCams(Integer projectId) {
        GHResponse<List<Area>> entity = basicFeignClient.Selects(null, projectId, null, null, null);
        List<Area> areas = entity.getData();

        PageResult<List<CamVo>> select = SelectCam(projectId, null, null, null, null, null, null, null);
        log.error(JSONUtil.toJsonStr(select.getData()));
        return GetParentTreeNode(areas, 0, select.getData());
    }

    private List<AreaCamTreeVo> GetParentTreeNode(List<Area> areas, Integer parentId, List<CamVo> camVos) {
        List<AreaCamTreeVo> areaList = areas.stream().filter(area -> area.getParentId().equals(parentId))
                .map(area -> {
                    AreaCamTreeVo areaCamTreeVo = AreaCamTreeVo.builder().id(area.getId()).label(area.getName())
                            .isCam(false).parentId(area.getParentId()).build();
                    List<CamVo> data = camVos.stream()
                            .filter(camVo -> camVo.getAreaId() != null && camVo.getAreaId().equals(area.getId()))
                            .collect(Collectors.toList());
                    List<AreaCamTreeVo> cams = data.stream().map(d -> AreaCamTreeVo.builder().label(d.getStrName())
                            .serverType(d.getServerType())
                            .app(d.getApp())
                            .camIp(d.getStrSrcIpAddress())
                            .username(d.getStrUser())
                            .password(d.getStrPasswd())
                            .profileToken(d.getProfileToken())
                            .ptzEnable(d.getPtzEnable())
                            .session(d.getSession())
                            .ip(d.getIp()).port(d.getPort()).token(d.getCamToken()).build())
                            .collect(Collectors.toList());
                    cams.addAll(GetParentTreeNode(areas, area.getId(), camVos));
                    areaCamTreeVo.setChildren(cams);
                    return areaCamTreeVo;
                }).collect(Collectors.toList());
        return areaList;
    }

    private boolean updateVideo(Video video) {
        UpdateWrapper<Video> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("camToken", video.getCamToken());
        updateWrapper.eq("serverId", video.getServerId());
        int update = videoMapper.update(video, updateWrapper);
        return update > 0;
    }

    @Override
    public List<CamDTO> GetCamByToken(String token) {
        return videoMapper.GetCamByToken(token);
    }

    private void updateVideoProducts(Integer serverId, Integer productId, Integer serverType, String mediaServerId) {
        LambdaUpdateWrapper<Video> wrapper = Wrappers.<Video>lambdaUpdate()
                .set(Video::getProductId, productId);
        if (serverType == null || serverType == 1 || serverType == 3) {
            wrapper.eq(serverId != null, Video::getServerId, serverId);
        } else if (serverType == 2) {
            wrapper.eq(Video::getMediaServerId, mediaServerId);
        }

        update(wrapper);
    }

    private List<Video> queryVideos(Integer serverId, Integer serverType, String mediaServerId) {

        if (serverType == null || serverType == 1 || serverType == 3) {
            return lambdaQuery().eq(serverId != null, Video::getServerId, serverId).list();
        } else if (serverType == 2) {
            return lambdaQuery().eq(Video::getMediaServerId, mediaServerId).list();
        }
        return null;

    }

    private Device convertToDeviceDto(Video video) {
        return Device.builder()
                .projectId(video.getProjectId())
                .areaId(video.getAreaId())
                .productId(video.getProductId())
                .name(video.getStrName())
                .model("cam")
                .code("cam_" + video.getCamToken())
                .status(0)
                .camFlag(true)
                .build();
    }

}
