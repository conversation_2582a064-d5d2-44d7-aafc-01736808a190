package com.gh.video.service;

import com.gh.video.model.entity.Video;

import java.util.List;

public interface XYMediaServerService {



    com.alibaba.fastjson.JSONObject getGroupAndCamTree(String ip ,String port ,String token);


   boolean syncCamList(Integer projectId,String ip,String port,String token,Integer productId,Integer serverId,String groupId);



   List<Video> getCamList();

   String getVideoUrl( String nodeno,String agreementType);


    boolean ptz(String nodeno,String direction ,Integer start_flag);
}
