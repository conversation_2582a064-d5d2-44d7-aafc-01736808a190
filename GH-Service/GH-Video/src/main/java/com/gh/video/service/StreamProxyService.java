package com.gh.video.service;

import com.alibaba.fastjson.JSONObject;
import com.gh.video.model.entity.Server;
import com.gh.video.model.entity.Video;

public interface StreamProxyService  {
    boolean start(Video streamProxyItem, Server server);

    JSONObject addStreamProxyToZlm(Video param,Server server);




    void del(String app, String stream);

    JSONObject removeStreamProxyFromZlm(Video param);



     boolean stop(String app, String stream);


}
