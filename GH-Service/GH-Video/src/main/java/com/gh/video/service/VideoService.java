package com.gh.video.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.gh.common.utils.PageResult;
import com.gh.video.model.dto.CamDTO;
import com.gh.video.model.dto.DelCam;
import com.gh.video.model.entity.Video;
import com.gh.video.model.vo.AreaCamTreeVo;
import com.gh.video.model.vo.CamVo;

import java.util.List;

public interface VideoService extends IService<Video> {
    boolean AddCam(Video video,String ip,Integer port);

    boolean DelCam(List<DelCam> delCams);

    boolean UpdateCam(Video video);

    PageResult<List<CamVo>> SelectCam(Integer projectId, Integer menuId, Integer serverId, Integer[] areaId, String keyword,String token ,Integer page, Integer size);


    List<AreaCamTreeVo> GetTreeCams(Integer projectId);


    Boolean updateCamToDevice(Integer serverId,Integer productId,Integer serverType,String mediaServerId);


    List<CamDTO> GetCamByToken(String token);



}
