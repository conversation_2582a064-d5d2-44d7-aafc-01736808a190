package com.gh.video.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.StrUtil;
import com.gh.common.exception.ExceptionEnum;
import com.gh.common.exception.GhCustomException;
import com.gh.common.exception.SystemEnumMsg;
import com.gh.common.utils.GHResponse;
import com.gh.common.utils.PageResult;
import com.gh.log.annotation.OperLog;
import com.gh.video.model.dto.RunInfo;
import com.gh.video.model.entity.Server;
import com.gh.video.service.H5Service;
import com.gh.video.service.ServerService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Arrays;
import java.util.List;

@RequestMapping("/server")
@RestController
@Api(tags = "流媒体管理接口")

public class ServerController {
    @Autowired
    private ServerService serverService;
    @Autowired
    private H5Service h5Service;

    @PostMapping
    @ApiOperation("添加流媒体")
    @OperLog(category = "流媒体管理", description = "添加流媒体")
    @PreAuthorize("hasAnyAuthority('sys_admin','sys_project_admin','sys_server_add')")
    public GHResponse add(@RequestBody @Valid Server server) {

        if (checkServerExists(server.getProjectId(), server.getIp(), null)) {
            throw new GhCustomException(ExceptionEnum.PROJECT_REPEATE_ADD);
        }
        return serverService.save(server)
                ? GHResponse.ok(null, SystemEnumMsg.CREATE_SUCCESS.msg())
                : GHResponse.failed(SystemEnumMsg.CREATE_ERROR.msg());
    }

    @PutMapping
    @ApiOperation("更新流媒体")
    @OperLog(category = "流媒体管理", description = "更新流媒体")
    @PreAuthorize("hasAnyAuthority('sys_admin','sys_project_admin','sys_server_update')")
    public GHResponse patch(@RequestBody @Valid Server server) {
        if (checkServerExists(server.getProjectId(), server.getIp(), server.getId())) {
            throw new GhCustomException(ExceptionEnum.PROJECT_REPEATE_ADD);
        }
        return serverService.updateById(server)
                ? GHResponse.ok()
                : GHResponse.failed(SystemEnumMsg.Update_ERROR.msg());
    }

    @DeleteMapping
    @ApiOperation("删除流媒体")
    @OperLog(category = "流媒体管理", description = "删除流媒体")
    @PreAuthorize("hasAnyAuthority('sys_admin','sys_project_admin','sys_server_del')")
    public GHResponse delete(@RequestParam Integer[] ids) {
        if (ArrayUtil.isEmpty(ids)) {
            return GHResponse.failed(SystemEnumMsg.Delete_ERROR.msg());
        }

        return serverService.removeByIds(Arrays.asList(ids))
                ? GHResponse.ok()
                : GHResponse.failed(SystemEnumMsg.Delete_ERROR.msg());
    }

    @GetMapping("{id}")
    @ApiOperation("查询流媒体")
    public GHResponse<Server> getServerById(@PathVariable(value = "id", required = true) Integer id) {

        PageResult<List<Server>> pageListResult = serverService.select(id, null, null, null, null, null);
        if (CollUtil.isNotEmpty(pageListResult.getData())) {
            return GHResponse.ok(pageListResult.getData().get(0));
        }

        return GHResponse.ok(null);
    }

    @GetMapping
    @ApiOperation("查询流媒体")
    public GHResponse<List<Server>> getServerList(Integer projectId, String keyword, Integer page, Integer size) {
        PageResult<List<Server>> pageResultDto = serverService.select(null, projectId, keyword, null, page, size);
        return GHResponse.ok(pageResultDto.getData(), pageResultDto.getTotal());
    }

    @GetMapping("info")
    @ApiOperation("获取运行信息")
    public GHResponse<RunInfo> getServerRunInfo(String ip, Integer port) {
        RunInfo runInfo = h5Service.GetRunInfo(ip, port);
        return GHResponse.ok(runInfo);
    }

    @GetMapping("sync")
    @ApiOperation("同步流媒体数据到本地")
   
    @PreAuthorize("hasAnyAuthority('sys_admin','sys_project_admin','sys_server_sync')")
    public GHResponse getServerSrc(Integer serverId, Integer productId) {

        boolean update = serverService.UpdateFromRemoteServer(serverId, productId);

        return GHResponse.ok();
    }

    @GetMapping("session")
    @ApiOperation("获取session")
    public GHResponse session(Integer id, String ip, Integer port) {
        if (id == null && StrUtil.isEmpty(ip) && port == null) {
            return GHResponse.ok();
        }
        Server server = serverService.lambdaQuery().eq(id != null, Server::getId, id)
                .eq(StrUtil.isNotEmpty(ip), Server::getIp, ip)
                .eq(null != port, Server::getPort, port)
                .one();
        return GHResponse.ok(server != null ? server.getSession() : null);
    }

    private boolean checkServerExists(Integer projectId, String ip, Integer excludeId) {
        if (projectId == null || StrUtil.isEmpty(ip)) {
            return false;
        }

        return serverService.lambdaQuery()
                .eq(Server::getProjectId, projectId)
                .eq(Server::getIp, ip)
                .ne(excludeId != null, Server::getId, excludeId)
                .count() > 0;
    }
}
