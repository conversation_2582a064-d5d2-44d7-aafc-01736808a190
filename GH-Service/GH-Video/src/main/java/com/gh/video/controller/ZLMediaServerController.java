//package com.gh.video.controller;
//
//import cn.hutool.core.collection.CollUtil;
//import cn.hutool.core.util.ArrayUtil;
//import cn.hutool.core.util.StrUtil;
//import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
//import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
//import com.gh.common.exception.ExceptionEnum;
//import com.gh.common.exception.GhCustomException;
//import com.gh.common.exception.SystemEnumMsg;
//import com.gh.common.utils.GHResponse;
//import com.gh.common.utils.PageResult;
//import com.gh.video.model.entity.ZLMMediaServer;
//import com.gh.video.service.ZLMMediaServerService;
//import io.swagger.annotations.Api;
//import io.swagger.annotations.ApiOperation;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.security.access.prepost.PreAuthorize;
//import org.springframework.web.bind.annotation.*;
//
//import javax.validation.Valid;
//import java.util.Arrays;
//import java.util.List;
//
//@RequestMapping("/zl-server")
//@RestController
//@Api(tags = "zl流媒体管理接口")
//
//public class ZLMediaServerController {
//    @Autowired
//    private ZLMMediaServerService serverService;
//
//
//
//    @PostMapping
//    @ApiOperation("添加流媒体")
//    @PreAuthorize("hasAnyAuthority('sys_admin','sys_project_admin','sys_server_add')")
//    public GHResponse Add(@RequestBody @Valid ZLMMediaServer server)
//    {
//        List<ZLMMediaServer> list = serverService.lambdaQuery().eq(ZLMMediaServer::getId, server.getId())
//                .eq(ZLMMediaServer::getProjectId,server.getProjectId())
//                .list();
//        if(CollUtil.isNotEmpty(list))
//        {
//            throw  new GhCustomException(ExceptionEnum.PROJECT_REPEATE_ADD);
//        }
//        boolean insert = serverService.save(server);
//        if(insert)
//        {
//            return GHResponse.ok(null, SystemEnumMsg.CREATE_SUCCESS.msg());
//        }
//        return GHResponse.failed(SystemEnumMsg.CREATE_ERROR.msg());
//    }
//
//    @PutMapping
//    @ApiOperation("更新流媒体")
//    @PreAuthorize("hasAnyAuthority('sys_admin','sys_project_admin','sys_server_update')")
//    public GHResponse Patch(@RequestBody @Valid ZLMMediaServer server)
//    {
//
//        boolean update = serverService.updateById(server);
//        if(update)
//        {
//            return GHResponse.ok();
//        }
//        return GHResponse.failed(SystemEnumMsg.Update_ERROR.msg());
//    }
//
//
//    @DeleteMapping
//    @ApiOperation("删除流媒体")
//    @PreAuthorize("hasAnyAuthority('sys_admin','sys_project_admin','sys_server_del')")
//    public GHResponse Delete(@RequestParam String[] ids)
//    {
//
//        if(ArrayUtil.isNotEmpty(ids))
//        {
//            boolean delete = serverService.removeByIds(Arrays.asList(ids));
//            if(delete)
//            {
//                return GHResponse.ok();
//            }
//        }
//
//        return GHResponse.failed(SystemEnumMsg.Delete_ERROR.msg());
//    }
//
//
//    @GetMapping("{id}")
//    @ApiOperation("查询流媒体")
//    public GHResponse<ZLMMediaServer> Select(@PathVariable(value = "id",required = true) String id)
//    {
//
//        ZLMMediaServer mediaServer = serverService.getMediaServer(id);
//        return GHResponse.ok(mediaServer);
//    }
//
//
//    @GetMapping
//    @ApiOperation("查询流媒体")
//    public GHResponse<List<ZLMMediaServer>> Selects(Integer projectId,String keyword,Integer page,Integer size)
//    {
//
//        LambdaQueryChainWrapper<ZLMMediaServer> wrapper = this.serverService.lambdaQuery().like(StrUtil.isNotEmpty(keyword), ZLMMediaServer::getName, keyword)
//                .eq(ZLMMediaServer::getProjectId, projectId);
//        if(PageResult.isPage(page,size))
//        {
//            Page<ZLMMediaServer> serverPage = wrapper.page(new Page<>(page, size));
//            return GHResponse.ok(serverPage.getRecords(),serverPage.getTotal());
//        }
//
//        List<ZLMMediaServer> list = wrapper.list();
//        return GHResponse.ok(list,list.size());
//    }
//
//
//
//
//}
