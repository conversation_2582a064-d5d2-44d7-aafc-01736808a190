package com.gh.video.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gh.video.model.dto.CamDTO;
import com.gh.video.model.entity.Video;
import com.gh.video.model.vo.CamVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface VideoMapper extends BaseMapper<Video> {
    List<CamVo> GetCam(Page page, @Param("projectId") Integer projectId,
                       @Param("menuId") Integer menuId, @Param("serverId") Integer serverId,
                        @Param("areaId") List<Integer> areaId,
                        @Param("keyword") String keyword, @Param("token") String token);

    List<CamVo> GetRoleCam(Page page, @Param("projectId") Integer projectId,
                       @Param("menuId") Integer menuId, @Param("serverId") Integer serverId,
                       @Param("areaId") List<Integer> areaId,
                       @Param("keyword") String keyword,Integer userId,@Param("token") String token);


    List<CamDTO> GetCamByToken(String token);

}
