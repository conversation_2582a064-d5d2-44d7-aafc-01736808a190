package com.gh.video.listener;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.exception.ExcelDataConvertException;
import com.gh.common.utils.RequestUtil;
import com.gh.video.model.dto.VideoDto;
import com.gh.video.model.entity.Video;
import com.gh.video.service.VideoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class UploadVideoListener extends AnalysisEventListener<VideoDto> {

    @Autowired
    private VideoService videoService;

    private static final String DEFAULT_VIDEO_TYPE = "H5_ONVIF";
    private static final String ZLM_PREFIX = "zlm_";

    @Override
    public void invoke(VideoDto videoDto, AnalysisContext analysisContext) {
        try {
            Video video = convertToVideo(videoDto);
            videoService.AddCam(video, videoDto.getServerIp(),videoDto.getServerPort());
        }catch (Exception e)
        {
            log.error("导入摄像机失败",e);
        }

    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
        log.info("视频数据导入完成");
    }

    @Override
    public void onException(Exception exception, AnalysisContext context) {
        if (exception instanceof ExcelDataConvertException) {
            ExcelDataConvertException ex = (ExcelDataConvertException) exception;
            log.error("Excel解析错误 - 行:{}, 列:{}, 数据:{}", 
                ex.getRowIndex(), 
                ex.getColumnIndex(), 
                ex.getCellData());
        } else {
            log.error("数据处理异常", exception);
        }
    }

    private Video convertToVideo(VideoDto videoDto) {
        Video video = new Video();
        BeanUtil.copyProperties(videoDto, video);
        video.setProjectId(RequestUtil.CurrentProject());
        
        // 设置视频类型
        if (StrUtil.isEmpty(videoDto.getNType())) {
            video.setVideoType(DEFAULT_VIDEO_TYPE);
        }
        
        // 处理不同的服务器类型
        processServerType(video);
        
        return video;
    }

    private void processServerType(Video video) {
        Integer serverType = video.getServerType();
        if (serverType == null) {
            return;
        }

        switch (serverType) {
            case 1:
                processOnvifServer(video);
                break;
            case 2:
                processZlmServer(video);
                break;
            default:
                log.warn("未知的服务器类型: {}", serverType);
        }
    }


    private void processOnvifServer(Video video) {
        video.setVideoType(DEFAULT_VIDEO_TYPE);
        if (StrUtil.isEmpty(video.getCamToken())) {
            video.setCamToken(RandomUtil.randomString(6));
        }
    }

    private void processZlmServer(Video video) {
        video.setCamToken(ZLM_PREFIX + RandomUtil.randomString(6));
        video.setEnable(true);
        video.setApp("live");
        video.setPlayType(1);
    }





}
