package com.gh.video.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ArrayUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.gh.common.exception.ExceptionEnum;
import com.gh.common.exception.GhCustomException;
import com.gh.common.exception.SystemEnumMsg;
import com.gh.common.utils.GHResponse;
import com.gh.common.utils.PageResult;
import com.gh.log.annotation.OperLog;
import com.gh.video.model.entity.Line;
import com.gh.video.model.entity.LineCam;
import com.gh.video.model.vo.LineVO;
import com.gh.video.service.LineCamService;
import com.gh.video.service.LineService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@RequestMapping("/line")
@RestController
@Api(tags = "视频线路管理")
@Slf4j
public class LineController {

    @Autowired
    private LineService lineService;

    @Autowired
    private LineCamService lineCamService;

    @PostMapping
    @ApiOperation("添加线路")
    @Transactional
    @OperLog(category = "视频线路管理", description = "添加线路")
    @PreAuthorize("hasAnyAuthority('sys_admin','sys_project_admin','sys_server_add')")
    public GHResponse addLine(@RequestBody @Valid Line line) {
        if(checkLineNameExists(line.getName(), null)){
            throw new GhCustomException(ExceptionEnum.PROJECT_REPEATE_ADD);
        }
        if (!lineService.save(line)) {
            return GHResponse.failed(SystemEnumMsg.CREATE_ERROR.msg());
        }
        saveCams(line.getId(), line.getTokens());
        return GHResponse.ok(null, SystemEnumMsg.CREATE_SUCCESS.msg());
    }

    @PutMapping
    @ApiOperation("更新线路")
    @OperLog(category = "视频线路管理", description = "更新线路")
    @PreAuthorize("hasAnyAuthority('sys_admin','sys_project_admin','sys_server_update')")
    public GHResponse patchLine(@RequestBody @Valid Line line) {
        if(checkLineNameExists(line.getName(), line.getId())){
            throw new GhCustomException(ExceptionEnum.PROJECT_REPEATE_ADD);
        }
        if (!lineService.updateById(line)) {
            return GHResponse.failed(SystemEnumMsg.Update_ERROR.msg());
        }
        lineCamService.remove(Wrappers.<LineCam>lambdaQuery()
        .eq(LineCam::getLineId, line.getId()));
        saveCams(line.getId(), line.getTokens());
        return GHResponse.ok();
    }

    @DeleteMapping
    @ApiOperation("删除线路")
    @OperLog(category = "视频线路管理", description = "删除线路")
    public GHResponse delLines(@RequestParam Integer[] ids) {

        if (ArrayUtil.isNotEmpty(ids)) {
            boolean delete = lineService.removeByIds(Arrays.asList(ids));
            if (delete) {
                return GHResponse.ok();
            }
        }

        return GHResponse.failed(SystemEnumMsg.Delete_ERROR.msg());
    }

    @GetMapping
    @ApiOperation("查询视频巡更线路")
    public GHResponse<List<LineVO>> getLine(Integer projectId, String keyword, Integer page, Integer size) {
        PageResult<List<LineVO>> line = lineService.getLine(keyword, projectId, page, size);
        return GHResponse.ok(line.getData(), line.getTotal());
    }

    private boolean checkLineNameExists(String name, Integer excludeId) {

        LambdaQueryWrapper<Line> wrapper = Wrappers.<Line>lambdaQuery()
                .eq(Line::getName, name)
                .ne(excludeId != null, Line::getId, excludeId);

        return lineService.count(wrapper) > 0;

    }

    private void saveCams(Integer lineId, List<String> tokens) {
        if (CollUtil.isEmpty(tokens)) {
            return;
        }

        List<LineCam> cams = tokens.stream()
            .map(token -> LineCam.builder()
                .camToken(token)
                .lineId(lineId)
                .build())
            .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(cams)) {
             lineCamService.saveBatch(cams);
        }
    }



}
