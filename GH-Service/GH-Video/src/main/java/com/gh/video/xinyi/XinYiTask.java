package com.gh.video.xinyi;

import cn.hutool.core.collection.CollUtil;
import com.gh.common.redis.RedisHelper;
import com.gh.common.utils.GHResponse;
import com.gh.resource.feign.client.ResourceFeignClient;
import com.gh.resource.feign.dto.Device;
import com.gh.video.model.entity.Video;
import com.gh.video.service.XYMediaServerService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * Description:
 * User: zhangkeguang
 * Date: 2024-12-31-09:41
 */
@Component
@Slf4j
public class XinYiTask {

    @Autowired
    private XYMediaServerService xyMediaServerService;


    @Autowired
    private RedisHelper redisHelper;


    @Autowired
    private ResourceFeignClient deviceFeignClient;

    @Scheduled(fixedRate = 1000 * 3)
    public void run() {
        List<Video> camList = xyMediaServerService.getCamList();
        if (CollUtil.isNotEmpty(camList)) {
            for (Video video : camList) {
                GHResponse<List<Device>> deviceAll = deviceFeignClient.getDeviceAll(null, null, null, "cam_" + video.getCamToken());
                if (deviceAll != null && deviceAll.getData() != null&& deviceAll.getData().size() > 0) {
                    Device device = deviceAll.getData().get(0);
                    redisHelper.setHash(14, "product:" + device.getProductId() + ":" + device.getId(),
                            "comStatus", video.getStatus()?"1":"0");
                }
            }
        }
    }
}
