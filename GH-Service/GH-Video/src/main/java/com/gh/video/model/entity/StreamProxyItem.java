package com.gh.video.model.entity;


import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * <AUTHOR>
 */
@ApiModel(value = "拉流代理的信息")
@TableName("stream_proxy")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class StreamProxyItem  {

    @TableId(type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "类型")
    private String type;

    @ApiModelProperty(value = "应用名")
    private String app;

    @ApiModelProperty(value = "流ID")
    private String stream;

    @ApiModelProperty(value = "流媒体服务ID")
    private String mediaServerId;

    @ApiModelProperty(value = "拉流地址")
    private String url;

    private String ip;

    private String username;

    private String password;

    //ffmpeg使用
    @ApiModelProperty(value = "拉流地址")
    private String src_url;
    @ApiModelProperty(value = "目标地址")
    private String dst_url;
    @ApiModelProperty(value = "超时时间")
    private int timeout_ms;

    @ApiModelProperty(value = "ffmpeg模板KEY")
    private String ffmpeg_cmd_key;

    @ApiModelProperty(value = "rtsp拉流时，拉流方式，0：tcp，1：udp，2：组播")
    private String rtp_type;

    @ApiModelProperty(value = "是否启用")
    private boolean enable;
    @ApiModelProperty(value = "是否启用HLS")
    private boolean enable_hls;
    @ApiModelProperty(value = "是否启用MP4")
    private boolean enable_mp4;
    @ApiModelProperty(value = "是否 无人观看时删除")
    private boolean enable_remove_none_reader;

    @ApiModelProperty(value = "是否 无人观看时自动停用")
    private boolean enable_disable_none_reader;

    @ApiModelProperty(value = "上级平台国标ID")
    private String platformGbId;

    @ApiModelProperty(value = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private String createTime;

    private Integer areaId;


}
