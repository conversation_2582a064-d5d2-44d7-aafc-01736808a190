package com.gh.video.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gh.video.model.entity.StreamProxyItem;
import com.gh.video.model.vo.StreamProxyItemVo;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;

@Mapper
@Repository
public interface StreamProxyMapper extends BaseMapper<StreamProxyItem> {
   List<StreamProxyItemVo> getStreamProxyItem(Page page,String keyword,String serverId,Integer areaId,Integer projectId);

}
