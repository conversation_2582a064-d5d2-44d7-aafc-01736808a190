package com.gh.video.service.impl;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.net.NetUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.json.JSONUtil;
import com.gh.common.exception.ExceptionEnum;
import com.gh.common.exception.GhCustomException;
import com.gh.video.model.dto.H5Response;
import com.gh.video.model.dto.H5SrcResponse;
import com.gh.video.model.dto.RunInfo;
import com.gh.video.model.entity.Server;
import com.gh.video.model.entity.Video;
import com.gh.video.service.H5Service;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
//@RequiredArgsConstructor
public class H5ServiceImpl implements H5Service {

    @Autowired
    private RestTemplate restTemplate;
    private static final String API_BASE_PATH = "/api/v1";
    private static final int TIMEOUT = 5000;


    @Override
    @Async
    public Boolean OnlyDelH5(Server server, String token) {
        if (server == null || StrUtil.isEmpty(token)) {
            log.warn("删除H5请求参数无效: server={}, token={}", server, token);
            return false;
        }
        Map<String, String> params = new HashMap<>(2);
        params.put("token", token);
        params.put("session", server.getSession());

        String url = createUrl(server.getIp(), server.getPort(), "/DelSrc", params);
        try {
            String response = executeGet(url);
            return parseResponse(response);
        } catch (Exception e) {
            log.error("删除H5失败: {}", e.getMessage());
            return false;
        }
    }

    @Override
    @Async
    public Boolean AddH5Cam(Video video, String ip, String port,String session) {
        if (!validateAddH5CamParams(video, ip, port)) {
            return false;
        }
        String url = createAddCamUrl(video, ip, port, session);
     
        try {
            String response = executeGet(url);
            return parseResponse(response);
        } catch (Exception e) {
            e.printStackTrace();
            throw new GhCustomException(ExceptionEnum.VIDEO_IMPORT_ERROR);
        }
    }

    @Override
    public RunInfo GetRunInfo(String ip, Integer port) {
        validateIpAndPort(ip, port);
        String url = createUrl(ip, String.valueOf(port), "/GetRunInfo", null);
        String resp = executeGet(url);
        return parseRunInfo(resp);
    }

    @Override
    @Transactional
    public H5SrcResponse GetSrc(Server server) {
        Map<String, String> params = new HashMap<>(1);
        params.put("session", server.getSession());

        String url = createUrl(server.getIp(), server.getPort(), "/GetSrc", params);
        String response = executeGet(url);
        return JSONUtil.toBean(response, H5SrcResponse.class);
    }

    private String createUrl(String ip, String port, String path, Map<String, String> params) {
        StringBuilder url = new StringBuilder()
            .append("http://")
            .append(ip)
            .append(":")
            .append(port)
            .append(API_BASE_PATH)
            .append(path);

        if (MapUtil.isNotEmpty(params)) {
            url.append("?").append(buildQueryString(params));
        }

        return url.toString();
    }

    private String createAddCamUrl(Video video, String ip, String port, String session) {
        String endpoint = video.getVideoType().equals("H5_ONVIF") ? "/AddSrcONVIF" : "/AddSrcRTSP";
        
        Map<String, String> params = new HashMap<>();
        params.put("name", video.getStrName());
        params.put("token", video.getCamToken());
        params.put("user", video.getStrUser());
        params.put("password", video.getStrPasswd());
        params.put("session", session);

        if (video.getVideoType().equals("H5_ONVIF")) {
            params.put("ip", video.getStrSrcIpAddress());
            params.put("port", video.getStrSrcPort());
        } else {
            params.put("url", video.getStrUrl());
        }

        return createUrl(ip, port, endpoint, params);
    }

    private String buildQueryString(Map<String, String> params) {
        return params.entrySet().stream()
            .filter(e -> StrUtil.isNotBlank(e.getValue()))
            .map(e -> e.getKey() + "=" + e.getValue())
            .collect(Collectors.joining("&"));
    }

    private String executeGet(String url) {
        return HttpRequest.get(url)
            .timeout(TIMEOUT)
            .execute()
            .body();
    }

    private Boolean parseResponse(String response) {
        return StrUtil.isNotBlank(response) && 
               JSONUtil.toBean(response, H5Response.class).getBStatus();
    }

    private RunInfo parseRunInfo(String response) {
        return StrUtil.isNotBlank(response) ? 
               JSONUtil.toBean(response, RunInfo.class) : null;
    }

    private void validateIpAndPort(String ip, Integer port) {
        if (!NetUtil.isValidPort(port)) {
            throw new GhCustomException(ExceptionEnum.PORT_ERROR);
        }
        if (!io.netty.util.NetUtil.isValidIpV4Address(ip)) {
            throw new GhCustomException(ExceptionEnum.IP_ERROR);
        }
    }

    private boolean validateAddH5CamParams(Video video, String ip, String port) {
        if (video == null || StrUtil.hasBlank(ip, port)) {
            log.warn("添加H5摄像头参数无效: video={}, ip={}, port={}", video, ip, port);
            return false;
        }
        return true;
    }


}
