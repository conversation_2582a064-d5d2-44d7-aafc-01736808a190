package com.gh.video.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.StringRedisTemplate;

//@Configuration
public class RestTemplateConfig {

//    @Bean
//    @LoadBalanced
//    public RestTemplate GetRestTemplate()
//    {
//        return new RestTemplate();
//    }

    @Autowired
    private RedisConnectionFactory redisConnectionFactory;


    @Bean
    public StringRedisTemplate stringRedisTemplate() {
        //采用默认配置即可-后续有自定义配置时则在此处添加即可
        StringRedisTemplate stringRedisTemplate = new StringRedisTemplate();
        stringRedisTemplate.setConnectionFactory(redisConnectionFactory);
        return stringRedisTemplate;
    }

}
