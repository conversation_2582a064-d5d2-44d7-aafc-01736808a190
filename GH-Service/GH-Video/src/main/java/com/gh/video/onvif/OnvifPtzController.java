package com.gh.video.onvif;

import be.teletask.onvif.OnvifManager;
import be.teletask.onvif.listeners.OnvifResponseListener;
import be.teletask.onvif.listeners.OnvifStatusListener;
import be.teletask.onvif.models.OnvifDevice;
import be.teletask.onvif.models.OnvifPreset;
import be.teletask.onvif.models.OnvifStatus;
import be.teletask.onvif.responses.OnvifResponse;
import cn.hutool.json.JSONUtil;
import com.gh.common.utils.GHResponse;
import com.gh.video.model.vo.PtzVo;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;

/**
 * Description:
 * User: zhangkeguang
 * Date: 2022-11-08-08:59
 */
@RestController
@Slf4j
@Api(tags = "onvif控制操作")
public class OnvifPtzController {
    @RequestMapping("/ptz")
    public GHResponse ptz(PtzVo ptzVo) {
        OnvifManager onvifManager = new OnvifManager(new OnvifResponseListener() {
            @Override
            public void onResponse(@NotNull OnvifDevice onvifDevice, @NotNull OnvifResponse onvifResponse) {
                log.info(JSONUtil.toJsonStr(onvifResponse));
            }

            @Override
            public void onError(@NotNull OnvifDevice onvifDevice, int i, String s) {
            }
        });
        OnvifDevice device = new OnvifDevice(ptzVo.getIp());
        device.setUsername(ptzVo.getUsername());
        device.setPassword(ptzVo.getPassword());
        onvifManager.getStatus(device, ptzVo.getProfileToken(), new OnvifStatusListener() {
            @Override
            public void onStatusReceived(OnvifDevice onvifDevice, String s, OnvifStatus onvifStatus) {
                Double pan = onvifStatus.getPan();
                Double tilt = onvifStatus.getTilt();
                Double zoom = onvifStatus.getZoom();
                if ("left".equalsIgnoreCase(ptzVo.getPosition())) {
                    pan = pan + (-0.1);
                    if (pan <= -1) {
                        pan = -1.0;
                    }
                } else if ("right".equalsIgnoreCase(ptzVo.getPosition())) {
                    pan = pan + 0.1;
                    if (pan >= 1) {
                        pan = 1.0;
                    }
                } else if ("top".equalsIgnoreCase(ptzVo.getPosition())) {
                    tilt = tilt + 0.1;
                    if (tilt >= 1) {
                        tilt = 1.0;
                    }
                } else if ("down".equalsIgnoreCase(ptzVo.getPosition())) {

                    tilt = tilt + (-0.1);
                    if (tilt <= -1) {
                        tilt = -1.0;
                    }
                } else if ("zoomup".equalsIgnoreCase(ptzVo.getPosition())) {
                    zoom = zoom + 0.1;
                    if (zoom >= 1) {
                        zoom = 1.0;
                    }
                } else if ("zoomdown".equalsIgnoreCase(ptzVo.getPosition())) {
                    zoom = zoom - 0.1;
                    if (zoom <= 0) {
                        zoom = 0.0;
                    }
                }
                onvifManager.absoluteMove(device, ptzVo.getProfileToken(), ptzVo.getPan(), ptzVo.getTilt(), ptzVo.getZoom());
            }
        });

        return GHResponse.ok();

    }

    @PostMapping("/move")
    public GHResponse move(@RequestBody  PtzVo ptzVo) {
        OnvifManager onvifManager = new OnvifManager(new OnvifResponseListener() {
            @Override
            public void onResponse(@NotNull OnvifDevice onvifDevice, @NotNull OnvifResponse onvifResponse) {
                log.info(JSONUtil.toJsonStr(onvifResponse));
            }

            @Override
            public void onError(@NotNull OnvifDevice onvifDevice, int i, String s) {
            }
        });
        OnvifDevice device = new OnvifDevice(ptzVo.getIp());
        device.setUsername(ptzVo.getUsername());
        device.setPassword(ptzVo.getPassword());
        double pan=0.0,tilt=0.0,zoom=0.0;
        if ("left".equalsIgnoreCase(ptzVo.getPosition())) {
            pan = -0.5;
        } else if ("right".equalsIgnoreCase(ptzVo.getPosition())) {
            pan = 0.5;
        } else if ("top".equalsIgnoreCase(ptzVo.getPosition())) {
            tilt = 0.5;
        } else if ("down".equalsIgnoreCase(ptzVo.getPosition())) {
            tilt = -0.5;
        } else if ("zoomin".equalsIgnoreCase(ptzVo.getPosition())) {
            zoom = 0.5;
        } else if ("zoomout".equalsIgnoreCase(ptzVo.getPosition())) {
            zoom = -0.5;
        }
        onvifManager.continueMoveXY(device, ptzVo.getProfileToken(), pan, tilt, zoom);
        return GHResponse.ok();
    }


    @PostMapping("/stop")
    public GHResponse stop( @RequestBody PtzVo ptzVo) {
        OnvifManager onvifManager = new OnvifManager(new OnvifResponseListener() {
            @Override
            public void onResponse(@NotNull OnvifDevice onvifDevice, @NotNull OnvifResponse onvifResponse) {
                log.info(JSONUtil.toJsonStr(onvifResponse));
            }

            @Override
            public void onError(@NotNull OnvifDevice onvifDevice, int i, String s) {
            }
        });


        OnvifDevice device = new OnvifDevice(ptzVo.getIp());
        device.setUsername(ptzVo.getUsername());
        device.setPassword(ptzVo.getPassword());

        onvifManager.stopMove(device, ptzVo.getProfileToken());

        return GHResponse.ok();
    }

    @PostMapping("/go-preset")
    public GHResponse preset( @RequestBody PtzVo ptzVo) {
        OnvifManager onvifManager = new OnvifManager(new OnvifResponseListener() {
            @Override
            public void onResponse(@NotNull OnvifDevice onvifDevice, @NotNull OnvifResponse onvifResponse) {
                log.info(JSONUtil.toJsonStr(onvifResponse));
            }

            @Override
            public void onError(@NotNull OnvifDevice onvifDevice, int i, String s) {
                log.error(s);
            }
        });
        OnvifDevice device = new OnvifDevice(ptzVo.getIp());
        device.setUsername(ptzVo.getUsername());
        device.setPassword(ptzVo.getPassword());
        OnvifPreset preset = new OnvifPreset();
        preset.setName(ptzVo.getName());
        preset.setToken(ptzVo.getToken());
        preset.setPan(ptzVo.getPan());
        preset.setTilt(ptzVo.getTilt());
        preset.setZoom(ptzVo.getZoom());
        onvifManager.gotoPreset(device,ptzVo.getProfileToken(),preset);
        return GHResponse.ok();

    }
}
