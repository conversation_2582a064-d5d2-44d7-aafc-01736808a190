package com.gh.video.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import com.alibaba.excel.EasyExcel;
import com.gh.common.exception.ExceptionEnum;
import com.gh.common.exception.GhCustomException;
import com.gh.common.exception.SystemEnumMsg;
import com.gh.common.utils.GHResponse;
import com.gh.common.utils.PageResult;
import com.gh.log.annotation.OperLog;
import com.gh.video.listener.UploadVideoListener;
import com.gh.video.model.dto.CamDTO;
import com.gh.video.model.dto.DelCam;
import com.gh.video.model.dto.VideoDto;
import com.gh.video.model.entity.Video;
import com.gh.video.model.vo.AreaCamTreeVo;
import com.gh.video.model.vo.CamVo;
import com.gh.video.model.vo.VideoVo;
import com.gh.video.service.VideoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

@RequestMapping("/camera")
@RestController
@Api(tags = "摄像机管理接口")
@Slf4j
public class VideoController {

    @Autowired
    private VideoService videoService;

    @Autowired
    private UploadVideoListener listener;




    @PostMapping
    @ApiOperation("添加摄像机")
    @OperLog(category = "摄像机管理", description = "添加摄像机")
    @PreAuthorize("hasAnyAuthority('sys_admin','sys_project_admin','sys_video_add')")
    public GHResponse add(@RequestBody @Valid VideoVo videoVo) {

        Video video = buildVideoFromVo(videoVo);

        video.setCamToken( generateToken(video.getServerType()));

        boolean success = videoVo.getServerType() == 1 
            ? videoService.AddCam(video, null, null)
            : videoService.save(video);
            
        return success 
            ? GHResponse.ok(null, SystemEnumMsg.CREATE_SUCCESS.msg())
            : GHResponse.failed(SystemEnumMsg.CREATE_ERROR.msg());
    }

    @PutMapping
    @ApiOperation("更新摄像机")
    @OperLog(category = "摄像机管理", description = "更新摄像机")
    @PreAuthorize("hasAnyAuthority('sys_admin','sys_project_admin','sys_video_update')")
    public GHResponse update(@RequestBody VideoVo videoVo) {

        Video video = buildVideoFromVo(videoVo);
        boolean success = videoVo.getServerType() == 1 
            ? videoService.UpdateCam(video)
            : videoService.updateById(video);
            
        return success 
            ? GHResponse.ok()
            : GHResponse.failed(SystemEnumMsg.Update_ERROR.msg());
    }

    @DeleteMapping
    @ApiOperation("删除摄像机")
    @OperLog(category = "摄像机管理", description = "删除摄像机")
    @PreAuthorize("hasAnyAuthority('sys_admin','sys_project_admin','sys_video_del')")
    public GHResponse Delete(@RequestBody List<DelCam> delCams) {
        if (CollUtil.isNotEmpty(delCams)) {
            boolean delete = videoService.DelCam(delCams);
            if (delete) {
                return GHResponse.ok();
            }
        }

        return GHResponse.failed(SystemEnumMsg.Delete_ERROR.msg());
    }

    @GetMapping("{token}")
    @ApiOperation("查询摄像机")
    public GHResponse<CamDTO> Select(@PathVariable(value = "token") String token) {

        List<CamDTO> list = videoService.GetCamByToken(token);
        if (CollUtil.isNotEmpty(list)) {
            return GHResponse.ok(list.get(0));
        }
        return GHResponse.ok(null);
    }

    @GetMapping
    @ApiOperation("查询摄像机")
    public GHResponse<List<CamVo>> query(Integer serverId, Integer projectId, @RequestParam(required = false) Integer[] areaId, Integer menuId, String keyword, Integer page, Integer size) {
        PageResult<List<CamVo>> pageListResult = videoService.SelectCam(projectId, menuId, serverId, areaId, keyword,null, page, size);
        return GHResponse.ok(pageListResult.getData(), pageListResult.getTotal());
    }

    @PostMapping("/upload")
    @ApiOperation("批量导入")
    @OperLog(category = "摄像机管理", description = "批量导入")
    @PreAuthorize("hasAnyAuthority('sys_admin','sys_project_admin','sys_video_import')")
    public GHResponse Upload(@RequestParam("file") MultipartFile file) throws IOException {
        if (null == file || file.isEmpty()) {
            throw new GhCustomException(ExceptionEnum.FILE_EMPTY);
        }
        String extName = file.getOriginalFilename().split("\\.")[1];
        if (!"xls".equals(extName) && !"xlsx".equals(extName)) {
            throw new GhCustomException(ExceptionEnum.FILE_EXT_FORMAT);
        }
        EasyExcel.read(file.getInputStream(), VideoDto.class, listener).sheet().doRead();
        return GHResponse.ok();
    }


    @GetMapping("/area")
    @ApiOperation("查询区域列表里的摄像机")
    public GHResponse<List<AreaCamTreeVo>> GetAreaCams(Integer projectId) {
        List<AreaCamTreeVo> areaCamTreeVos = videoService.GetTreeCams(projectId);
        return GHResponse.ok(areaCamTreeVos);
    }

    @GetMapping("sync")
    @ApiOperation("同步摄像机到设备台账")
    public GHResponse uploadDeviceCam(Integer serverId, Integer productId, String mediaServerId, Integer serverType) {
        Boolean aBoolean = videoService.updateCamToDevice(serverId, productId, serverType, mediaServerId);
        if (aBoolean != null && aBoolean) {
            return GHResponse.ok();
        }
        return GHResponse.failed("同步失败");
    }


    @GetMapping("sync-h5")
    @ApiOperation("同步摄像机到流媒体")
    @OperLog(category = "摄像机管理", description = "同步摄像机到流媒体")
    @PreAuthorize("hasAnyAuthority('sys_admin','sys_project_admin','sys_video_toh5')")
    public GHResponse uploadDeviceCamH5(Integer projectId) {
        if (projectId == null) {
            return GHResponse.failed("项目ID不能为空");
        }
        try {
            // 获取摄像头列表
            PageResult<List<CamVo>> result = videoService.SelectCam(projectId, null, null, null, null, null, null, null);
            if (CollUtil.isEmpty(result.getData())) {
                return GHResponse.ok("没有需要同步的摄像头");
            }

            // 同步所有摄像头
            result.getData().forEach(this::syncCameraToH5);
            
            return GHResponse.ok("同步完成");
        } catch (GhCustomException e) {
            log.error("同步摄像头失败: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("同步摄像头发生未知错误", e);
            throw new GhCustomException(ExceptionEnum.VIDEO_IMPORT_ERROR);
        }
    }

    private Video buildVideoFromVo(VideoVo videoVo) {
        Video video = Video.builder().build();
        BeanUtil.copyProperties(videoVo, video);
        return video;
    }

    private String generateToken(Integer serverType) {
        return serverType == 2 
            ? "zlm_" + RandomUtil.randomString(6)
            : RandomUtil.randomString(6);
    }

    private void syncCameraToH5(CamVo camVo) {
        String baseUrl = StrUtil.format("http://{}:{}", camVo.getIp(), camVo.getPort());
        String requestUrl = buildH5RequestUrl(baseUrl, camVo);
        
        if (StrUtil.isEmpty(requestUrl)) {
            log.warn("摄像头[{}]URL构建失败，跳过同步", camVo.getStrName());
            return;
        }

        try {
            log.info("同步摄像头[{}], 请求地址: {}", camVo.getStrName(), requestUrl);
            HttpRequest.get(requestUrl)
                    .setReadTimeout(5000)
                    .setConnectionTimeout(3000)
                    .execute();
        } catch (Exception e) {
            log.error("同步摄像头[{}]失败: {}", camVo.getStrName(), e.getMessage());
            throw new GhCustomException(ExceptionEnum.VIDEO_IMPORT_ERROR);
        }
    }

    private String buildH5RequestUrl(String baseUrl, CamVo camVo) {
        String videoType = camVo.getVideoType().toUpperCase();   
        switch (videoType) {
            case "H5_ONVIF":
                return StrUtil.format("{}/api/v1/AddSrcONVIF?name={}&token={}&user={}&password={}&ip={}&port={}&session={}", 
                    baseUrl, 
                    camVo.getStrName(), 
                    camVo.getCamToken(), 
                    camVo.getStrUser(), 
                    camVo.getStrPasswd(),
                    camVo.getStrSrcIpAddress(), 
                    camVo.getStrSrcPort(),
                    camVo.getSession());
                
            case "H5_STREAM":
                return StrUtil.format("{}/api/v1/AddSrcRTSP?name={}&token={}&url={}&user={}&password={}", 
                    baseUrl, 
                    camVo.getStrName(), 
                    camVo.getCamToken(),
                    camVo.getStrUrl(), 
                    camVo.getStrUser(), 
                    camVo.getStrPasswd());
                
            case "H5_CH_DEVICE":
                return StrUtil.format("{}/api/v1/AddDeviceDh?name={}&token={}&user={}&password={}&ip={}&port={}", 
                    baseUrl, 
                    camVo.getStrName(), 
                    camVo.getCamToken(), 
                    camVo.getStrUser(), 
                    camVo.getStrPasswd(),
                    camVo.getStrSrcIpAddress(),
                    camVo.getStrSrcPort());
                
            default:
                log.warn("未知的视频类型: {}", videoType);
                return "";
        }
    }




}
