package com.gh.video.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.gh.video.config.XinYiVideoConfig;
import com.gh.video.model.entity.Video;
import com.gh.video.service.VideoService;
import com.gh.video.service.XYMediaServerService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Description:
 * User: zhangkeguang
 * Date: 2022-11-08-09:19
 */
@Service
@Slf4j
public class XYMediaServerServiceImpl implements XYMediaServerService {

    @Autowired
    private XinYiVideoConfig config;

    @Autowired
    private VideoService videoService;

    @Override
    public com.alibaba.fastjson.JSONObject  getGroupAndCamTree(String ip,String port,String token) {
        String url =config.getUrl();
        String access_token=config.getAccess_token();
        if(StrUtil.isNotEmpty(ip)&&StrUtil.isNotEmpty(port))
        {
            url = "http://"+ip+":"+port;
        }
        if(StrUtil.isNotEmpty(token))
        {
            access_token =token;
        }
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("access_token", access_token);
//        log.info("请求地址:"+url +"/api/channelinfo/getGroupAndChanneInfoListNew"+ "?" + URLUtil.buildQuery(paramMap, StandardCharsets.UTF_8));
        String body = HttpRequest.post(url +"/api/channelinfo/getGroupAndChanneInfoListNew"+ "?" + URLUtil.buildQuery(paramMap, StandardCharsets.UTF_8))
                .timeout(10000)
                .execute()
                .body();
        if(StrUtil.isNotEmpty(body))
        {
             return com.alibaba.fastjson.JSONObject.parseObject(body);

        }
        return null;
    }

    @Override
    public String getVideoUrl(String nodeno,String agreementType) {
        if(StrUtil.isEmpty(agreementType))
        {
            agreementType = "rtmp";
        }
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("access_token", config.getAccess_token());
        paramMap.put("agreementType", agreementType);
        paramMap.put("channo", nodeno);
        paramMap.put("remoteId",config.getRemoteId());
        paramMap.put("remoteName",config.getRemoteName());
        paramMap.put("remoteIP",config.getRemoteIP());

        String body = HttpRequest.post(config.getUrl()+"/api/videoStream/getVideoStreamUrl" + "?" + URLUtil.buildQuery(paramMap, StandardCharsets.UTF_8))
                .timeout(20000)
                .execute()
                .body();
        log.info("getVideoUrl:"+body);
        if(StrUtil.isNotEmpty(body))
        {
            JSONObject obj = JSONUtil.parseObj(body, true);
            if(null != obj)
            {
                return obj.getStr("data");
            }
        }

        return null;
    }

    @Override
    public boolean ptz(String nodeno, String direction, Integer start_flag) {
        if(null == start_flag)
        {
            start_flag = 1;
        }
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("access_token", config.getAccess_token());
        paramMap.put("start_flag", start_flag);
        paramMap.put("channo", nodeno);
        paramMap.put("remoteId",config.getRemoteId());
        paramMap.put("remoteName",config.getRemoteName());
        paramMap.put("remoteIP",config.getRemoteIP());
        paramMap.put("direction",direction);
        log.info("ptz 参数:"+URLUtil.buildQuery(paramMap, StandardCharsets.UTF_8));

        String body = HttpRequest.post(config.getUrl()+"/api/channelinfo/globeChannelControl" + "?" + URLUtil.buildQuery(paramMap, StandardCharsets.UTF_8))
                .execute()
                .body();
        log.info("ptz:"+body);
        if(StrUtil.isNotEmpty(body))
        {
            JSONObject obj = JSONUtil.parseObj(body, true);
            if(obj.getInt("code") == 200)
            {
                return true;
            }
        }

        return false;
    }

    @Override
    public boolean syncCamList(Integer projectId,String ip,String port,String token,Integer productId,Integer serverId,String groupId) {
        com.alibaba.fastjson.JSONObject tree = getGroupAndCamTree(ip,port,token);
        if(null != tree)
        {

            com.alibaba.fastjson.JSONArray array = tree.getJSONArray("data");
//            log.info("===========获取摄像头数据开始==========="+JSONUtil.toJsonStr(object));
            //循环遍历解析出摄像头信息 treeType GROUP为组织 CHANNEL为摄像头 children为子节点 层级不固定
//            parseTree(array,projectId,productId,serverId);
            parseSpecificGroupTree(array,projectId,productId,serverId,groupId);
            return true;
        }
        return false;
    }

    @Override
    public List<Video> getCamList() {
        com.alibaba.fastjson.JSONObject tree = getGroupAndCamTree(null,null,null);
        if(null != tree)
        {
            com.alibaba.fastjson.JSONArray array = tree.getJSONArray("data");
            //循环遍历解析出摄像头信息 treeType GROUP为组织 CHANNEL为摄像头 children为子节点 层级不固定
            List<Video> camList = CollUtil.newArrayList();
            parseTree(array,camList);
            return camList;
        }
        return  null;
    }

    private void parseTree(com.alibaba.fastjson.JSONArray array,Integer projectId,Integer productId,Integer serverId)
    {
       //返回所有摄像机 放到一个集合里面
        for (int i = 0; i < array.size(); i++) {
            com.alibaba.fastjson.JSONObject jsonObject = array.getJSONObject(i);
            String treeType = jsonObject.getString("treeType");
            if("CHANNEL".equals(treeType))
            {
                Video video = Video.builder()
                        .camToken(jsonObject.getString("nodeno"))
                        .strName(jsonObject.getString("label"))
                        .projectId(projectId)
                        .productId(productId)
                        .serverType(3)
                        .serverId(serverId)
                        .ptz(jsonObject.getInteger("nodeCategory") == 1 ? true : false)
                        .ptzEnable(jsonObject.getInteger("nodeCategory") == 1 ? true : false)
                        .status(jsonObject.getInteger("isUp") == 1 ? true : false)
                        .build();
                videoService.saveOrUpdate(video, Wrappers.<Video>lambdaQuery().eq(Video::getCamToken,video.getCamToken()));
            }else
            {
                com.alibaba.fastjson.JSONArray children = jsonObject.getJSONArray("children");
                if(CollUtil.isNotEmpty(children))
                {
                    parseTree(children,projectId,productId,serverId);
                }
            }
        }
    }

    private void parseTree(com.alibaba.fastjson.JSONArray array, List<Video> camList)
    {
        //返回所有摄像机 放到一个集合里面
        for (int i = 0; i < array.size(); i++) {
            com.alibaba.fastjson.JSONObject jsonObject = array.getJSONObject(i);
            String treeType = jsonObject.getString("treeType");
            if("CHANNEL".equals(treeType))
            {
                Video video = Video.builder()
                        .camToken(jsonObject.getString("nodeno"))
                        .strName(jsonObject.getString("label"))
                        .ptz(jsonObject.getInteger("nodeCategory") == 1 ? true : false)
                        .status(jsonObject.getInteger("isUp") == 1 ? true : false)
                        .build();
                camList.add(video);
            }else
            {
                com.alibaba.fastjson.JSONArray children = jsonObject.getJSONArray("children");
                if(CollUtil.isNotEmpty(children))
                {
                    parseTree(children,camList);
                }
            }
        }

    }



    /**
     * 获取指定 GROUP 节点下的所有摄像机数据
     * @param array 原始数据数组
     * @param projectId 项目ID
     * @param productId 产品ID
     * @param serverId 服务器ID
     * @param targetGroupId 目标GROUP节点ID
     */
    private void parseSpecificGroupTree(com.alibaba.fastjson.JSONArray array, Integer projectId, Integer productId, Integer serverId, String targetGroupId) {
        for (int i = 0; i < array.size(); i++) {
            com.alibaba.fastjson.JSONObject jsonObject = array.getJSONObject(i);
            String treeType = jsonObject.getString("treeType");
            String id = jsonObject.getString("id");
            
            if ("GROUP".equals(treeType) && targetGroupId.equals(id)) {
                com.alibaba.fastjson.JSONArray children = jsonObject.getJSONArray("children");
                if (CollUtil.isNotEmpty(children)) {
                    parseTree(children, projectId, productId, serverId);
                }
            } else if ("GROUP".equals(treeType)) {
                com.alibaba.fastjson.JSONArray children = jsonObject.getJSONArray("children");
                if (CollUtil.isNotEmpty(children)) {
                    parseSpecificGroupTree(children, projectId, productId, serverId, targetGroupId);
                }
            }
        }
    }



}
