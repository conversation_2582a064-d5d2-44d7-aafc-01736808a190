package com.gh.video.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gh.common.utils.PageResult;
import com.gh.video.mapper.LineMapper;
import com.gh.video.model.entity.Line;
import com.gh.video.model.vo.LineVO;
import com.gh.video.service.LineService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * Description:
 * User: zhangkeguang
 * Date: 2023-05-24-16:51
 */
@Service
public class LineServiceImpl extends ServiceImpl<LineMapper, Line> implements LineService {

    @Autowired
    private LineMapper lineMapper;
    @Override
    public PageResult<List<LineVO>> getLine(String keyword, Integer projectId, Integer page, Integer size) {

        if(StrUtil.isNotEmpty(keyword))
        {
            keyword="%"+keyword+"%";
        }
        Page page1=null;
        if(PageResult.isPage(page,size))
        {
            page1=new Page(page,size);
        }
        List<LineVO> line = lineMapper.getLine(page1, keyword, projectId);
        return PageResult.<List<LineVO>>builder()
                .data(line)
                .total(page1==null?line.size():page1.getTotal())
                .build();

    }
}

