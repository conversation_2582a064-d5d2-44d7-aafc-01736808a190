package com.gh.video.onvif;

import be.teletask.onvif.OnvifManager;
import be.teletask.onvif.listeners.OnvifResponseListener;
import be.teletask.onvif.models.OnvifDevice;
import be.teletask.onvif.responses.OnvifResponse;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.nacos.client.utils.IPUtil;
import com.gh.video.model.entity.Video;
import com.gh.video.service.VideoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;

import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;

/**
 * Description:
 * User: zhangkeguang
 * Date: 2022-11-09-10:41
 */

//@Component
@Slf4j
public class OnvifStatusTask {

    @Autowired
    private VideoService videoService;

    /**
     * 通过onvif协议更新摄像机状态和profileToken(ptz控制使用)
     */
    @Scheduled(fixedRate =5000 )
    public void createPatrolTask() {
        List<Video> videos = videoService.lambdaQuery().eq(Video::getServerType, 2)
                .eq(Video::getEnable, true)
                .list();
        if(CollUtil.isNotEmpty(videos))
        {
            OnvifManager   onvifManager = new OnvifManager(new OnvifResponseListener() {
                @Override
                public void onResponse(@NotNull OnvifDevice onvifDevice, @NotNull OnvifResponse onvifResponse) {
                    log.error(JSONUtil.toJsonStr(onvifResponse));
                }
                @Override
                public void onError(@NotNull OnvifDevice onvifDevice, int i, String s) {
                }
            });

            for (Video video:videos)
            {
                if(IPUtil.isIPV4(video.getStrSrcIpAddress())&& StrUtil.isNotEmpty(video.getStrUser())&&
                StrUtil.isNotEmpty(video.getStrPasswd()))
                {
                    OnvifDevice device = new OnvifDevice(video.getStrSrcIpAddress());
                    device.setUsername(video.getStrUser());
                    device.setPassword(video.getStrPasswd());
                    device.setAddresses(Arrays.asList(video.getId().toString()));
                    onvifManager.getDeviceInformation(device, (onvifDevice, onvifDeviceInformation) -> {
                         log.info(JSONUtil.toJsonStr(onvifDeviceInformation));
                         if(null!=onvifDeviceInformation && StrUtil.isNotEmpty(onvifDeviceInformation.getSerialNumber()))
                         {
                             String id = onvifDevice.getAddresses().get(0);
                             videoService.lambdaUpdate().set(Video::getStatus,true)
                                     .eq(Video::getId, Convert.toInt(id,0))
                                     .update();
                         }
                    });

                    if(video.getPtzEnable()!=null&&video.getPtzEnable()&&StrUtil.isEmpty(video.getProfileToken()))
                    {
                        onvifManager.getMediaProfiles(device, (onvifDevice, list) -> {
                            String token = list.get(0).getToken();
                            String id = onvifDevice.getAddresses().get(0);
                            videoService.lambdaUpdate().set(Video::getProfileToken,token)
                                    .eq(Video::getId, Convert.toInt(id,0))
                                    .update();

                            //查询预置位
                            onvifManager.getPresets(device, token, (presetDevice, s, presets) -> {
                                videoService.lambdaUpdate().set(Video::getPresets,JSONUtil.toJsonStr(presets))
                                        .eq(Video::getId, Convert.toInt(id,0))
                                        .update();
                            });
                        });
                    }
                }
            }
        }
    }


}
