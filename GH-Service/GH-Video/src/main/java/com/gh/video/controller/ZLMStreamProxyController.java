//package com.gh.video.controller;
//
//import cn.hutool.core.util.ArrayUtil;
//import com.gh.common.exception.SystemEnumMsg;
//import com.gh.common.utils.GHResponse;
//import com.gh.video.model.entity.StreamProxyItem;
//import com.gh.video.service.StreamProxyService;
//import io.swagger.annotations.Api;
//import io.swagger.annotations.ApiOperation;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.security.access.prepost.PreAuthorize;
//import org.springframework.web.bind.annotation.*;
//
//import javax.validation.Valid;
//import java.util.Arrays;
//
//@RequestMapping("/stream-proxy")
//@RestController
//@Api(tags = "摄像机管理接口")
//@Slf4j
//public class ZLMStreamProxyController {
//
//    @Autowired
//    private StreamProxyService streamProxyService;
//
//
//
//
//    @PostMapping
//    @ApiOperation("添加摄像机")
//    @PreAuthorize("hasAnyAuthority('sys_admin','sys_project_admin','sys_video_add')")
//    public GHResponse Add(@RequestBody @Valid StreamProxyItem videoVo) {
//
//        boolean insert = streamProxyService.save(videoVo);
//        if (insert) {
//            return GHResponse.ok(null, SystemEnumMsg.CREATE_SUCCESS.msg());
//        }
//        return GHResponse.failed(SystemEnumMsg.CREATE_ERROR.msg());
//    }
//
//
//    @PutMapping
//    @ApiOperation("更新摄像机")
//    @PreAuthorize("hasAnyAuthority('sys_admin','sys_project_admin','sys_video_update')")
//    public GHResponse Patch(@RequestBody StreamProxyItem videoVo) {
//
//        boolean update = streamProxyService.updateById(videoVo);
//        if (update) {
//            return GHResponse.ok();
//        }
//
//        return GHResponse.failed(SystemEnumMsg.Update_ERROR.msg());
//    }
//
//    @DeleteMapping
//    @ApiOperation("删除摄像机")
//    @PreAuthorize("hasAnyAuthority('sys_admin','sys_project_admin','sys_video_del')")
//    public GHResponse Delete(@RequestParam Integer[] ids) {
//        if(ArrayUtil.isNotEmpty(ids))
//        {
//            boolean delete = streamProxyService.removeByIds(Arrays.asList(ids));
//            if(delete)
//            {
//                return GHResponse.ok();
//            }
//        }
//
//        return GHResponse.failed(SystemEnumMsg.Delete_ERROR.msg());
//    }
//
//
//
//
////
////    @GetMapping("/area")
////    @ApiOperation("查询区域列表里的摄像机")
////    public GHResponse<List<AreaCamTreeVo>> GetAreaCams(Integer projectId) {
////        List<AreaCamTreeVo> areaCamTreeVos = videoService.GetTreeCams(projectId);
////        return GHResponse.ok(areaCamTreeVos);
////    }
//
//
//
//
//
//}
