package com.gh.video.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gh.common.utils.PageResult;
import com.gh.video.mapper.ServerMapper;
import com.gh.video.model.dto.H5Cam;
import com.gh.video.model.dto.H5SrcResponse;
import com.gh.video.model.entity.Server;
import com.gh.video.model.entity.Video;
import com.gh.video.service.H5Service;
import com.gh.video.service.ServerService;
import com.gh.video.service.VideoService;
import com.gh.video.service.XYMediaServerService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
public class ServerServiceImpl extends ServiceImpl<ServerMapper, Server> implements ServerService {

    @Autowired
    private H5Service h5Service;
    @Autowired
    private VideoService videoService;

    @Autowired
    private XYMediaServerService xyMediaServerService;

    private static final int H5_SERVER_TYPE = 1;
    private static final int XY_SERVER_TYPE = 3;

    @Override
    public PageResult<List<Server>> select(Integer id, Integer projectId, String keyword, String ip, Integer page,
            Integer size) {
        Page<Server> serverPage = lambdaQuery().eq(null != id, Server::getId, id)
                .eq(null != projectId, Server::getProjectId, projectId)
                .and(StrUtil.isNotBlank(keyword),
                        query -> query.like(Server::getServerName, keyword).or().like(Server::getIp, keyword))
                .eq(StrUtil.isNotBlank(ip), Server::getIp, ip)
                .page(PageResult.isPage(page, size) ? new Page<>(page, size) : new Page<>(1, Integer.MAX_VALUE));
        return PageResult.<List<Server>>builder().total(serverPage.getTotal())
                .data(serverPage.getRecords()).build();

    }

    @Override
    public boolean UpdateFromRemoteServer(Integer serverId, Integer productId) {
       
        Server server = getById(serverId);
        if (server == null) {
            log.error("serverId:{}不存在", serverId);
            return false;
        }

        if (server.getServerType() == H5_SERVER_TYPE) {
            syncH5Cameras(server, productId);
        } else if (server.getServerType() == XY_SERVER_TYPE) {
            // 信义流媒体
            syncXYServer(server, productId,server.getSecret());
        }

        return true;
    }



    private boolean syncH5Cameras(Server server, Integer productId) {

        H5SrcResponse response = h5Service.GetSrc(server);
        if (response == null || CollUtil.isEmpty(response.getSrc())) {
            log.info("未发现摄像头数据: serverId={}, serverName={}", server.getId(), server.getServerName());
            return true;
        }

        List<H5Cam> allCameras = response.getSrc();
        int totalCount = allCameras.size();
        log.info("开始同步摄像头数据: serverId={}, total={}", server.getId(), totalCount);

        for (H5Cam h5Cam : allCameras) {
            try {
                
                processSingleCamera(h5Cam, server, productId);

            } catch (Exception e) {
                log.error("同步摄像头数据失败: serverId={}, error={}", server.getId(), e.getMessage());
                return false;
            }
        }
        log.info("摄像头数据同步完成: serverId={}, total={}", server.getId(), totalCount);
        return true;

    }



    private boolean processSingleCamera(H5Cam cam, Server server, Integer productId) {
        try {
            Video video = buildVideo(cam, server, productId);
            saveOrUpdateVideo(video, server.getProjectId());
            return true;
        } catch (Exception e) {
            log.error("保存摄像头数据失败: camera={}, error={}", cam.getStrToken(), e.getMessage());
            return false;
        }
    }

    private Video buildVideo(H5Cam cam, Server server, Integer productId) {
        return Video.builder()
                .videoType(cam.getNType())
                .camToken(cam.getStrToken())
                .strUrl(cam.getStrUrl())
                .serverId(server.getId())
                .projectId(server.getProjectId())
                .productId(productId)
                .serverType(H5_SERVER_TYPE)
                .build();
    }

    private void saveOrUpdateVideo(Video video, Integer projectId) {
        videoService.saveOrUpdate(video,
                Wrappers.<Video>lambdaQuery()
                        .eq(Video::getCamToken, video.getCamToken())
                        .eq(Video::getProjectId, projectId));
    }

    private boolean syncXYServer(Server server, Integer productId,String groupId) {
        try {
            xyMediaServerService.syncCamList(
                    server.getProjectId(),
                    server.getIp(),
                    server.getPort(),
                    null,
                    productId,
                    server.getId(),groupId);
            return true;
        } catch (Exception e) {
            log.error("同步信义服务器失败: serverId={}, error={}",
                    server.getId(), e.getMessage(), e);
            return false;
        }
    }

}
