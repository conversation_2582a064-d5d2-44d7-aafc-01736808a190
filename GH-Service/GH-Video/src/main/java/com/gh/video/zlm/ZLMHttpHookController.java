package com.gh.video.zlm;

import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.HMac;
import cn.hutool.crypto.digest.HmacAlgorithm;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.gh.video.model.entity.Server;
import com.gh.video.model.entity.Video;
import com.gh.video.service.ServerService;
import com.gh.video.service.StreamProxyService;
import com.gh.video.service.VideoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

/**
 * Description:
 * User: zhangkeguang
 * Date: 2022-11-08-08:59
 */
@RestController
@RequestMapping("/index/hook")
@Slf4j
public class ZLMHttpHookController {

    @Autowired
    private ServerService mediaServerService;

    @Autowired
    private VideoService videoService;

    @Autowired
    private StreamProxyService streamProxyService;

    @Value("${zlm.auth:false}")
    private boolean auth;

    @ResponseBody
    @PostMapping(value = "/on_stream_not_found", produces = "application/json;charset=UTF-8")
    public JSONObject onStreamNotFound(@RequestBody JSONObject json) {
        JSONObject ret = new JSONObject();
        ret.put("code", 0);
        ret.put("msg", "success");

        log.info("[ ZLM HOOK ]on_stream_not_found API调用，参数：" + json.toString());
        String mediaServerId = json.getString("mediaServerId");
        Server mediaInfo = mediaServerService.lambdaQuery().eq(Server::getMediaServerId, mediaServerId).one();
        if (mediaInfo != null) {
            String app = json.getString("app");
            String streamId = json.getString("stream");
            // 拉流代理
            Video video = videoService.lambdaQuery().eq(Video::getApp, app)
                    .eq(Video::getCamToken, streamId).one();
            if(video.getPlayType()==2)//推流不做处理
            {
                ret.put("code", -1);
                ret.put("msg", "fail");
                return ret;
            }
            if (video != null && video.getEnable()) {
                streamProxyService.start(video,mediaInfo);
            }
        }
        return ret;
    }


    @ResponseBody
    @PostMapping(value = "/on_stream_none_reader", produces = "application/json;charset=UTF-8")
    public JSONObject onStreamNoneReader(@RequestBody JSONObject json) {

        log.info("[ ZLM HOOK ]on_stream_none_reader API调用，参数：" + json.toString());
        String mediaServerId = json.getString("mediaServerId");
        String streamId = json.getString("stream");
        String app = json.getString("app");
        JSONObject ret = new JSONObject();
        ret.put("code", 0);

        Video streamProxyItem = videoService.lambdaQuery().eq(Video::getApp, app)
                .eq(Video::getCamToken, streamId).one();
        if (streamProxyItem != null) {

            // 无人观看停用
            ret.put("close", true);
            // 修改数据
            streamProxyService.stop(app, streamId);
            return ret;
        }
        // 推流具有主动性，暂时不做处理
//			StreamPushItem streamPushItem = streamPushService.getPush(app, streamId);
//			if (streamPushItem != null) {
//				// TODO 发送停止
//
//			}

        return ret;
    }


    @ResponseBody
    @PostMapping(value = "/on_server_started", produces = "application/json;charset=UTF-8")
    public JSONObject onServerStarted(HttpServletRequest request, @RequestBody JSONObject jsonObject) {

        log.info("[ ZLM HOOK ]on_server_started API调用，参数：" + jsonObject.toString());

        String remoteAddr = request.getRemoteAddr();
        jsonObject.put("ip", remoteAddr);
//        List<ZlmHttpHookSubscribe.Event> subscribes = this.subscribe.getSubscribes(HookType.on_server_started);
//        if (subscribes != null  && subscribes.size() > 0) {
//            for (ZlmHttpHookSubscribe.Event subscribe : subscribes) {
//                subscribe.response(null, jsonObject);
//            }
//        }
//
//        ZLMServerConfig zlmServerConfig = JSONObject.toJavaObject(jsonObject, ZLMServerConfig.class);
//        if (zlmServerConfig !=null ) {
//            mediaServerService.zlmServerOnline(zlmServerConfig);
//        }
        JSONObject ret = new JSONObject();
        ret.put("code", 0);
        ret.put("msg", "success");
        return ret;
    }

    @ResponseBody
    @PostMapping(value = "/on_server_keepalive", produces = "application/json;charset=UTF-8")
    public JSONObject onServerKeepalive(@RequestBody JSONObject json) {

//        log.info("[ ZLM HOOK ]on_server_keepalive API调用，参数：" + json.toString());
        String mediaServerId = json.getString("mediaServerId");
//        List<ZlmHttpHookSubscribe.Event> subscribes = this.subscribe.getSubscribes(HookType.on_server_keepalive);
//        if (subscribes != null  && subscribes.size() > 0) {
//            for (ZlmHttpHookSubscribe.Event subscribe : subscribes) {
//                subscribe.response(null, json);
//            }
//        }
//        mediaServerService.updateMediaServerKeepalive(mediaServerId, json.getJSONObject("data"));

        JSONObject ret = new JSONObject();
        ret.put("code", 0);
        ret.put("msg", "success");

        return ret;
    }

    /**
     * rtsp专用的鉴权事件，先触发on_rtsp_realm事件然后才会触发on_rtsp_auth事件。
     *
     */
    @ResponseBody
    @PostMapping(value = "/on_rtsp_realm", produces = "application/json;charset=UTF-8")
    public JSONObject onRtspRealm(@RequestBody JSONObject json){
        log.info("[ ZLM HOOK ]on_rtsp_realm API调用，参数：" + json.toString());
        JSONObject ret = new JSONObject();
        ret.put("code", 0);
        ret.put("realm", "");
        return ret;
    }

    @ResponseBody
    @PostMapping(value = "/on_rtsp_auth", produces = "application/json;charset=UTF-8")
    public JSONObject onRtspAuth(@RequestBody JSONObject json){

        log.info("[ ZLM HOOK ]on_rtsp_auth API调用，参数：" + json.toString());

        String mediaServerId = json.getString("mediaServerId");
        JSONObject ret = new JSONObject();
        ret.put("code", 0);
        ret.put("encrypted", false);
        ret.put("passwd", "test");
        return ret;
    }

    @ResponseBody
    @PostMapping(value = "/on_play", produces = "application/json;charset=UTF-8")
    public JSONObject onPlay(@RequestBody OnPlayHookParam param){

        log.info("[ ZLM HOOK ]on_play API调用，参数：" + JSON.toJSONString(param));
        JSONObject ret = new JSONObject();
        ret.put("code", 0);
        ret.put("msg", "success");

        if(auth)//开启校验
        {
            String params = param.getParams();
            if(StrUtil.isNotEmpty(params)&&params.contains("="))
            {
                String[] keys = params.split("=");
                if(keys.length==2)
                {
                    String stream =param.getStream();
                    byte[] key = "zlm".getBytes();
                    HMac mac = new HMac(HmacAlgorithm.HmacMD5, key);
                    String macHex = mac.digestHex(stream);
                    if(macHex.equalsIgnoreCase(keys[1]))
                    {
                        ret.put("code", 0);
                    }else
                    {
                        ret.put("code", -1);
                        ret.put("msg", "鉴权失败");
                    }
                }

            }
        }
        return ret;
    }

    @ResponseBody
    @PostMapping(value = "/on_stream_changed", produces = "application/json;charset=UTF-8")
    public JSONObject onStreamChanged(@RequestBody MediaItem item){
        log.info("[ ZLM HOOK ]on_stream_changed API调用，参数：" + JSONObject.toJSONString(item));
        JSONObject ret = new JSONObject();
        ret.put("code", 0);
        ret.put("msg", "success");
        return ret;
    }



}
