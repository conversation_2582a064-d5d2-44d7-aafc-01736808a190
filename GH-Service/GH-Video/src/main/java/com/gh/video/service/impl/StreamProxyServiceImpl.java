package com.gh.video.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.gh.video.model.entity.Server;
import com.gh.video.model.entity.Video;
import com.gh.video.service.ServerService;
import com.gh.video.service.StreamProxyService;
import com.gh.video.service.VideoService;
import com.gh.video.service.ZLMMediaServerService;
import com.gh.video.zlm.ZLMRESTfulUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Description:
 * User: zhangkeguang
 * Date: 2022-11-08-09:19
 */
@Service
@Slf4j
public class StreamProxyServiceImpl implements StreamProxyService {
    @Autowired
    private ZLMRESTfulUtils zlmresTfulUtils;

    @Autowired
    private ZLMMediaServerService mediaServerService;


    @Autowired
    private VideoService videoService;

    @Autowired

    private ServerService serverService;


    @Override
    public boolean start(Video video,Server server) {
        boolean result = false;
        JSONObject jsonObject = addStreamProxyToZlm(video,server);
        if (jsonObject == null) {
            return false;
        }
        if (jsonObject.getInteger("code") == 0) {
            result = true;
        }
        return result;
    }

    @Override
    public JSONObject addStreamProxyToZlm(Video param,Server mediaServerItem) {
       JSONObject result = zlmresTfulUtils.addStreamProxy(mediaServerItem, param.getApp(), param.getCamToken(), param.getStrUrl(),
                true, true, "tcp");
        return result;
    }


    @Override
    public void del(String app, String stream) {

        Video streamProxyItem = videoService.lambdaQuery().eq(Video::getApp, app)
                .eq(Video::getCamToken, stream).one();
        if (streamProxyItem != null) {
            JSONObject jsonObject = removeStreamProxyFromZlm(streamProxyItem);
            if (jsonObject != null && jsonObject.getInteger("code") == 0) {
                // 如果关联了国标那么移除关联
//                gbStreamMapper.del(app, stream);
                // platformGbStreamMapper.delByAppAndStream(app, stream);
                // TODO 如果关联的推流， 那么状态设置为离线
            }
            //redisCatchStorage.removeStream(streamProxyItem.getMediaServerId(), "PULL", app, stream);
        }

    }

    @Override
    public JSONObject removeStreamProxyFromZlm(Video param) {
        if (param == null) {
            return null;
        }
        Server mediaServerItem = serverService.lambdaQuery().eq(Server::getMediaServerId, param.getMediaServerId()).one();
        JSONObject result = zlmresTfulUtils.closeStreams(mediaServerItem, param.getApp(), param.getCamToken());
        return result;
    }


    @Override
    public boolean stop(String app, String stream) {
        boolean result = false;
        Video streamProxyDto = videoService.lambdaQuery().eq(Video::getApp, app)
                .eq(Video::getCamToken, stream).one();
        if (streamProxyDto != null && streamProxyDto.getEnable()) {
            JSONObject jsonObject = removeStreamProxyFromZlm(streamProxyDto);
            if (jsonObject != null && jsonObject.getInteger("code") == 0) {
                return true;
            }
        }
        return result;
    }


//    private List<StreamProxyTreeVo> GetParentTreeNode(List<Area> areas, Integer parentId, Integer projectId, List<StreamProxyItem> items) {
//        List<StreamProxyTreeVo> areaList = areas.stream().filter(area -> area.getParentId().equals(parentId))
//                .map(area -> {
//                            StreamProxyTreeVo areaCamTreeVo = StreamProxyTreeVo.builder().id(area.getId()).name(area.getName())
//                                    .isCam(false).parentId(area.getParentId()).build();
//                            List<StreamProxyItem> data = items.stream().filter(streamProxyItem -> streamProxyItem.getAreaId().equals(area.getId()))
//                                    .collect(Collectors.toList());
//                            List<StreamProxyTreeVo> cams = data.stream().map(d ->
//                                    StreamProxyTreeVo.builder().name(d)
//                                            .app(d.getApp())
//                                            .streamId(d.getStream())
//                                            .isCam(true)
//                                            .mediaIp(d.getMediaServerIP())
//                                            .mediaPort(d.getMediaServerPort()).build()
//                            ).collect(Collectors.toList());
//                            cams.addAll(GetParentTreeNode(areas, area.getId(), projectId, items));
//                            areaCamTreeVo.setChildren(cams);
//                            return areaCamTreeVo;
//                        }
//                ).collect(Collectors.toList());
//        return areaList;
//    }

}
