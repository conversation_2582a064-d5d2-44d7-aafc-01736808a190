package com.gh.video.xinyi;

import com.gh.common.utils.GHResponse;
import com.gh.video.service.XYMediaServerService;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * Description:信义视频接口
 * User: zhangkeguang
 * Date: 2024-12-31
 */
@RequestMapping("/xy")
@Slf4j
@RestController
@AllArgsConstructor
public class XYManagerController {

    @Autowired
    private XYMediaServerService xyManagerService;

    @GetMapping(value = "tree", produces = "application/json;charset=UTF-8")
    public GHResponse onStreamNotFound() {

        com.alibaba.fastjson.JSONObject tree = xyManagerService.getGroupAndCamTree(null,null,null);
        return GHResponse.ok(tree.getJSONArray("data"));
    }


    @ApiOperation(value = "同步信义视频设备树到本地")
    @GetMapping("sync")
    public GHResponse sync(@RequestParam Integer projectId) {
        boolean camList = xyManagerService.syncCamList(projectId,null,null,null,null,null,null);
        if(camList)
        {
            return GHResponse.ok();
        }
        return GHResponse.failed();
    }

    @ApiOperation(value = "云台控制")
    @PostMapping("control")
    public GHResponse ptz(@RequestParam String direction, @RequestParam String nodeno, @RequestParam Integer start_flag) {
        boolean success = xyManagerService.ptz(nodeno,direction,start_flag);
        if(success)
        {
            return GHResponse.ok();
        }
        return GHResponse.failed();
    }

    @ApiOperation(value = "获取视频播放url")
    @ResponseBody
    @GetMapping("url")
    public GHResponse getVideoUrl(@RequestParam String nodeno, @RequestParam String agreementType) {
        String url = xyManagerService.getVideoUrl(nodeno,agreementType);

            return GHResponse.ok(url);

    }



}
