package com.gh.video.config;

import cn.hutool.core.util.StrUtil;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

//@Configuration
public class KafkaConfig implements InitializingBean {
    @Value("${kafka.topic}")
    private String topic;

    @Override
    public void afterPropertiesSet() throws Exception {
        if(StrUtil.isNotBlank(topic))
        {
            System.setProperty("topic", topic);
        }else
        {
            System.setProperty("topic","topic");
        }
    }
}
