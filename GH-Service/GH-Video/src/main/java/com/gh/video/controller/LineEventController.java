package com.gh.video.controller;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gh.common.exception.SystemEnumMsg;
import com.gh.common.utils.GHResponse;
import com.gh.common.utils.PageResult;
import com.gh.log.annotation.OperLog;
import com.gh.video.model.entity.LineEvent;
import com.gh.video.service.LineEventService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@RequestMapping("/event")
@RestController
@Api(tags = "视频线路事件")
@Slf4j
public class LineEventController {

    @Autowired
    private LineEventService service;
    private static final int DEFAULT_PAGE_SIZE = 1000;


    @PostMapping
    @ApiOperation("添加线路")
    @OperLog(category = "视频线路管理", description = "添加线路")
    public GHResponse addEvent(@RequestBody @Valid LineEvent event) {
       return service.save(event) 
       ? GHResponse.ok(null, SystemEnumMsg.CREATE_SUCCESS.msg())
       : GHResponse.failed(SystemEnumMsg.CREATE_ERROR.msg());
    }


    @GetMapping
    @ApiOperation("查询事件")
    public GHResponse<List<LineEvent>> getEvents(Integer projectId, String keyword, Integer page, Integer size) {
        Page<LineEvent> eventPage = service.lambdaQuery().eq(projectId != null, LineEvent::getProjectId, projectId)
                .like(StrUtil.isNotEmpty(keyword), LineEvent::getName, keyword)
                .orderByDesc(LineEvent::getCreateTime)
                .page(PageResult.isPage(page, size)
                        ? new Page<LineEvent>(page, size)
                        : new Page<LineEvent>(1, DEFAULT_PAGE_SIZE));
        
        return GHResponse.ok(eventPage.getRecords(),eventPage.getTotal());

    }


}
