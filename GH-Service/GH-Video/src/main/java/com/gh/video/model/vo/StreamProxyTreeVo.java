package com.gh.video.model.vo;

import lombok.Builder;
import lombok.Data;

import java.util.List;

@Data
@Builder
public class StreamProxyTreeVo {
    private Integer id;

    private String name;

    private String app;

    private String streamId;

    private String mediaIp;

    private Integer mediaPort;

    private Integer parentId;

    private boolean isCam;



    private List<StreamProxyTreeVo> children;
}
