package com.gh.video.service;

import com.gh.video.model.dto.H5SrcResponse;
import com.gh.video.model.dto.RunInfo;
import com.gh.video.model.entity.Server;
import com.gh.video.model.entity.Video;

public interface H5Service {
     Boolean OnlyDelH5(Server server, String token);
     Boolean AddH5Cam(Video video,String ip,String port,String session);
     RunInfo GetRunInfo(String ip ,Integer port);
     H5SrcResponse GetSrc(Server server);
}
