spring:
  application:
    name: video-service
  servlet:
    multipart:
      max-file-size: 50MB
  mvc:
    servlet:
      load-on-startup: 1
  cloud:
    nacos:
      server-addr: ${host:*************:28848}
      config:
        file-extension: yaml
        namespace: 9027158d-ab5d-43f7-a691-3b7546cc79f9
#        namespace: 734f1747-1d86-4f76-9cef-91fc7ea262b6
        extension-configs:
          - data-id: mysql.yml
          - data-id: redis.yml
          - data-id: json.yml
          - data-id: feign.yml
          - data-id: swagger.yml
#          - data-id: admin.yml
#          - data-id: zipkin.yml
      discovery:
        server-addr: ${host:*************:28848}
        namespace: 9027158d-ab5d-43f7-a691-3b7546cc79f9
server:
  port: 9003

#
#h5ss:
#  url: http://127.0.0.1:2500


zlm:
  auth: false

logging:
  level:
    com.gh.video.mapper: debug


xinyi:
    url: http://
    client_id:
    access_token:
    remoteId:
    remoteName:
    remoteIP:
feign:
  client:
    config:
        default:
            connect-timeout: 10000
            read-timeout: 10000



