<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gh.video.mapper.LineMapper">


    <select id="getCam" resultType="com.gh.video.model.vo.CamVo">
        select ghvideo_cam.camToken,ghvideo_cam.strName,ip,port from ghvideo_linecam
        left join ghvideo_cam on ghvideo_linecam.camToken=ghvideo_cam.camToken
        LEFT JOIN ghvideo_server on ghvideo_cam.serverId=ghvideo_server.id
        where lineId=#{id}
    </select>
    <resultMap id="line" type="com.gh.video.model.vo.LineVO" autoMapping="true">
        <id column="id" property="id"></id>
        <collection property="cams" select="getCam" ofType="com.gh.video.model.vo.CamVo" column="id">

        </collection>
    </resultMap>

    <select id="getLine" resultMap="line">
        select  * from ghvideo_line
        <where>
            <if test="projectId!=null">
                and ghvideo_line.projectId=#{projectId}
            </if>

            <if test="keyword!=null and keyword !=''">
                and ghvideo_line.name like #{keyword}
            </if>
        </where>
    </select>






</mapper>
