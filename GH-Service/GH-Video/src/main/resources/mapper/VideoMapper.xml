<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gh.video.mapper.VideoMapper">

    <sql id="Base_Cam_Columns">
        c.ptzEnable, c.playType, c.profileToken, c.app, c.serverType, 
        c.id, c.camToken, c.serverId, c.areaId, c.strName, c.strUrl, 
        c.strUser, c.strPasswd, c.strSrcIpAddress, c.strSrcPort, 
        c.nType as videoType, c.enable, c.lating, c.ptz, c.menuId, 
        c.productId, c.projectId, c.status,c.bOnline
    </sql>
    <sql id="Server_Columns">
        s.serverName, s.ip, s.port, s.session
    </sql>

    <select id="GetCam" resultType="com.gh.video.model.vo.CamVo" >
        SELECT 
            <include refid="Base_Cam_Columns"/>,
            <include refid="Server_Columns"/>
        FROM GHVideo_Cam c
        LEFT JOIN GHVideo_Server s ON (
            c.mediaServerId = s.mediaServerId 
            OR c.serverId = s.id
        )
        <where>
            <if test="projectId!=null">
                and c.projectId=#{projectId}
            </if>
            <if test="menuId!=null">
                and c.menuId=#{menuId}
            </if>
            <if test="areaId!=null and areaId.size()>0">
                <foreach collection="areaId" open=" and c.areaId in (" close=")" item="id" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="serverId!=null">
                and c.serverId=#{serverId}
            </if>
            <if test="keyword!=null and keyword !=''">
                and c.strName like #{keyword}
            </if>
            <if test="token!=null and token !=''">
               and   c.camToken = #{token}
            </if>
        </where>
        order by c.strName
    </select>


    <select id="GetRoleCam" resultType="com.gh.video.model.vo.CamVo" >
       SELECT 
            <include refid="Base_Cam_Columns"/>,
            <include refid="Server_Columns"/>
        FROM ghauth_userrole ur
        LEFT JOIN ghauth_roledevice rd ON ur.roleId = rd.roleId
        LEFT JOIN ghdm_device d ON rd.deviceId = d.id
        LEFT JOIN ghvideo_cam c ON CONCAT('cam_', c.camToken) = d.code
        LEFT JOIN ghvideo_server s ON (
            c.mediaServerId = s.mediaServerId 
            OR c.serverId = s.id
        )
        <where>
            d.camFlag = 1
            <if test="projectId != null">
                AND c.projectId = #{projectId}
            </if>
            <if test="userId != null">
                AND ur.userId = #{userId}
            </if>
            <if test="menuId != null">
                AND c.menuId = #{menuId}
            </if>
            <if test="areaId != null and areaId.size() > 0">
                AND c.areaId IN
                <foreach collection="areaId" item="id" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="serverId != null">
                AND c.serverId = #{serverId}
            </if>
            <if test="keyword != null and keyword != ''">
                AND c.strName LIKE  #{keyword}
            </if>
            <if test="token != null and token != ''">
                AND c.camToken = #{token}
            </if>
        </where>
        ORDER BY c.strName
    </select>


    <select id="GetCamByToken" resultType="com.gh.video.model.dto.CamDTO" >
        SELECT c.camToken,c.strName,s.`session`,d.id as deviceId ,d.code as deviceCode,ptzEnable,ptz FROM ghvideo_cam c  LEFT JOIN ghdm_device d
        on c.camToken=d.`CODE` LEFT JOIN ghvideo_server s on c.serverId=s.id
        <where>

            <if test="token!=null and token !=''">
                and   c.camToken = #{token}
            </if>
        </where>
        order by c.strName
    </select>



</mapper>
