<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gh.video.mapper.StreamProxyMapper">

    <select id="getStreamProxyItem" resultType="com.gh.video.model.vo.StreamProxyItemVo" >
        SELECT p.areaId,  p.id,p.app,p.stream,p.mediaServerId,p.`enable`,p.`name`,m.ip as mediaServerIP,m.httpPort as mediaServerPort FROM stream_proxy p LEFT JOIN media_server m on p.mediaServerId=m.id
        <where>
            <if test="projectId!=null">
                and p.projectId=#{projectId}
            </if>


            <if test="areaId!=null">
                and p.areaId=#{areaId}
            </if>

            <if test="serverId!=null and serverId !=''">
                and p.mediaServerId=#{serverId}
            </if>
            <if test="keyword!=null and keyword !=''">
                and p.name like #{keyword}
            </if>

        </where>

    </select>


<!--    <select id="GetRoleCam" resultType="com.gh.video.model.vo.CamVo" >-->
<!--        SELECT GHVideo_Cam.id, ghvideo_cam.camToken,serverId,ghvideo_cam.areaId,strName,strUrl,strUser,strPasswd,strSrcIpAddress,strSrcPort,nType as videoType,-->
<!--        lating,ptz,ghvideo_cam.menuId,GHVideo_Cam.deviceType,GHVideo_Cam.projectId,GHVideo_Server.serverName,ghvideo_server.ip,port FROM ghauth_userrole-->
<!--        LEFT JOIN ghauth_roledevice ON ghauth_userrole.roleId=ghauth_roledevice.roleId-->
<!--        LEFT JOIN ghdm_device ON ghauth_roledevice.deviceId=ghdm_device.id-->
<!--        LEFT JOIN ghvideo_cam on concat('cam_',ghvideo_cam.camToken)=ghdm_device.`code`-->
<!--        LEFT JOIN ghvideo_server ON ghvideo_cam.serverId=ghvideo_server.id-->
<!--        WHERE ghdm_device.camFlag=1-->
<!--            <if test="projectId!=null">-->
<!--                and GHVideo_Cam.projectId=#{projectId}-->
<!--            </if>-->
<!--            <if test="userId!=null">-->
<!--                and ghauth_userrole.userId=#{userId}-->
<!--            </if>-->
<!--            <if test="menuId!=null">-->
<!--                and GHVideo_Cam.menuId=#{menuId}-->
<!--            </if>-->
<!--            <if test="areaId!=null">-->
<!--                and GHVideo_Cam.areaId=#{areaId}-->
<!--            </if>-->
<!--            <if test="serverId!=null">-->
<!--                and GHVideo_Cam.serverId=#{serverId}-->
<!--            </if>-->
<!--            <if test="keyword!=null and keyword !=''">-->
<!--                and (GHVideo_Cam.strName like #{keyword} or GHVideo_Cam.camToken like #{keyword})-->
<!--            </if>-->
<!--        order by GHVideo_Cam.strName-->
<!--    </select>-->



</mapper>
