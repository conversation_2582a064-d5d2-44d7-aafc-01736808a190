package com.gh.scene.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ArrayUtil;
import com.gh.common.exception.ExceptionEnum;
import com.gh.common.exception.GhCustomException;
import com.gh.common.exception.SystemEnumMsg;
import com.gh.common.utils.GHResponse;
import com.gh.common.utils.PageResult;
import com.gh.log.annotation.OperLog;
import com.gh.scene.model.entity.IntelligentControl;
import com.gh.scene.service.IntelligentControlService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Arrays;
import java.util.List;

@RestController
@RequestMapping("/intelligent")
@Api(tags = "智慧控制配置")
public class IntelligentController {
    @Autowired
    private IntelligentControlService service;


    @PostMapping
    @ApiOperation("添加智控配置")
    @OperLog(category = "智慧控制配置",description = "添加智慧控制配置")
    @PreAuthorize("hasAnyAuthority('sys_admin','sys_project_admin','sys_intelligent_add')")
    public GHResponse Add(@RequestBody @Valid IntelligentControl intelligentControl)
    {
        if(null== intelligentControl.getMenuId())
        {
            intelligentControl.setTag(1);
        }
        else
        {
            intelligentControl.setTag(2);
        }

        List<IntelligentControl> controls = service.lambdaQuery().eq(IntelligentControl::getProjectId, intelligentControl.getProjectId())
                .eq(IntelligentControl::getTag, intelligentControl.getTag())
                .eq(IntelligentControl::getName, intelligentControl.getName())
                .list();
        if(CollUtil.isNotEmpty(controls))
        {
            throw new GhCustomException(ExceptionEnum.PROJECT_REPEATE_ADD);
        }

        boolean insert = service.save(intelligentControl);
        if(insert)
        {
            return GHResponse.ok(null, SystemEnumMsg.CREATE_SUCCESS.msg());
        }
        return GHResponse.failed(SystemEnumMsg.CREATE_ERROR.msg());
    }

    @PutMapping
    @ApiOperation("更新智控配置")
    @OperLog(category = "智慧控制配置",description = "更新智慧控制配置")
    @PreAuthorize("hasAnyAuthority('sys_admin','sys_project_admin','sys_intelligent_update')")
    public GHResponse Patch(@RequestBody @Valid IntelligentControl intelligentControl)
    {
        if(null== intelligentControl.getMenuId())
        {
            intelligentControl.setTag(1);
        }
        else
        {
            intelligentControl.setTag(2);
        }
        List<IntelligentControl> controls = service.lambdaQuery().eq(IntelligentControl::getProjectId, intelligentControl.getProjectId())
                .eq(IntelligentControl::getTag, intelligentControl.getTag())
                .eq(IntelligentControl::getName, intelligentControl.getName())
                .ne(IntelligentControl::getId,intelligentControl.getId())
                .list();
        if(CollUtil.isNotEmpty(controls))
        {
            throw new GhCustomException(ExceptionEnum.PROJECT_REPEATE_ADD);
        }
        if(intelligentControl.getId()>0)
        {

            boolean update = service.updateById(intelligentControl);
            if(update)
            {
                return GHResponse.ok();
            }
        }

        return GHResponse.failed(SystemEnumMsg.Update_ERROR.msg());
    }
    @DeleteMapping
    @ApiOperation("删除智控配置")
    @OperLog(category = "智慧控制配置",description = "删除智慧控制配置")
    @PreAuthorize("hasAnyAuthority('sys_admin','sys_project_admin','sys_intelligent_del')")
    public GHResponse Delete(@RequestParam Integer[] ids)
    {

        if(ArrayUtil.isNotEmpty(ids))
        {
            boolean delete = service.removeByIds(Arrays.asList(ids));
            if(delete)
            {
                return GHResponse.ok();
            }
        }

        return GHResponse.failed(SystemEnumMsg.Delete_ERROR.msg());
    }

    @GetMapping("{id}")
    @ApiOperation("查询智控配置")
    public GHResponse<IntelligentControl> Select(@PathVariable(value = "id",required = true) Integer id)
    {
        PageResult<List<IntelligentControl>> select = service.select(id, null, null, null, null,null, null);
        if(CollUtil.isNotEmpty(select.getData()))
        {
            return GHResponse.ok(select.getData().get(0));
        }

        return GHResponse.failed(null);
    }
    @GetMapping
    @ApiOperation("查询智控配置")
    public GHResponse<List<IntelligentControl>> Selects(String name,Integer projectId,Integer id,Integer menuId,Integer tag,Integer page, Integer size)
    {
        PageResult<List<IntelligentControl>> select = service.select(id, name, menuId, projectId, tag,page, size);
        return GHResponse.ok(select.getData(),select.getTotal());
    }
}
