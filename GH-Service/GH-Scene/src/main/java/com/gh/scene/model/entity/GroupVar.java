package com.gh.scene.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
@Builder
@ApiModel(description = "GroupVar")
@TableName("GHScene_GroupVar")
@NoArgsConstructor
@AllArgsConstructor
public class GroupVar {

    @TableId(type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty("控制组id")
    @NotNull(message = "控制组id不能为空")
    private Integer groupId;

    @ApiModelProperty("设备指标id")
    private Integer deviceStandardId;

    @ApiModelProperty("变量值")
    private String varValue;

    private Boolean status;

    private Integer projectId;

    @TableField(exist = false)
    private String variable;

    @ApiModelProperty("关联的设备名称")
    @TableField(exist = false)
    private String name;

    @ApiModelProperty("设备指标名称")
    @TableField(exist = false)
    private String standardName;


    @TableField(exist = false)
    private List<Integer> deviceId;

    //设备指标
    @TableField(exist = false)
    private Integer standardId;


    @TableField(exist = false)
    private Integer productId;


}
