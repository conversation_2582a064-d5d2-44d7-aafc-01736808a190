package com.gh.scene.controller;

import cn.hutool.core.util.ArrayUtil;
import com.gh.common.exception.ExceptionEnum;
import com.gh.common.exception.GhCustomException;
import com.gh.common.exception.SystemEnumMsg;
import com.gh.common.utils.GHResponse;
import com.gh.common.utils.PageResult;
import com.gh.log.annotation.OperLog;
import com.gh.scene.model.dto.StrategyDto;
import com.gh.scene.model.entity.Strategy;
import com.gh.scene.service.StrategyService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Arrays;
import java.util.List;


@RestController
@RequestMapping("/strategy")
@Api(tags = "策略控制配置")
public class StrategyController {
    @Autowired
    private StrategyService service;

    @PostMapping
    @ApiOperation("添加策略配置")
    @OperLog(category = "策略控制配置",description = "添加策略控制配置")
    @PreAuthorize("hasAnyAuthority('sys_admin','sys_project_admin','sys_strategy_add')")
    public GHResponse Add(@RequestBody @Valid Strategy strategy)
    {
        if(null== strategy.getMenuId())
        {
            strategy.setTag(1);
        }
        else
        {
            strategy.setTag(2);
        }
        if(checkExist(strategy.getProjectId(),strategy.getName(),null))
        {
            throw new GhCustomException(ExceptionEnum.PROJECT_REPEATE_ADD);
        }
        boolean insert = service.insert(strategy);
        if(insert)
        {
            return GHResponse.ok(null, SystemEnumMsg.CREATE_SUCCESS.msg());
        }
        return GHResponse.failed(SystemEnumMsg.CREATE_ERROR.msg());
    }

    @PutMapping
    @ApiOperation("更新策略配置")
    @OperLog(category = "策略控制配置",description = "更新策略控制配置")
    @PreAuthorize("hasAnyAuthority('sys_admin','sys_project_admin','sys_strategy_update')")
    public GHResponse Patch(@RequestBody @Valid Strategy strategy)
    {
        if(null== strategy.getMenuId())
        {
            strategy.setTag(1);
        }
        else
        {
            strategy.setTag(2);
        }
        if(checkExist(strategy.getProjectId(),strategy.getName(),strategy.getId()))
        {
            throw new GhCustomException(ExceptionEnum.PROJECT_REPEATE_ADD);
        }
        if(strategy.getId()>0)
        {
            boolean update = service.update(strategy);
            if(update)
            {
                return GHResponse.ok();
            }
        }

        return GHResponse.failed(SystemEnumMsg.Update_ERROR.msg());
    }

    // 更新策略配置状态
    @PostMapping("state")
    @ApiOperation("更新策略配置状态")
    @OperLog(category = "策略控制配置",description = "更新策略控制配置状态")
    public GHResponse<String> PatchState(@RequestBody Strategy strategy)
    {

        if(strategy.getId()!=null)
        {
            Boolean update = service.lambdaUpdate().set(Strategy::getStatus, strategy.getStatus())
                    .eq(Strategy::getId, strategy.getId())
                    .update();
            if(update)
            {
                return GHResponse.ok(SystemEnumMsg.Update_SUCCESS.msg());
            }
        }
        return GHResponse.failed(SystemEnumMsg.Update_ERROR.msg());
    }

    @DeleteMapping
    @ApiOperation("删除策略配置")
    @OperLog(category = "策略控制配置",description = "删除策略控制配置")
    @PreAuthorize("hasAnyAuthority('sys_admin','sys_project_admin','sys_strategy_del')")
    public GHResponse Delete(@RequestParam Integer[] ids)
    {

        if(ArrayUtil.isNotEmpty(ids))
        {
            boolean delete = service.removeByIds(Arrays.asList(ids));
            if(delete)
            {
                return GHResponse.ok();
            }
        }

        return GHResponse.ok(SystemEnumMsg.Delete_ERROR.msg());
    }


    @GetMapping
    @ApiOperation("查询策略配置")
    public GHResponse<List<StrategyDto>> SelectStrategy(String name, Integer projectId, Integer id, Integer menuId, String ids, Integer tag, Integer page, Integer size)
    {
        PageResult<List<StrategyDto>> select = service.select(id, name, menuId, projectId,ids ,tag,page, size);
        return GHResponse.ok(select.getData(),select.getTotal());
    }


    private boolean checkExist(Integer projectId, String name,Integer id)
    {
        return this.service.lambdaQuery()
                .eq(Strategy::getProjectId, projectId)
                .eq(Strategy::getName, name)
                .ne(id!=null,Strategy::getId,id)
                .count() > 0;
    }
    

}
