package com.gh.scene.repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gh.scene.model.dto.StrategyScheduleDTO;
import com.gh.scene.model.entity.StrategySchedule;


import java.util.List;

import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;



@Repository
public interface StrategyScheduleMapper extends BaseMapper<StrategySchedule>{

     List<StrategyScheduleDTO> getStrategySchedule(@Param("projectId") Integer projectId, @Param("bt") String bt, @Param("et") String et);

}
