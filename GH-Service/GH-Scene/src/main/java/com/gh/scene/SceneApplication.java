package com.gh.scene;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableScheduling;

@SpringBootApplication
@EnableDiscoveryClient
@ComponentScan(basePackages = "com.gh")
@EnableFeignClients("com.gh")
@EnableScheduling
public class SceneApplication {
    public static void main(String[] args) {
        SpringApplication.run(SceneApplication.class,args);
    }
}
