package com.gh.scene.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.gh.common.utils.PageResult;
import com.gh.scene.model.dto.StrategyModeGroupDTO;
import com.gh.scene.model.entity.StrategyModeGroup;

import java.util.List;


public interface StrategyModeGroupService extends IService<StrategyModeGroup> {

    PageResult<List<StrategyModeGroupDTO>> getStrategyModeGroupList(Integer page, Integer size, Integer id, Integer strategyModeId, String keyword);

}
