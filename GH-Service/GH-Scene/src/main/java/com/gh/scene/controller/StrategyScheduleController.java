package com.gh.scene.controller;

import com.gh.common.exception.SystemEnumMsg;
import com.gh.common.utils.GHResponse;
import com.gh.scene.model.dto.StrategyScheduleDTO;
import com.gh.scene.model.entity.StrategySchedule;
import com.gh.scene.service.StrategyScheduleService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;

import org.springframework.web.bind.annotation.*;




@RestController
@RequestMapping("/strategySchedule")
@Api(tags = "策略日程编排")
public class StrategyScheduleController {
    @Autowired
    private StrategyScheduleService service;

    @PostMapping
    @ApiOperation("添加策略日程编排")
    public GHResponse<String> addStrategySchedule(@RequestBody StrategySchedule strategySchedule) {
       return service.save(strategySchedule)
              ? GHResponse.ok(SystemEnumMsg.CREATE_SUCCESS.msg())
              : GHResponse.failed(SystemEnumMsg.CREATE_ERROR.msg());
       
    }

    @PutMapping
    @ApiOperation("更新策略日程编排")
    public GHResponse<String> updateStrategySchedule(@RequestBody StrategySchedule strategySchedule) {
       return  service.updateById(strategySchedule)
              ? GHResponse.ok(SystemEnumMsg.Update_SUCCESS.msg())
              : GHResponse.failed(SystemEnumMsg.Update_ERROR.msg());
    }

    @DeleteMapping
    @ApiOperation("删除策略日程编排")
    public GHResponse<String> deleteStrategySchedule(@RequestParam Integer id) {
        return service.removeById(id)
              ? GHResponse.ok(SystemEnumMsg.Delete_SUCCESS.msg())
              : GHResponse.failed(SystemEnumMsg.Delete_ERROR.msg());
    }

    @GetMapping
    @ApiOperation("查询策略日程编排")
    public GHResponse<List<StrategyScheduleDTO>> getStrategySchedule(String bt,String et,Integer projectId) {
         return GHResponse.ok(service.getStrategySchedule(projectId, bt, et));
    }


    
    
    


    

}
