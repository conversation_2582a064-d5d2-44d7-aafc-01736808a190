package com.gh.scene.controller;

import cn.hutool.core.bean.BeanUtil;
import com.gh.common.exception.SystemEnumMsg;
import com.gh.common.utils.GHResponse;
import com.gh.scene.model.dto.ConfigDto;
import com.gh.scene.model.dto.RunningDto;
import com.gh.scene.model.entity.Running;
import com.gh.scene.service.RunningService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;


@RestController
@RequestMapping("/running")
@Api(tags = "智慧 策略 手动 模式")
public class RunningController {
    @Autowired
    private RunningService runningService;

    @PutMapping
    @ApiOperation("更新系统或者子系统模式")
    public GHResponse Patch(@RequestBody @Valid RunningDto running)
    {

        Running running1 = Running.builder().build();
        BeanUtil.copyProperties(running,running1);
        if(null!=running.getIds())
        {
            running1.setIds("["+running.getIds()+"]");
        }
        boolean update = runningService.AddOrUpdate(running1);
        if(update)
        {
           return  GHResponse.ok();
        }
        return GHResponse.failed(SystemEnumMsg.Update_ERROR.msg());
    }


    @PutMapping("uncheck")
    @ApiOperation("取消系统或者子系统模式配置")
    public GHResponse PatchUnCheck(Integer projectId, Integer type, Integer tag, Integer id, Integer menuId)
    {

        boolean update = runningService.unCheck(projectId,type,id,tag,menuId);
        if(update)
        {
            return  GHResponse.ok();
        }
        return GHResponse.failed(SystemEnumMsg.Update_ERROR.msg());
    }



    @GetMapping
    @ApiOperation("查询当前模式")
    public GHResponse<List<Running>> Selects(Integer type ,Integer projectId, Integer menuId,Integer tag,
                                                             Boolean status)
    {
        List<Running> select = runningService.select(type, projectId, menuId,tag,status);
        return GHResponse.ok(select);
    }


    /**
     * 一次查询三种模式下的配置

     * @param projectId
     * @param menuId
     * @param tag

     * @return
     */
    @GetMapping("config")
    @ApiOperation("一次查询三种模式下的配置")
    public GHResponse<ConfigDto> SelectsConfig(Integer projectId, Integer menuId,Integer tag,String type)

    {
        ConfigDto configDto = runningService.select(tag, projectId, menuId,type);
        return GHResponse.ok(configDto);
    }








}
