package com.gh.scene.controller;

import com.gh.common.utils.GHResponse;
import com.gh.common.utils.PageResult;
import com.gh.scene.model.dto.StrategyModeGroupDTO;
import com.gh.scene.model.entity.StrategyModeGroup;
import com.gh.scene.service.StrategyModeGroupService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;

@RestController
@RequestMapping("/strategyModeGroup")
@Api(tags = "策略模式控制组")
public class StrategyModeGroupController {

    @Autowired
    private StrategyModeGroupService strategyModeGroupService;

    @PostMapping
    @ApiOperation("新增策略模式控制组")
    public GHResponse<Boolean> addStrategyModeGroup(@RequestBody StrategyModeGroup strategyModeGroup) {
        return GHResponse.ok(strategyModeGroupService.save(strategyModeGroup));
    }

    @PutMapping
    @ApiOperation("更新策略模式控制组")
    public GHResponse<Boolean> updateStrategyModeGroup(@RequestBody StrategyModeGroup strategyModeGroup) {
        return GHResponse.ok(strategyModeGroupService.updateById(strategyModeGroup));
    }

    @DeleteMapping
    @ApiOperation("删除策略模式控制组")
    public GHResponse<Boolean> deleteStrategyModeGroup(@RequestParam Integer[] ids) {
        return GHResponse.ok(strategyModeGroupService.removeByIds(Arrays.asList(ids)));
    }



    @GetMapping
    @ApiOperation("查询策略模式控制组列表")
    public GHResponse<List<StrategyModeGroupDTO>> getStrategyModeGroupList(Integer page,Integer size,Integer id,Integer strategyModeId,String keyword) {
        PageResult<List<StrategyModeGroupDTO>> groupList = strategyModeGroupService.getStrategyModeGroupList(page, size, id, strategyModeId, keyword);
        return GHResponse.ok(groupList.getData(),groupList.getTotal());
    }



    
    

}
