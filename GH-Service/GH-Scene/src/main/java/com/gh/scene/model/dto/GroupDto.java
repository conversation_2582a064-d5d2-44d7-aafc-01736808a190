package com.gh.scene.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
@Builder

@NoArgsConstructor
@AllArgsConstructor
public class GroupDto {

    private Integer id;

    @ApiModelProperty("控制组名称")
    @NotEmpty(message = "name不能为空")
    private String name;

    private String description;

    @ApiModelProperty("项目id")
    @NotNull(message = "项目id不能为空")
    private Integer projectId;



    @Builder.Default
    private Boolean status=true;

    @ApiModelProperty("1 控制场景控制组，2 记录场景控制组  3 运行控制组")
    @NotNull(message = "type(控制组类型不能为空)")
    private Integer type;

    private String times;

    private String startVal;

    private String endVal;

    private Integer delay;
    private Integer strategyId;

    private List<GroupVarDto> groupVars;
}
