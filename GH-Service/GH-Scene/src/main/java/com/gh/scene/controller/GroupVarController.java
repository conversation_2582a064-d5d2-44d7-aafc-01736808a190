package com.gh.scene.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ArrayUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.gh.common.exception.ExceptionEnum;
import com.gh.common.exception.GhCustomException;
import com.gh.common.exception.SystemEnumMsg;
import com.gh.common.model.entity.DeviceStandard;
import com.gh.common.utils.GHResponse;
import com.gh.common.utils.PageResult;
import com.gh.log.annotation.OperLog;
import com.gh.scene.model.dto.GroupImportDto;
import com.gh.scene.model.entity.GroupVar;
import com.gh.scene.model.vo.GroupVarVo;
import com.gh.scene.service.DeviceStandardService;
import com.gh.scene.service.IGroupVarService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Arrays;
import java.util.List;

@RestController
@RequestMapping("/groupVar")
@Api(tags = "控制组变量管理")
public class GroupVarController {
    @Autowired
    private IGroupVarService groupVarService;

    @Autowired
    private DeviceStandardService deviceStandardService;


    @PostMapping
    @ApiOperation("添加控制组变量")
//    @OperLog(category = "控制组变量管理", description = "添加控制组变量")
    @PreAuthorize("hasAnyAuthority('sys_admin','sys_project_admin','sys_group_var_add')")
    public GHResponse Add(@RequestBody @Valid GroupVar groupVar) {
        List<GroupVar> vars = groupVarService
                .lambdaQuery()
                .eq(GroupVar::getProjectId, groupVar.getProjectId())
                .eq(GroupVar::getGroupId, groupVar.getGroupId())
                .eq(GroupVar::getDeviceStandardId, groupVar.getDeviceStandardId()).list();
        if (CollUtil.isNotEmpty(vars)) {
            throw new GhCustomException(ExceptionEnum.GROUP_VARIABLE_EXIST);
        }
        if (CollUtil.isNotEmpty(groupVar.getDeviceId())) {
            groupVar.getDeviceId().forEach(id -> {
                int stdId = 0;
                if (groupVar.getDeviceStandardId() != null) {
                    stdId = groupVar.getDeviceStandardId();
                } else {
                    List<DeviceStandard> deviceStandard = deviceStandardService.lambdaQuery().eq(DeviceStandard::getDeviceId, id)
                            .eq(DeviceStandard::getStandardId, groupVar.getStandardId()).list();
                    if (CollUtil.isNotEmpty(deviceStandard)) {
                        stdId = deviceStandard.get(0).getId();
                    }
                }
                if (stdId > 0) {
                    GroupVar groupVar2 = GroupVar.builder().status(true)
                            .deviceStandardId(stdId)
                            .projectId(groupVar.getProjectId())
                            .varValue(groupVar.getVarValue())
                            .groupId(groupVar.getGroupId()).build();
                    groupVarService.saveOrUpdate(groupVar2, Wrappers.<GroupVar>lambdaQuery().
                            eq(GroupVar::getDeviceStandardId, groupVar2.getDeviceStandardId())
                            .eq(GroupVar::getGroupId, groupVar2.getGroupId()));
                }

            });
        }
        return GHResponse.ok();
    }

    @PutMapping
    @ApiOperation("更新控制组变量")
    @OperLog(category = "控制组变量管理", description = "更新控制组变量")
    @PreAuthorize("hasAnyAuthority('sys_admin','sys_project_admin','sys_group_var_update')")
    public GHResponse Patch(@RequestBody @Valid GroupVar groupVar) {
        PageResult<List<GroupVar>> groupVar1 = groupVarService.findGroupVar(groupVar.getProjectId(), null, groupVar.getDeviceStandardId(), groupVar.getGroupId(), null, null);
        if (CollUtil.isNotEmpty(groupVar1.getData()) && !groupVar1.getData().get(0).getId().equals(groupVar.getId())) {
            throw new GhCustomException(ExceptionEnum.GROUP_VARIABLE_EXIST);
        }

        if (CollUtil.isNotEmpty(groupVar.getDeviceId())) {
            DeviceStandard deviceStandard = deviceStandardService.lambdaQuery().eq(DeviceStandard::getDeviceId, groupVar.getDeviceId().get(0))
                    .eq(DeviceStandard::getStandardId, groupVar.getStandardId()).one();
            if (null != deviceStandard) {
                groupVar.setDeviceStandardId(deviceStandard.getId());
                groupVarService.updateById(groupVar);
            }
        }
        return GHResponse.ok();
    }

    @DeleteMapping
    @ApiOperation("删除控制组变量")
    @OperLog(category = "控制组变量管理", description = "删除控制组变量")
    @PreAuthorize("hasAnyAuthority('sys_admin','sys_project_admin','sys_group_var_del')")
    public GHResponse Delete(@RequestParam Integer[] ids) {
        if (ArrayUtil.isNotEmpty(ids)) {
            boolean delete = groupVarService.removeByIds(Arrays.asList(ids));
            if (delete) {
                return GHResponse.ok();
            }
        }

        return GHResponse.failed(SystemEnumMsg.Delete_ERROR.msg());
    }

    @GetMapping("/var")
    @ApiOperation("查询控制组变量")
    public GHResponse<GroupVar> Select(Integer projectId, Integer groupId, String variable) {
        PageResult<List<GroupVar>> groupVar = groupVarService.findGroupVar(projectId, variable, null, groupId, null, null);
        if (CollUtil.isNotEmpty(groupVar.getData())) {
            return GHResponse.ok(groupVar.getData().get(0));
        }
        return GHResponse.ok(null);
    }

    @GetMapping
    @ApiOperation("查询控制组变量")
    public GHResponse<List<GroupVarVo>> Selects(Integer projectId, String keyword, Integer groupId, Integer type, Integer page, Integer size) {
        PageResult<List<GroupVarVo>> groupVar = groupVarService.getGroupVarVo(groupId, keyword, projectId, type, null, page, size);
        return GHResponse.ok(groupVar.getData(), groupVar.getTotal());
    }




    @PostMapping("import")
    @ApiOperation("导入控制组变量")
    @OperLog(category = "控制组变量管理", description = "导入控制组变量")
    public GHResponse importGrup(@RequestBody GroupImportDto groupVar) {
        List<GroupVar> groupVars = groupVarService.lambdaQuery()
                .eq(GroupVar::getGroupId, groupVar.getImportGroupId()).list();
        if (CollUtil.isNotEmpty(groupVars)) {
            groupVars.forEach(groupVar1 -> {
                DeviceStandard deviceStandard = deviceStandardService.lambdaQuery().eq(DeviceStandard::getId, groupVar1.getDeviceStandardId()).one();
                if (null != deviceStandard) {
                    DeviceStandard standard = deviceStandardService.lambdaQuery().eq(DeviceStandard::getDeviceId, deviceStandard.getDeviceId())
                            .eq(DeviceStandard::getStandardId, groupVar.getStandardId()).one();
                    if (null != standard) {
                        GroupVar groupVar2 = GroupVar.builder().status(true)
                                .deviceStandardId(standard.getId())
                                .projectId(groupVar.getProjectId())
                                .varValue(groupVar.getVarValue())
                                .groupId(groupVar.getGroupId()).build();
                        groupVarService.saveOrUpdate(groupVar2, Wrappers.<GroupVar>lambdaQuery().
                                eq(GroupVar::getDeviceStandardId, groupVar2.getDeviceStandardId())
                                .eq(GroupVar::getGroupId, groupVar2.getGroupId()));
                    }

                }


            });
        }
        return GHResponse.ok();
    }
}
