package com.gh.scene.model.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 策略模式
 */
@Data
@Builder
@ApiModel(description = "策略模式事件")
@TableName("GHOPS_StrategyModeEvent")
@NoArgsConstructor
@AllArgsConstructor
public class StrategyModeEvent {
    
    @TableId(type = IdType.AUTO)
    private Integer id;

    private Date startTime;

    private Date endTime;

    private Integer strategyModeId;

    private Integer day;

    private String varVal;

    private Integer groupId;



}
