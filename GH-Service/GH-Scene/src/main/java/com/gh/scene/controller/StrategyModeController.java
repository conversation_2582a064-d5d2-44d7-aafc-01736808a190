package com.gh.scene.controller;


import com.gh.common.exception.ExceptionEnum;
import com.gh.common.exception.GhCustomException;
import com.gh.common.utils.GHResponse;
import com.gh.scene.model.entity.StrategyMode;
import com.gh.scene.service.StrategyModeService;

import cn.hutool.core.util.BooleanUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Arrays;
import java.util.List;


@RestController
@RequestMapping("/strategyMode")
@Api(tags = "策略模式配置")
public class StrategyModeController {

    @Autowired
    private StrategyModeService strategyModeService;

    // 新增策略模式
    @PostMapping
    @ApiOperation("新增策略模式")
    public GHResponse<Boolean> addStrategyMode( @Valid @RequestBody StrategyMode strategyMode) {
        // 判断是否存在
        if (isExist(strategyMode.getName(), strategyMode.getProjectId(), null)) {
            throw new GhCustomException(ExceptionEnum.PROJECT_REPEATE_ADD);
        }
        return GHResponse.ok(strategyModeService.save(strategyMode));
    }

    // 更新策略模式
    @PutMapping
    @ApiOperation("更新策略模式")
    public GHResponse<Boolean> updateStrategyMode(@RequestBody StrategyMode strategyMode) {
        // 判断是否存在
        if (isExist(strategyMode.getName(), strategyMode.getProjectId(), strategyMode.getId())) {
            throw new GhCustomException(ExceptionEnum.PROJECT_REPEATE_ADD);
        }
        //只能有一种模式开启
        if(BooleanUtil.isTrue(strategyMode.getStatus())){
            strategyModeService.lambdaUpdate()
            .eq(StrategyMode::getProjectId, strategyMode.getProjectId())
            .set(StrategyMode::getStatus, 0)
            .update();
        }
        return GHResponse.ok(strategyModeService.updateById(strategyMode));
    }

    // 删除策略模式
    @DeleteMapping
    @ApiOperation("删除策略模式")
    public GHResponse<Boolean> deleteStrategyMode(@RequestParam Integer[] ids) {
        return GHResponse.ok(strategyModeService.removeByIds(Arrays.asList(ids)));
    }
    
    // 查询策略模式
    @GetMapping
    @ApiOperation("查询策略模式")
    public GHResponse<List<StrategyMode>> getStrategyMode(String name,Integer projectId,Integer id) {
        return GHResponse.ok(strategyModeService
        .lambdaQuery()
        .eq(name != null, StrategyMode::getName, name)
        .eq(projectId != null, StrategyMode::getProjectId, projectId)
        .ne(id != null, StrategyMode::getId, id)
        .list());
    }



    private boolean isExist(String name,Integer projectId,Integer id) {
       
        return strategyModeService.lambdaQuery()
        .eq(StrategyMode::getName, name)
        .eq(StrategyMode::getProjectId, projectId)
        .ne(id != null, StrategyMode::getId, id)
        .count() > 0;
    }
    
    
    
  
    

}
