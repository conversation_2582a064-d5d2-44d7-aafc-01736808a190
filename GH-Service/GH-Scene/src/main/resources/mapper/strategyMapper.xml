<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gh.scene.repository.StrategyRepository">

    <select id="selectVar" resultType="com.gh.scene.model.dto.GroupVarDto">
        select ghscene_groupvar.*,ghdm_devicestandard.variable ,ghdm_standard.`name` as standardName,ghdm_device.`name`,
        ghdm_device.id as deviceId
        from ghscene_groupvar left join ghdm_devicestandard
        on ghscene_groupvar.deviceStandardId=ghdm_devicestandard.id
        LEFT JOIN ghdm_standard on ghdm_devicestandard.standardId=ghdm_standard.id
        LEFT JOIN ghdm_device ON ghdm_devicestandard.deviceId=ghdm_device.id
        <where>
            <if test="id!=null">
                ghscene_groupvar.groupId=#{id}
            </if>
        </where>
    </select>
    <resultMap id="groupMap" type="com.gh.scene.model.dto.GroupDto" autoMapping="true">
        <id column="id" property="id"></id>
        <result column="name" property="name"></result>
        <result column="type" property="type"></result>
        <collection property="groupVars" column="id" select="selectVar"></collection>
    </resultMap>



    <select id="selectGroup" resultMap="groupMap">
        select ghscene_group.*,startVal,endVal,delay from ghscene_group
        LEFT JOIN GHOPS_StrategyGroup on ghscene_group.id=GHOPS_StrategyGroup.groupId
        <where>
            ghscene_group.type =3 and ghscene_group.status=1 and GHOPS_StrategyGroup.type =1
            <if test="id!=null">
                and GHOPS_StrategyGroup.strategyId=#{id}
            </if>
        </where>
    </select>
    <select id="selectGroup1" resultMap="groupMap">
        select ghscene_group.* from ghscene_group
        LEFT JOIN GHOPS_StrategyGroup on ghscene_group.id=GHOPS_StrategyGroup.groupId

        <where>
            ghscene_group.type =3 and ghscene_group.status=1 and GHOPS_StrategyGroup.type =2
            <if test="id!=null">
                and GHOPS_StrategyGroup.strategyId=#{id}
            </if>
        </where>
    </select>
    <resultMap id="strategyMap" type="com.gh.scene.model.dto.StrategyDto" autoMapping="true">
        <id column="id" property="id"></id>
        <collection property="startGroup" column="id"  select="selectGroup"></collection>
<!--        <collection property="endGroup" column="id" select="selectGroup1"></collection>-->
    </resultMap>

    <select id="select"  resultMap="strategyMap">
        SELECT GHOPS_Strategy.* FROM GHOPS_Strategy
        <where>
            <if test="projectId!=null">
                and GHOPS_Strategy.projectId=#{projectId}
            </if>
            <if test="id!=null">
                and GHOPS_Strategy.id=#{id}
            </if>
            <if test="tag!=null">
                and GHOPS_Strategy.tag = #{tag}
            </if>
            <if test="menuId!=null">
                and GHOPS_Strategy.menuId=#{menuId}
            </if>
            <if test="name!=null">
                and GHOPS_Strategy.id like #{name}
            </if>
            <if test="ids!=null and ids!=''">
                and GHOPS_Strategy.id in ${ids}
            </if>
        </where>
    </select>

    <select id="selectRole"  resultMap="strategyMap">
        SELECT GHOPS_Strategy.* FROM GHOPS_Strategy
        LEFT JOIN ghauth_rolestrategy ON GHOPS_Strategy.id=ghauth_rolestrategy.id
        <where>
            <if test="projectId!=null">
                and GHOPS_Strategy.projectId=#{projectId}
            </if>
            <if test="id!=null">
                and GHOPS_Strategy.id=#{id}
            </if>
            <if test="tag!=null">
                and GHOPS_Strategy.tag = #{tag}
            </if>
            <if test="menuId!=null">
                and GHOPS_Strategy.menuId=#{menuId}
            </if>
            <if test="name!=null">
                and GHOPS_Strategy.id like #{name}
            </if>
            <if test="ids!=null and ids!=''">
                and GHOPS_Strategy.id in ${ids}
            </if>
            <if test="roleId!=null and roleId.size()>0">
                <foreach collection="roleId" open=" and roleId in (" close=")" item="id" separator=",">
                    #{id}
                </foreach>
            </if>
        </where>
    </select>

</mapper>
