package com.gh.energy.dao;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * Description:
 * User: zhangkeguang
 * Date: 2022-04-20-10:57
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class EnergyCategoryDown {

    @ExcelProperty(value = "分项名称",index = 0)

    private String name;

    @ExcelProperty("数值")
    private Double value;

    @ExcelProperty("时间")
    private Date time;



}
