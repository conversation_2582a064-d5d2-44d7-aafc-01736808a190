package com.gh.energy.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import lombok.Builder;
import lombok.Data;
import org.bouncycastle.cms.PasswordRecipientId;

@Data
@Builder
@TableName("GHEnergy_Feature")
@ApiModel(description = "能耗功能信息")
public class Feature {
    @TableId(type = IdType.AUTO)
    private Integer id;
    private Integer parentId;
    private String name;
    private String description;
    private Integer projectId;
    private String fullName;
    private String fullPath;
}
