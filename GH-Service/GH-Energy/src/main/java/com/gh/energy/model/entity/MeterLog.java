package com.gh.energy.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import lombok.Builder;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.PipedReader;
import java.math.BigDecimal;
import java.util.Date;

@Data
@Builder
@TableName("GHEnergy_MeterLog")
@ApiModel(description = "换表记录")
public class MeterLog {
    @TableId(type = IdType.AUTO)
    private Integer id;
    @NotNull(message = "deviceId 不能为空")
    private Integer deviceId;
    @NotNull(message = "oldValue 不能为空")
    private BigDecimal oldValue;
    @NotNull(message = "initValue 不能为空")
    private BigDecimal initValue;
    @NotNull(message = "operaterId 不能为空")
    private Integer operaterId;
    @NotNull(message = "projectId 不能为空")
    private Integer projectId;


    private  BigDecimal oldFValue=new BigDecimal(0);
    private  BigDecimal oldPValue=new BigDecimal(0);
    private  BigDecimal oldGValue=new BigDecimal(0);

    private  BigDecimal intFValue=new BigDecimal(0);
    private  BigDecimal intPValue=new BigDecimal(0);
    private  BigDecimal intGValue=new BigDecimal(0);


    private Date logTime;
}
