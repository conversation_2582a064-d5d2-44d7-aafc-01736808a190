package com.gh.energy.controller;


import cn.hutool.core.date.DateUtil;
import com.gh.common.exception.SystemEnumMsg;
import com.gh.common.utils.GHResponse;
import com.gh.energy.model.entity.MeterLog;
import com.gh.energy.response.PageResult;
import com.gh.energy.service.MeterLogService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.Date;
import java.util.List;

@RequestMapping("/meter")
@RestController
@Api(tags = "换表")
public class MeterLogController {

    @Autowired
    private MeterLogService meterLogService;


    @PostMapping
    @ApiOperation("新增换表记录")
    public GHResponse Add(@Valid @RequestBody MeterLog meterLog) {

        meterLog.setLogTime(DateUtil.date());
        boolean insert = meterLogService.insert(meterLog);
        if(insert)
        {
           return GHResponse.ok(null, SystemEnumMsg.CREATE_SUCCESS.msg()) ;
        }
        return   GHResponse.ok(null, SystemEnumMsg.CREATE_ERROR.msg()) ;
    }




    @GetMapping
    @ApiOperation("查询换表记录")
    public GHResponse<List<MeterLog>> Select(Integer id,
                                            Integer page,Integer size,
                                            @RequestParam @DateTimeFormat( pattern = "yyyy-MM-dd HH:mm:ss") Date bt,
                                            @RequestParam  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")  Date et, Integer projectId) {
        PageResult<List<MeterLog>> select = meterLogService.select(id,bt,et,projectId,size,page);
        return   GHResponse.ok(select.getData(),select.count) ;
    }



}
