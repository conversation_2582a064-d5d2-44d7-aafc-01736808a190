package com.gh.energy.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gh.energy.response.PageResult;
import com.gh.energy.mapper.ChargeMapper;
import com.gh.energy.model.entity.ChargeType;
import com.gh.energy.service.ChargeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.List;


@Service
public class ChargeServiceImpl extends BaseServiceImpl<ChargeMapper> implements ChargeService {

    @Autowired
    private ChargeMapper chargeMapper;

    @Override
    public PageResult<List<ChargeType>> select(Integer id, String name, Integer projectId , Integer deviceType, Integer size, Integer page) {

        QueryWrapper<ChargeType> queryWrapper = new QueryWrapper<>();
        if(null!=id)
        {
            queryWrapper.eq("id",id);
        }
        if(StrUtil.isNotBlank(name))
        {
            queryWrapper.like("name","%"+name+"%");
        }
        if(null!=projectId)
        {
            queryWrapper.eq("projectId",projectId);
        }
        if(null!=deviceType)
        {
            queryWrapper.eq("deviceType",deviceType);

        }
        queryWrapper.orderByDesc("priceTime");
        if(null!=size&&null!=page&&size>0&&page>0)
        {
            Page<ChargeType> devicePage = new Page<>(page, size);
            Page<ChargeType> selectPage = chargeMapper.selectPage(devicePage, queryWrapper);
            return PageResult.<List<ChargeType>>builder().data(selectPage.getRecords())
                    .count(selectPage.getTotal()).build();
        }else
        {
            List<ChargeType> devices = chargeMapper.selectList(queryWrapper);
            return PageResult.<List<ChargeType>>builder().data(devices)
                    .count(devices.size()).build();
        }
    }

    @Override
    public List<ChargeType> selectFirstCharge(Integer projectId) {
        return chargeMapper.selectFirstCharge(projectId);
    }


}
