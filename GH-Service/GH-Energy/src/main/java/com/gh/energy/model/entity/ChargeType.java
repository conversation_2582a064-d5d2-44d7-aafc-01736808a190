package com.gh.energy.model.entity;


import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalTime;
import java.util.Date;

@Data
@Builder
@TableName("GHEnergy_Charge")
@ApiModel(description = "电费收费类型")
public class ChargeType {
    private Integer id;
    @NotNull(message = "status不能为空")
    private Boolean status=true;
    @NotNull(message = "projectId不能为空")
    private Integer projectId;

    @NotNull(message = "电费定价时间")
    private Date priceTime;

    @NotNull(message = "deviceType不能为空")

    @ApiModelProperty("1电表 2水表 3气表")
    private Integer deviceType;

    private BigDecimal price;
    private BigDecimal pPrice;
    private BigDecimal fPrice;
    private BigDecimal gPrice;


}
