package com.gh.energy.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gh.common.exception.ExceptionEnum;
import com.gh.common.exception.GhCustomException;
import com.gh.energy.mapper.MeterLogMapper;
import com.gh.energy.model.dto.DeviceData;
import com.gh.energy.model.dto.DeviceDataPrice;
import com.gh.energy.model.entity.ChargeType;
import com.gh.energy.model.entity.Device;
import com.gh.energy.model.entity.MeterLog;
import com.gh.energy.model.enums.DateEnum;
import com.gh.energy.response.PageResult;
import com.gh.energy.service.ChargeService;
import com.gh.energy.service.DeviceService;
import com.gh.energy.service.MeterLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import static com.gh.energy.model.enums.DateEnum.*;

@Service
public class MeterLogServiceImpl extends BaseServiceImpl<MeterLogMapper> implements MeterLogService {

    @Autowired
    private MeterLogMapper meterLogMapper;

    @Autowired
    private DeviceService deviceService;



    @Autowired
    private ChargeService chargeService;

    @Override
    public PageResult<List<MeterLog>> select(Integer id, Date bt, Date et, Integer projectId, Integer size, Integer page) {
        QueryWrapper<MeterLog> queryWrapper = new QueryWrapper<>();
        if(null!=id)
        {
            queryWrapper.eq("id",id);
        }
        if(null!=bt&&null!=et)
        {
            queryWrapper.ge("logTime",bt);
            queryWrapper.le("logTime",et);
        }
        if(null!=projectId)
        {
            queryWrapper.eq("projectId",projectId);
        }
        if(null!=size&&null!=page&&size>0&&page>0)
        {
            Page<MeterLog> logPage = new Page<>(page, size);
            Page<MeterLog> meterLogPage = meterLogMapper.selectPage(logPage, queryWrapper);
            return PageResult.<List<MeterLog>>builder().data(meterLogPage.getRecords())
                    .count(meterLogPage.getTotal()).build();
        }
        else
        {

            List<MeterLog> logs = meterLogMapper.selectList(queryWrapper);
            return PageResult.<List<MeterLog>>builder().data(logs)
                    .count(logs.size()).build();
        }
    }

    @Override
    public boolean insert(MeterLog meterLog) {
        PageResult<List<Device>> select = deviceService.select(meterLog.getDeviceId(), null, null, null, null, null, null, null, null, null);
        if (null != select && CollUtil.isNotEmpty(select.getData())) {
            DateTime now = DateUtil.date();
            //校正时间 支持 10 20 30分钟抄表触发
            Integer min = now.minute() / 10 * 10;
            now.setField(DateField.MINUTE, min).setField(DateField.SECOND, 0).setField(DateField.MILLISECOND, 0);
            DateTime offset = DateUtil.offset(now, DateField.MINUTE, -10);
            Device device = select.getData().get(0);
            if (device.getStatus()) {
                throw new GhCustomException(ExceptionEnum.METER_ON_STATUS);
            }
            //结算本期数据
            for (DateEnum value : values()) {
                DeviceData minData = MaxAndMin(device.getId(), now, value, "");
                if (meterLog.getOldValue().compareTo(minData.getValue()) >= 0
                        && meterLog.getOldFValue().compareTo(minData.getFValue()) >= 0
                        && meterLog.getOldPValue().compareTo(minData.getPValue()) >= 0
                        && meterLog.getOldGValue().compareTo(minData.getGValue()) >= 0
                ) {
                    List<ChargeType> charges = chargeService.selectFirstCharge(meterLog.getProjectId());
                    DeviceDataPrice price = createPrice(minData, meterLog, device.getDeviceType(), charges, value);
                   // Point devicePoint = createDevicePoint(device, "device" + value.name(), switchTime(value, offset), minData, meterLog, price);
                    //influxDb.writeSinglePoint(devicePoint, "energy");
                }
            }
           return    meterLogMapper.insert(meterLog)>0;
        }
        return false;
    }

    private DeviceDataPrice createPrice(DeviceData min, MeterLog max, Integer deviceType, List<ChargeType> charges, DateEnum dateEnum) {
        DeviceDataPrice chargeData = new DeviceDataPrice();
        //年度跨过统计
        if (null != charges && CollUtil.isNotEmpty(charges) && dateEnum != Year) {
//            List<ChargeType> data = charges.getData();
            List<ChargeType> collect = charges.stream().filter(d -> d.getDeviceType() == deviceType).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(collect)) {
                ChargeType chargeType = collect.get(0);
                //不采用分时计费，峰平谷配置是要配置null或者0
                if (chargeType.getPrice() != null && chargeType.getPrice().compareTo(new BigDecimal(0)) > 0) {
                    chargeData.setPrice((max.getOldValue().subtract(min.getValue())).multiply(chargeType.getPrice()).setScale(2, RoundingMode.DOWN));
                } else {
                    chargeData.setFPrice((max.getOldFValue().subtract(min.getFValue())).multiply(chargeType.getFPrice()).setScale(2, RoundingMode.DOWN));
                    chargeData.setPPrice((max.getOldPValue().subtract(min.getPValue())).multiply(chargeType.getPPrice()).setScale(2, RoundingMode.DOWN));
                    chargeData.setGPrice((max.getOldGValue().subtract(min.getGValue())).multiply(chargeType.getGPrice()).setScale(2, RoundingMode.DOWN));
                    chargeData.setPrice(chargeData.getFPrice().add(chargeData.getPPrice()).add(chargeData.getGPrice()).setScale(2, RoundingMode.DOWN));
                }
            }
        }
        return chargeData;
    }

    private DeviceData MaxAndMin(Integer id, DateTime now, DateEnum type, String order) {
        DateTime offset = DateUtil.offset(now, DateField.MINUTE, -10);
        String sql = "";
        if (type == Hour) {
            sql = "select * from history_" + offset.year() + " where id='" + id + "' and  time>='" + offset.toString("yyyy-MM-dd HH:00:00") + "' and time<='" + now.toString("yyyy-MM-dd HH:mm:ss") + "' " + order + " limit 1 tz('Asia/Shanghai')";
        } else if (type == Day) {
            sql = "select * from history_" + offset.year() + " where id='" + id + "' and  time>='" + offset.toString("yyyy-MM-dd 00:00:00") + "' and time<='" + now.toString("yyyy-MM-dd HH:mm:ss") + "'   " + order + " limit 1 tz('Asia/Shanghai')";
        } else if (type == Month) {
            sql = "select * from history_" + offset.year() + " where id='" + id + "' and  time>='" + offset.toString("yyyy-MM-01 00:00:00") + "' and time<='" + now.toString("yyyy-MM-dd HH:mm:ss") + "'  " + order + " limit 1 tz('Asia/Shanghai')";
        } else if (type == Year) {
            sql = "select * from history_" + offset.year() + " where id='" + id + "' and  time>='" + offset.toString("yyyy-01-01 00:00:00") + "' and time<='" + now.toString("yyyy-MM-dd HH:mm:ss") + "'  " + order + " limit 1 tz('Asia/Shanghai')";
        }
//        QueryResult result = influxDb.queryResult(sql, "energy");
        DeviceData deviceData = new DeviceData();
//        if (null != result) {
//            if (CollUtil.isNotEmpty(result.getResults()) &&
//                    CollUtil.isNotEmpty(result.getResults().get(0).getSeries()) &&
//                    CollUtil.isNotEmpty(result.getResults().get(0).getSeries().get(0).getValues())) {
//                List<Object> values = result.getResults().get(0).getSeries().get(0).getValues().get(0);
//                if (CollUtil.isNotEmpty(values)) {
//                    deviceData.setValue(BigDecimal.valueOf(values.get(7) == null ? 0 : (double) values.get(7)));
//                    deviceData.setFValue(BigDecimal.valueOf(values.get(1) == null ? 0 : (double) values.get(1)));
//                    deviceData.setPValue(BigDecimal.valueOf(values.get(5) == null ? 0 : (double) values.get(5)));
//                    deviceData.setGValue(BigDecimal.valueOf(values.get(2) == null ? 0 : (double) values.get(2)));
//                    deviceData.setDateTime(values.get(0) == null ? now : DateUtil.parseUTC(values.get(0).toString()));
//                }
//            }
//
//        }
        return deviceData;
    }



}
