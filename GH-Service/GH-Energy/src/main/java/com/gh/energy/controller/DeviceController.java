package com.gh.energy.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import com.gh.basic.feign.client.BasicFeignClient;
import com.gh.basic.feign.model.entity.Area;
import com.gh.common.exception.ExceptionEnum;
import com.gh.common.exception.GhCustomException;
import com.gh.common.exception.SystemEnumMsg;
import com.gh.common.utils.GHResponse;
import com.gh.energy.model.entity.Device;
import com.gh.energy.model.vo.AreaEnergyDevice;
import com.gh.energy.response.PageResult;
import com.gh.energy.service.DeviceService;
import com.gh.log.annotation.OperLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@RequestMapping("/device")
@RestController
@Api(tags = "能耗设备接口")
public class DeviceController {

    @Autowired
    private DeviceService deviceService;

    @Autowired
    private BasicFeignClient basicFeignClient;

    @PostMapping
    @ApiOperation("新增设备")
    @OperLog(category = "能耗设备管理", description = "新增设备")
    @PreAuthorize("hasAnyAuthority('sys_admin','sys_project_admin','sys_edevice_add')")
    public GHResponse Add(@Valid @RequestBody Device device) {
        List<Device> devices = deviceService.lambdaQuery().eq(Device::getProjectId, device.getProjectId())
                .eq(Device::getName, device.getName())
                .eq(Device::getDeviceType, device.getDeviceType())
                .list();
        if (CollUtil.isNotEmpty(devices)) {
            throw new GhCustomException(ExceptionEnum.PROJECT_REPEATE_ADD);
        }
        boolean insert = deviceService.insert(device);
        if (insert) {
            return GHResponse.ok(null, SystemEnumMsg.CREATE_SUCCESS.msg());
        }
        return GHResponse.ok(null, SystemEnumMsg.CREATE_ERROR.msg());
    }

    @PutMapping
    @ApiOperation("修改设备")
    @OperLog(category = "能耗设备管理", description = "修改设备")
    @PreAuthorize("hasAnyAuthority('sys_admin','sys_project_admin','sys_edevice_update')")
    public GHResponse Patch(@Valid @RequestBody Device device) {
        List<Device> devices = deviceService.lambdaQuery().eq(Device::getProjectId, device.getProjectId())
                .eq(Device::getName, device.getName())
                .ne(Device::getId, device.getId())
                .eq(Device::getDeviceType, device.getDeviceType())
                .list();
        if (CollUtil.isNotEmpty(devices)) {
            throw new GhCustomException(ExceptionEnum.PROJECT_REPEATE_ADD);
        }
        boolean update = deviceService.updateById(device);
        if (update) {
            return GHResponse.ok(null, SystemEnumMsg.Update_SUCCESS.msg());
        }
        return GHResponse.ok(null, SystemEnumMsg.Update_ERROR.msg());
    }

    @DeleteMapping
    @ApiOperation("删除设备")
    @OperLog(category = "能耗设备管理", description = "删除设备")
    @PreAuthorize("hasAnyAuthority('sys_admin','sys_project_admin','sys_edevice_del')")
    public GHResponse Delete(@RequestParam Integer[] ids) {

        boolean delete = deviceService.removeByIds(Arrays.asList(ids));
        if (delete) {
            return GHResponse.ok(null, SystemEnumMsg.Delete_SUCCESS.msg());
        }
        return GHResponse.ok(null, SystemEnumMsg.Delete_ERROR.msg());
    }


    @GetMapping
    @ApiOperation("查询设备")
    public GHResponse<List<Device>> Select(Integer id,
                                           String name,
                                           Integer projectId,
                                           Integer type,
                                           String fullPath,
                                           Integer categoryId,
                                           Integer featureId,
                                           Integer size,
                                           Integer page) {
        String areaCode = "", deptCode = "";
        if (null != type && StrUtil.isNotBlank(fullPath)) {
            if (type == 1) {
                areaCode = "%" + fullPath + "%";
            } else if (type == 2) {
                deptCode = "%" + fullPath + "%";
            }
        }
        if(type==3)
        {
            if(StrUtil.isNotEmpty(fullPath))
            {
                type= Convert.toInt(fullPath,0);
            }else
            {
                type=null;
            }
        }else
        {
            type=null;
        }


        PageResult<List<Device>> select = deviceService.select(id, name, projectId, areaCode, deptCode, categoryId, featureId, type, size, page);
        return GHResponse.ok(select.getData(), select.getCount());
    }


    /**
     * @param deviceTypeId 设备台账中的设备类型
     * @return
     */
    @PostMapping("energy")
    @ApiOperation("同步能耗设备到台账")
    public GHResponse Add(@RequestParam Integer deviceTypeId) {

        boolean insert = deviceService.updateToResource(deviceTypeId);
        if (insert) {
            return GHResponse.ok(null, SystemEnumMsg.Update_SUCCESS.msg());
        }
        return GHResponse.ok(null, SystemEnumMsg.Update_ERROR.msg());
    }

    @GetMapping("tree")
    @ApiOperation("查询电表--树")
    public GHResponse SelectDeviceTree(Integer projectId,String keyword) {
        GHResponse<List<Area>> response = basicFeignClient.Selects(null, projectId, null, null, null);
        List<Device> devices = deviceService.lambdaQuery()
                .like(StrUtil.isNotEmpty(keyword),Device::getName,keyword)
                .list();
        return GHResponse.ok(GetParentTreeNode(response.getData(), 0, devices));
    }

    private List<AreaEnergyDevice> GetParentTreeNode(List<Area> areas, Integer parentId, List<Device> devices) {
        List<AreaEnergyDevice> areaList = areas.stream().filter(area -> area.getParentId() != null && area.getParentId().equals(parentId))
                .map(area -> {
                            List<Device> list = devices.stream().filter(device -> device.getAreaId() != null && device.getAreaId().equals(area.getId())).collect(Collectors.toList());
                            AreaEnergyDevice areaEnergyDevice = AreaEnergyDevice.builder().id(area.getId())
                                    .parentId(parentId)
                                    .label(area.getName())
                                    .build();
                            List<AreaEnergyDevice> list1 = list.stream().map(device -> {
                                AreaEnergyDevice energyDevice = AreaEnergyDevice.builder().id(device.getId())
                                        .parentId(parentId)
                                        .code(device.getCode())
                                        .label(device.getName())
                                        .build();
                                return energyDevice;
                            }).collect(Collectors.toList());

                            list1.addAll(GetParentTreeNode(areas, area.getId(), devices));
                            areaEnergyDevice.setChildren(list1);
                            return areaEnergyDevice;
                        }
                ).collect(Collectors.toList());
        return areaList;
    }



}
