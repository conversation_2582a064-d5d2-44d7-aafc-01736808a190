<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gh.energy.mapper.UsageGuideMapper">
    <select id="selectUsageGuide"  resultType="com.gh.energy.model.dto.UsageGuideDto">
        SELECT ghenergy_usageguide.*,areaSize FROM ghenergy_usageguide LEFT JOIN ghops_project ON projectId=ghops_project.id
        <where>
            <if test="id!=null">
                and ghenergy_usageguide.id=#{id}
            </if>
            <if test="name!=null and name!=''">
                and ghenergy_usageguide.name like #{name}
            </if>
        </where>

    </select>

</mapper>
