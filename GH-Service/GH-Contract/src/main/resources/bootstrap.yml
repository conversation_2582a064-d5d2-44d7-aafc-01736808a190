spring:
  application:
    name: contract-service
  cloud:
    nacos:
      server-addr: 36.152.149.51:28848
      config:
        file-extension: yaml
        namespace: 9027158d-ab5d-43f7-a691-3b7546cc79f9
        extension-configs:
          - data-id: redis.yml
          - data-id: mysql.yml
          - data-id: json.yml
          - data-id: swagger.yml
      discovery:
        server-addr: 36.152.149.51:28848
        namespace: 6cf15815-a3cb-4cba-a940-16026af4257c
  mvc:
    servlet:
      load-on-startup: 1
server:
  port: 9029

mybatis-plus:
  global-config:
    db-config:
      update-strategy: ignored
