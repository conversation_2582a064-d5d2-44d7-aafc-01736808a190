<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gh.contract.mapper.ContractTemplateMapper">
    <select id="getContractTemplateList"   resultType="com.gh.contract.model.dto.ContractTemplateDTO">
        SELECT c.*,t.`name` as typeName FROM ghcontract_template c LEFT JOIN ghcontract_type t on c.typeId=t.id
        <where>
            <if test="keyword!=null and keyword!=''">
                and c.name like concat('%',#{keyword},'%')
            </if>
            <if test="projectId!=null">
                and c.projectId=#{projectId}
            </if>
            <if test="typeId!=null">
                and c.typeId=#{typeId}
            </if>
        </where>
    </select>



</mapper>
