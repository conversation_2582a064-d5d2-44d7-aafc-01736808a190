<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gh.contract.mapper.ReceiptMapper">
    <select id="getReceiptList"   resultType="com.gh.contract.model.dto.ReceiptDTO">
        SELECT r.*,a.account,a.bankName,a.taxNumber,a.agencyName,contractName FROM ghcontract_receipt r LEFT JOIN ghcontract_agency a on r.agencyId=a.id

        LEFT JOIN ghcontract_list l on l.contractNo=r.contractCode
        <where>
            <if test="keyword!=null and keyword!=''">
                and a.agencyName like concat('%',#{keyword},'%')
            </if>
            <if test="projectId!=null">
                and r.projectId=#{projectId}
            </if>
            <if test="bt!=null">
                and  r.receiptDate <![CDATA[>=]]> #{bt}
            </if>
            <if test="et!=null">
                and r.receiptDate <![CDATA[<=]]> #{et}
            </if>

        </where>
        order by r.receiptDate desc
    </select>



</mapper>
