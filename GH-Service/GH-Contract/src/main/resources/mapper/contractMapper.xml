<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gh.contract.mapper.ContractMapper">
    <select id="getContractList"   resultType="com.gh.contract.model.dto.ContractDTO">
        SELECT l.* ,d.`NAME` as contractDeptName ,u.`name` as contractPersonName FROM ghcontract_list l  LEFT JOIN ghops_department d on l.contractDepartment=d.id
        LEFT JOIN ghauth_user u on l.contractPerson=u.id

        <where>
            <if test="keyword!=null and keyword!=''">
                and l.contractName like concat('%',#{keyword},'%')
            </if>
            <if test="projectId!=null">
                and l.projectId=#{projectId}
            </if>
            <if test="deptId!=null">
                and l.contractDepartment=#{deptId}
            </if>
        </where>
    </select>



</mapper>
