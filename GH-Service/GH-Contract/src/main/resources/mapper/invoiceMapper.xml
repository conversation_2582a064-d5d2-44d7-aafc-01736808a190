<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gh.contract.mapper.InvoiceMapper">
    <select id="getInvoiceList"   resultType="com.gh.contract.model.dto.InvoiceDTO">
        SELECT i.*,l.contractName,c.bankName as customerBankName,
        c.account as customerAccount,c.company,c.taxNumber
        FROM ghcontract_invoice i LEFT JOIN ghcontract_list l on i.contractCode=l.contractNo
        LEFT JOIN ghcontract_customer c on i.customerId=c.id

        <where>
            <if test="keyword!=null and keyword!=''">
                and c.company like concat('%',#{keyword},'%')
            </if>
            <if test="projectId!=null">
                and i.projectId=#{projectId}
            </if>
            <if test="bt!=null">
                and  i.invoiceDate <![CDATA[>=]]> #{bt}
            </if>
            <if test="et!=null">
                and i.invoiceDate <![CDATA[<=]]> #{et}
            </if>

        </where>
        order by i.invoiceDate desc
    </select>



</mapper>
