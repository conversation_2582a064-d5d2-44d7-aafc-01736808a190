package com.gh.contract.model.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Builder;
import lombok.Data;

@Builder
@Data
@TableName("GHContract_Agency")
public class Agency {

    @TableId(type = IdType.AUTO)
    private Integer id;

    private Integer projectId;

    //单位名称
    private String agencyName;
    //税号
    private String taxNumber;
    //开户行
    private String bankName;
    //账号
    private String account;
    //账户名
    private String accountName;
    //备注
    private String remark;


}
