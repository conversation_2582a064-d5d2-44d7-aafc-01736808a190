package com.gh.contract.controller;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ArrayUtil;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gh.common.exception.ExceptionEnum;
import com.gh.common.exception.GhCustomException;
import com.gh.common.exception.SystemEnumMsg;
import com.gh.common.utils.GHResponse;
import com.gh.common.utils.PageResult;
import com.gh.contract.model.entity.Agency;
import com.gh.contract.service.AgencyService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Arrays;
import java.util.List;

@RestController
@Api("机构单位")
@RequestMapping("/agency")
public class AgencyController {

    @Autowired
    private AgencyService service;

    @PostMapping
    @ApiOperation("添加机构单位")
    public GHResponse Add(@RequestBody @Valid Agency agency) {
        List<Agency> types = service.lambdaQuery().eq(Agency::getAgencyName, agency.getAgencyName())
                .eq(Agency::getProjectId, agency.getProjectId()).list();
        if (CollUtil.isNotEmpty(types)) {
            throw new GhCustomException(ExceptionEnum.PROJECT_REPEATE_ADD);
        }

        boolean insert = service.save(agency);

        if (insert) {
            return GHResponse.ok(null, SystemEnumMsg.CREATE_SUCCESS.msg());
        }

        return GHResponse.failed(SystemEnumMsg.CREATE_ERROR.msg());
    }

    @PutMapping
    @ApiOperation("更新机构单位")
    public GHResponse Patch(@RequestBody @Valid Agency agency) {
        List<Agency> groups = service.lambdaQuery().eq(Agency::getAgencyName, agency.getAgencyName())
                .eq(Agency::getProjectId, agency.getProjectId())
                .ne(Agency::getId, agency.getId())
                .list();
        if (CollUtil.isNotEmpty(groups)) {
            throw new GhCustomException(ExceptionEnum.PROJECT_REPEATE_ADD);
        }
        if (agency.getId() > 0) {
            boolean update = service.updateById(agency);
            if (update) {
                return GHResponse.ok();
            }

        }
        return GHResponse.failed(SystemEnumMsg.Update_ERROR.msg());
    }

    @DeleteMapping
    @ApiOperation("删除机构单位")
    public GHResponse Delete(@RequestParam Integer[] ids)
    {

        if(ArrayUtil.isNotEmpty(ids))
        {
            boolean delete = service.removeByIds(Arrays.asList(ids));
            if(delete)
            {
                return GHResponse.ok();
            }
        }

        return GHResponse.failed(SystemEnumMsg.Delete_ERROR.msg());
    }

    @GetMapping
    @ApiOperation("查询机构单位")
    public GHResponse<List<Agency>> typeList(Integer projectId,String keyword ,Integer page, Integer size)
    {
        LambdaQueryChainWrapper<Agency> wrapper = service.lambdaQuery().eq(Agency::getProjectId, projectId)
                .like(keyword != null, Agency::getAgencyName, keyword);
        if(PageResult.isPage(page,size))
        {
            Page<Agency> page1 = new Page<>(page, size);
            wrapper.page(page1);
            return GHResponse.ok(page1.getRecords(),page1.getTotal());
        }else
        {
            List<Agency> list = wrapper.list();
            return GHResponse.ok(list);
        }


    }

    @GetMapping("id")
    @ApiOperation("查询机构单位详情")
    public GHResponse agency(Integer id)
    {
        Agency one = service.lambdaQuery().eq(Agency::getId, id)
                .oneOpt()
                .orElse(null);
        return GHResponse.ok(one);

    }


}
