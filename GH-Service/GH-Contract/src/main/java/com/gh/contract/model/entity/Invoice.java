package com.gh.contract.model.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;


//合同开票
@Builder
@Data
@TableName("GHContract_Invoice")
public class Invoice {

    @TableId(type = IdType.AUTO)
    private Integer id;

    private Integer projectId;

    //合同信息
    private String contractCode;
    //开票日期
    private Date invoiceDate;
    //开票单位
    private Integer agencyId;
    //发票类型
    private Integer invoiceType;
    //公司主体
    private Integer customerId;
    //本次开票金额
    private BigDecimal amount;
    //邮寄姓名
    private String name;
    //邮寄电话
    private String phone;
    //邮寄地址
    private String address;


}
