package com.gh.contract.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.gh.common.utils.PageResult;
import com.gh.contract.model.dto.InvoiceDTO;
import com.gh.contract.model.entity.Invoice;

import java.util.Date;
import java.util.List;

public interface InvoiceService extends IService<Invoice> {

    PageResult<List<InvoiceDTO>> getInvoiceList(Integer page,Integer size, String keyword, Integer projectId, Date bt, Date et);


}
