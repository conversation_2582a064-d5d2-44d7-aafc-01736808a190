package com.gh.contract.model.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Builder;
import lombok.Data;

@Builder
@Data
@TableName("GHContract_Customer")
public class Customer {

    @TableId(type = IdType.AUTO)
    private Integer id;

    private Integer projectId;

    //公司主体联系人
    private String name;
    //税号
    private String taxNumber;
    //开户行
    private String bankName;
    //账号
    private String account;
    //公司主体
    private String company;
    //公司主题联系人号码
    private String phone;


}
