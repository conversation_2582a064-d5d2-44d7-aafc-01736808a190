package com.gh.contract.controller;


import cn.hutool.core.util.ArrayUtil;
import com.gh.common.exception.SystemEnumMsg;
import com.gh.common.utils.GHResponse;
import com.gh.common.utils.PageResult;
import com.gh.contract.model.dto.ReceiptDTO;
import com.gh.contract.model.entity.Receipt;
import com.gh.contract.service.ReceiveService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

@RestController
@Api("合同收票")
@RequestMapping("/receipt")
public class ReceiptController {

    @Autowired
    private ReceiveService service;

    @PostMapping
    @ApiOperation("添加合同收票")
    public GHResponse Add(@RequestBody @Valid Receipt Receipt) {
        boolean insert = service.save(Receipt);

        if (insert) {
            return GHResponse.ok(null, SystemEnumMsg.CREATE_SUCCESS.msg());
        }

        return GHResponse.failed(SystemEnumMsg.CREATE_ERROR.msg());
    }

    @PutMapping
    @ApiOperation("更新合同收票")
    public GHResponse Patch(@RequestBody @Valid Receipt receipt) {
        if (receipt.getId() > 0) {
            boolean update = service.updateById(receipt);
            if (update) {
                return GHResponse.ok();
            }

        }
        return GHResponse.failed(SystemEnumMsg.Update_ERROR.msg());
    }

    @DeleteMapping
    @ApiOperation("删除合同收票")
    public GHResponse Delete(@RequestParam Integer[] ids)
    {

        if(ArrayUtil.isNotEmpty(ids))
        {
            boolean delete = service.removeByIds(Arrays.asList(ids));
            if(delete)
            {
                return GHResponse.ok();
            }
        }

        return GHResponse.failed(SystemEnumMsg.Delete_ERROR.msg());
    }

    @GetMapping
    @ApiOperation("查询合同收票")
    public GHResponse<List<ReceiptDTO>> typeList(Integer projectId, String keyword , Integer page, Integer size,
                                                 @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date bt,
                                                    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date et)
    {
        PageResult<List<ReceiptDTO>> list = service.getReceiptList(page, size, keyword, projectId, bt, et);
        return GHResponse.ok(list.getData(),list.getTotal());
    }

    @GetMapping("id")
    @ApiOperation("查询合同收票详情")
    public GHResponse Receipt(Integer id)
    {
        Receipt one = service.lambdaQuery().eq(Receipt::getId, id)
                .oneOpt()
                .orElse(null);
        return GHResponse.ok(one);

    }


}
