
package com.gh.contract.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gh.contract.model.dto.InvoiceDTO;
import com.gh.contract.model.entity.Invoice;

import java.util.Date;
import java.util.List;

public interface InvoiceMapper extends BaseMapper<Invoice> {

    List<InvoiceDTO> getInvoiceList(Page page, String keyword, Integer projectId, Date bt,Date et);


}
