package com.gh.contract.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gh.common.utils.PageResult;
import com.gh.contract.mapper.InvoiceMapper;
import com.gh.contract.model.dto.InvoiceDTO;
import com.gh.contract.model.entity.Invoice;
import com.gh.contract.service.InvoiceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * Description:
 * User: zhangkeguang
 * Date: 2024-05-10-09:24
 */

@Service
public class InvoiceServiceImpl extends ServiceImpl<InvoiceMapper, Invoice> implements InvoiceService {

    @Autowired
    private InvoiceMapper mapper;


    @Override
    public PageResult<List<InvoiceDTO>> getInvoiceList(Integer page, Integer size, String keyword, Integer projectId, Date bt, Date et) {
        Page page1 = null;
        if(PageResult.isPage(page,size)){
            page1 = new Page(page,size);
        }
        List<InvoiceDTO> contractTemplateList = mapper.getInvoiceList(page1, keyword, projectId, bt, et);

        return PageResult.<List<InvoiceDTO>>
                builder().data(contractTemplateList).total(page1==null?contractTemplateList.size():page1.getTotal()).build();

    }
}
