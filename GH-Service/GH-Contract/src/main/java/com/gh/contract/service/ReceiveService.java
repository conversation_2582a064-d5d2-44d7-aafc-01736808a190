package com.gh.contract.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.gh.common.utils.PageResult;
import com.gh.contract.model.dto.ReceiptDTO;
import com.gh.contract.model.entity.Receipt;

import java.util.Date;
import java.util.List;

public interface ReceiveService extends IService<Receipt> {

    PageResult<List<ReceiptDTO>> getReceiptList(Integer page, Integer size, String keyword, Integer projectId, Date bt, Date et);


}
