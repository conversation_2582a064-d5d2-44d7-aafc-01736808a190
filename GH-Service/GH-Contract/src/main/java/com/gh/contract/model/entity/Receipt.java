package com.gh.contract.model.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;


//合同开票
@Builder
@Data
@TableName("GHContract_Receipt")
public class Receipt {

    @TableId(type = IdType.AUTO)
    private Integer id;
    private Integer projectId;
    private String  contractCode;
    private Date receiptDate;
    private Integer   receiptType;
    private BigDecimal amount;
    private Integer  agencyId;
    private String remark;

}
