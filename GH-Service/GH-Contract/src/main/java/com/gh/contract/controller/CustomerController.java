package com.gh.contract.controller;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ArrayUtil;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gh.common.exception.ExceptionEnum;
import com.gh.common.exception.GhCustomException;
import com.gh.common.exception.SystemEnumMsg;
import com.gh.common.utils.GHResponse;
import com.gh.common.utils.PageResult;
import com.gh.contract.model.entity.Customer;
import com.gh.contract.service.CustomerService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Arrays;
import java.util.List;

@RestController
@Api("客户供应商")
@RequestMapping("/customer")
public class CustomerController {

    @Autowired
    private CustomerService service;

    @PostMapping
    @ApiOperation("添加客户供应商")
    public GHResponse Add(@RequestBody @Valid Customer customer) {
        List<Customer> types = service.lambdaQuery().eq(Customer::getCompany, customer.getCompany())
                .eq(Customer::getProjectId, customer.getProjectId()).list();
        if (CollUtil.isNotEmpty(types)) {
            throw new GhCustomException(ExceptionEnum.PROJECT_REPEATE_ADD);
        }

        boolean insert = service.save(customer);

        if (insert) {
            return GHResponse.ok(null, SystemEnumMsg.CREATE_SUCCESS.msg());
        }

        return GHResponse.failed(SystemEnumMsg.CREATE_ERROR.msg());
    }

    @PutMapping
    @ApiOperation("更新客户供应商")
    public GHResponse Patch(@RequestBody @Valid Customer customer) {
        List<Customer> groups = service.lambdaQuery().eq(Customer::getCompany, customer.getCompany())
                .eq(Customer::getProjectId, customer.getProjectId())
                .ne(Customer::getId, customer.getId())
                .list();
        if (CollUtil.isNotEmpty(groups)) {
            throw new GhCustomException(ExceptionEnum.PROJECT_REPEATE_ADD);
        }
        if (customer.getId() > 0) {
            boolean update = service.updateById(customer);
            if (update) {
                return GHResponse.ok();
            }

        }
        return GHResponse.failed(SystemEnumMsg.Update_ERROR.msg());
    }

    @DeleteMapping
    @ApiOperation("删除客户供应商")
    public GHResponse Delete(@RequestParam Integer[] ids)
    {

        if(ArrayUtil.isNotEmpty(ids))
        {
            boolean delete = service.removeByIds(Arrays.asList(ids));
            if(delete)
            {
                return GHResponse.ok();
            }
        }

        return GHResponse.failed(SystemEnumMsg.Delete_ERROR.msg());
    }

    @GetMapping
    @ApiOperation("查询客户供应商")
    public GHResponse<List<Customer>> typeList(Integer projectId,String keyword ,Integer page, Integer size)
    {
        LambdaQueryChainWrapper<Customer> wrapper = service.lambdaQuery().eq(Customer::getProjectId, projectId)
                .like(keyword != null, Customer::getCompany, keyword);
        if(PageResult.isPage(page,size))
        {
            Page<Customer> page1 = new Page<>(page, size);
            wrapper.page(page1);
            return GHResponse.ok(page1.getRecords(),page1.getTotal());
        }else
        {
            List<Customer> list = wrapper.list();
            return GHResponse.ok(list);
        }


    }

    @GetMapping("id")
    @ApiOperation("查询客户供应商详情")
    public GHResponse customer(Integer id)
    {
        Customer one = service.lambdaQuery().eq(Customer::getId, id)
                .oneOpt()
                .orElse(null);
        return GHResponse.ok(one);

    }


}
