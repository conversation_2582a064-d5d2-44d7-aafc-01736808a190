package com.gh.contract.model.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Builder
@Data
@TableName("GHContract_List")
public class Contract {

    @TableId(type = IdType.AUTO)
    private Integer id;

    private Integer projectId;

    //合同编号
    private String contractNo;
    //合同名称
    private String contractName;
    //合同金额
    private BigDecimal contractAmount;
    //所属部门
    private Integer contractDepartment;
    //签约日期
    private Date contractDate;
    //签约人员
    private Integer contractPerson;
    //合同状态
    private Integer contractStatus;
    //备注
    private String contractRemark;
    //对方签约主体
    private String contractParty;
    //对方签约编码
    private String contractPartyCode;
    //对方法人代表
    private String contractPartyLegal;
    //对方联系人
    private String contractPartyContact;
    //对方联系方式
    private String contractPartyPhone;
    //对方联系地址
    private String contractPartyAddress;
    //对方开户行
    private String contractPartyBank;
    //对方银行账号
    private String contractPartyBankAccount;
    //合同附件
    private String contractFiles;


}
