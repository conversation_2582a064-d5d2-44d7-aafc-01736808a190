package com.gh.contract.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gh.common.utils.PageResult;
import com.gh.contract.mapper.ContractTemplateMapper;
import com.gh.contract.mapper.ContractTypeMapper;
import com.gh.contract.model.dto.ContractTemplateDTO;
import com.gh.contract.model.entity.ContractType;
import com.gh.contract.service.ContractTypeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Description:
 * User: zhangkeguang
 * Date: 2024-05-10-09:24
 */

@Service
public class ContractTypeServiceImpl extends ServiceImpl<ContractTypeMapper, ContractType> implements ContractTypeService {

}
