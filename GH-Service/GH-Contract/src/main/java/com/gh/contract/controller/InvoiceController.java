package com.gh.contract.controller;


import cn.hutool.core.util.ArrayUtil;
import com.gh.common.exception.SystemEnumMsg;
import com.gh.common.utils.GHResponse;
import com.gh.common.utils.PageResult;
import com.gh.contract.model.dto.InvoiceDTO;
import com.gh.contract.model.entity.Invoice;
import com.gh.contract.service.InvoiceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

@RestController
@Api("合同开票")
@RequestMapping("/invoice")
public class InvoiceController {

    @Autowired
    private InvoiceService service;

    @PostMapping
    @ApiOperation("添加合同开票")
    public GHResponse Add(@RequestBody @Valid Invoice invoice) {
        boolean insert = service.save(invoice);

        if (insert) {
            return GHResponse.ok(null, SystemEnumMsg.CREATE_SUCCESS.msg());
        }

        return GHResponse.failed(SystemEnumMsg.CREATE_ERROR.msg());
    }

    @PutMapping
    @ApiOperation("更新合同开票")
    public GHResponse Patch(@RequestBody @Valid Invoice invoice) {
        if (invoice.getId() > 0) {
            boolean update = service.updateById(invoice);
            if (update) {
                return GHResponse.ok();
            }

        }
        return GHResponse.failed(SystemEnumMsg.Update_ERROR.msg());
    }

    @DeleteMapping
    @ApiOperation("删除合同开票")
    public GHResponse Delete(@RequestParam Integer[] ids)
    {

        if(ArrayUtil.isNotEmpty(ids))
        {
            boolean delete = service.removeByIds(Arrays.asList(ids));
            if(delete)
            {
                return GHResponse.ok();
            }
        }

        return GHResponse.failed(SystemEnumMsg.Delete_ERROR.msg());
    }

    @GetMapping
    @ApiOperation("查询合同开票")
    public GHResponse<List<InvoiceDTO>> typeList(Integer projectId, String keyword , Integer page, Integer size,
                                                 @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date bt,
                                                    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date et)
    {
        PageResult<List<InvoiceDTO>> list = service.getInvoiceList(page, size, keyword, projectId, bt, et);
        return GHResponse.ok(list.getData(),list.getTotal());
    }

    @GetMapping("id")
    @ApiOperation("查询合同开票详情")
    public GHResponse invoice(Integer id)
    {
        Invoice one = service.lambdaQuery().eq(Invoice::getId, id)
                .oneOpt()
                .orElse(null);
        return GHResponse.ok(one);

    }


}
