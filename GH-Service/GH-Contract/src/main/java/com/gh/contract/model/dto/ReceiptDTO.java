package com.gh.contract.model.dto;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;


//合同开票
@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ReceiptDTO {

    @TableId(type = IdType.AUTO)
    private Integer id;
    private Integer projectId;
    private String  contractCode;
    private Date receiptDate;
    private Integer   receiptType;
    private BigDecimal amount;
    private Integer  agencyId;
    private String remark;


    private String  contractName;

    private String taxNumber;
    private String agencyName;

    private BigDecimal account;
    private String bankName;

}
