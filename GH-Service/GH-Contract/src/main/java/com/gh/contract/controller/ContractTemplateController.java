package com.gh.contract.controller;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ArrayUtil;
import com.gh.common.exception.ExceptionEnum;
import com.gh.common.exception.GhCustomException;
import com.gh.common.exception.SystemEnumMsg;
import com.gh.common.utils.GHResponse;
import com.gh.common.utils.PageResult;
import com.gh.contract.model.dto.ContractTemplateDTO;
import com.gh.contract.model.entity.ContractTemplate;
import com.gh.contract.service.ContractTemplateService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Arrays;
import java.util.List;

@RestController
@Api("合同模版管理")
@RequestMapping("/template")
public class ContractTemplateController {

    @Autowired
    private ContractTemplateService service;

    @PostMapping
    @ApiOperation("添加合同模版")
    public GHResponse Add(@RequestBody @Valid ContractTemplate template) {
        List<ContractTemplate> types = service.lambdaQuery().eq(ContractTemplate::getName, template.getName())
                .eq(ContractTemplate::getProjectId, template.getProjectId()).list();
        if (CollUtil.isNotEmpty(types)) {
            throw new GhCustomException(ExceptionEnum.PROJECT_REPEATE_ADD);
        }

        boolean insert = service.save(template);

        if (insert) {
            return GHResponse.ok(null, SystemEnumMsg.CREATE_SUCCESS.msg());
        }

        return GHResponse.failed(SystemEnumMsg.CREATE_ERROR.msg());
    }

    @PutMapping
    @ApiOperation("更新合同模版")
    public GHResponse Patch(@RequestBody @Valid ContractTemplate template) {
        List<ContractTemplate> groups = service.lambdaQuery().eq(ContractTemplate::getName, template.getName())
                .eq(ContractTemplate::getProjectId, template.getProjectId())
                .ne(ContractTemplate::getId, template.getId())
                .list();
        if (CollUtil.isNotEmpty(groups)) {
            throw new GhCustomException(ExceptionEnum.PROJECT_REPEATE_ADD);
        }
        if (template.getId() > 0) {
            boolean update = service.updateById(template);
            if (update) {
                return GHResponse.ok();
            }

        }
        return GHResponse.failed(SystemEnumMsg.Update_ERROR.msg());
    }

    @DeleteMapping
    @ApiOperation("删除合同模版")
    public GHResponse Delete(@RequestParam Integer[] ids)
    {

        if(ArrayUtil.isNotEmpty(ids))
        {
            boolean delete = service.removeByIds(Arrays.asList(ids));
            if(delete)
            {
                return GHResponse.ok();
            }
        }

        return GHResponse.failed(SystemEnumMsg.Delete_ERROR.msg());
    }

    @GetMapping
    @ApiOperation("查询合同模版")
    public GHResponse<List<ContractTemplateDTO>> typeList(Integer projectId,String keyword ,Integer page, Integer size,Integer typeId)
    {


        PageResult<List<ContractTemplateDTO>> list = service.getContractTemplateList(page, size, keyword, projectId, typeId);
        return GHResponse.ok(list.getData(),list.getTotal());


    }

    @GetMapping("id")
    @ApiOperation("查询合同模版详情")
    public GHResponse template(Integer id)
    {
        ContractTemplate one = service.lambdaQuery().eq(ContractTemplate::getId, id)
                .oneOpt()
                .orElse(null);
        return GHResponse.ok(one);

    }


}
