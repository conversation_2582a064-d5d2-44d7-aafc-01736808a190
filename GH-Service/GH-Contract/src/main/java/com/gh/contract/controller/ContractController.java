package com.gh.contract.controller;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ArrayUtil;
import com.gh.common.exception.ExceptionEnum;
import com.gh.common.exception.GhCustomException;
import com.gh.common.exception.SystemEnumMsg;
import com.gh.common.utils.GHResponse;
import com.gh.common.utils.PageResult;
import com.gh.contract.model.dto.ContractDTO;
import com.gh.contract.model.entity.Contract;
import com.gh.contract.service.ContractService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Arrays;
import java.util.List;

@RestController
@Api("合同台账合同管理")
@RequestMapping("/contract")
public class ContractController {

    @Autowired
    private ContractService service;

    @PostMapping
    @ApiOperation("添加合同")
    public GHResponse Add(@RequestBody @Valid Contract contract) {
        List<Contract> contracts = service.lambdaQuery().eq(Contract::getContractName, contract.getContractName())
                .eq(Contract::getProjectId, contract.getProjectId()).list();
        if (CollUtil.isNotEmpty(contracts)) {
            throw new GhCustomException(ExceptionEnum.PROJECT_REPEATE_ADD);
        }

        boolean insert = service.save(contract);

        if (insert) {
            return GHResponse.ok(null, SystemEnumMsg.CREATE_SUCCESS.msg());
        }

        return GHResponse.failed(SystemEnumMsg.CREATE_ERROR.msg());
    }

    @PutMapping
    @ApiOperation("更新合同")
    public GHResponse Patch(@RequestBody @Valid Contract contract) {
        List<Contract> groups = service.lambdaQuery().eq(Contract::getContractName, contract.getContractName())
                .eq(Contract::getProjectId, contract.getProjectId())
                .ne(Contract::getId, contract.getId())
                .list();
        if (CollUtil.isNotEmpty(groups)) {
            throw new GhCustomException(ExceptionEnum.PROJECT_REPEATE_ADD);
        }
        if (contract.getId() > 0) {
            boolean update = service.updateById(contract);
            if (update) {
                return GHResponse.ok();
            }

        }
        return GHResponse.failed(SystemEnumMsg.Update_ERROR.msg());
    }

    @DeleteMapping
    @ApiOperation("删除合同")
    public GHResponse Delete(@RequestParam Integer[] ids)
    {

        if(ArrayUtil.isNotEmpty(ids))
        {
            boolean delete = service.removeByIds(Arrays.asList(ids));
            if(delete)
            {
                return GHResponse.ok();
            }
        }

        return GHResponse.failed(SystemEnumMsg.Delete_ERROR.msg());
    }

    @GetMapping
    @ApiOperation("查询合同")
    public GHResponse<List<ContractDTO>> list(Integer projectId,String keyword ,Integer page, Integer size,Integer deptId)
    {

        PageResult<List<ContractDTO>> list = service.getContractList(page, size, keyword, projectId, deptId);
        return GHResponse.ok(list.getData(),list.getTotal());

    }

    @GetMapping("id")
    @ApiOperation("查询合同")
    public GHResponse contract(Integer id)
    {
        Contract one = service.lambdaQuery().eq(Contract::getId, id)
                .oneOpt()
                .orElse(null);
        return GHResponse.ok(one);

    }


}
