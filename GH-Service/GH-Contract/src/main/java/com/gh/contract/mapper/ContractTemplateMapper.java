package com.gh.contract.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gh.contract.model.dto.ContractTemplateDTO;
import com.gh.contract.model.entity.ContractTemplate;

import java.util.List;

public interface ContractTemplateMapper extends BaseMapper<ContractTemplate> {

    List<ContractTemplateDTO> getContractTemplateList(Page page,String keyword,Integer projectId,Integer typeId);
}
