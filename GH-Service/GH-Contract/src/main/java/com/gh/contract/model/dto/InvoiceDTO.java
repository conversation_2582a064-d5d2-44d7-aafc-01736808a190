package com.gh.contract.model.dto;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;


//合同开票
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class InvoiceDTO {

    @TableId(type = IdType.AUTO)
    private Integer id;

    private Integer projectId;

    //合同信息
    private String contractCode;
    //开票日期
    private Date invoiceDate;
    //开票单位
    private Integer agencyId;
    //发票类型
    private Integer invoiceType;
    //公司主体
    private Integer customerId;
    //本次开票金额
    private BigDecimal amount;
    //邮寄姓名
    private String name;
    //邮寄电话
    private String phone;
    //邮寄地址
    private String address;

    //开票对象信息
    //合同名称
    private String contractName;
    //开票对象开户行
    private String customerBankName;
    //开票对象银行账号
    private String customerAccount;
    //开票对象公司
    private String company;
    //开票对象税号
    private String taxNumber;


}
