package com.gh.contract.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gh.contract.model.dto.ContractDTO;
import com.gh.contract.model.entity.Contract;

import java.util.List;

public interface ContractMapper extends BaseMapper<Contract> {

    List<ContractDTO> getContractList(Page page,String keyword,Integer projectId,Integer deptId);

}
