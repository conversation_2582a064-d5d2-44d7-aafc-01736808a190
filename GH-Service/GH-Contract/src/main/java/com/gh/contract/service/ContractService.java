package com.gh.contract.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.gh.common.utils.PageResult;
import com.gh.contract.model.dto.ContractDTO;
import com.gh.contract.model.entity.Contract;

import java.util.List;

public interface ContractService extends IService<Contract> {


    PageResult<List<ContractDTO>> getContractList(Integer page, Integer size, String keyword, Integer projectId, Integer deptId);



}
