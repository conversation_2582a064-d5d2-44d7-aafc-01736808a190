package com.gh.contract.controller;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ArrayUtil;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gh.common.exception.ExceptionEnum;
import com.gh.common.exception.GhCustomException;
import com.gh.common.exception.SystemEnumMsg;
import com.gh.common.utils.GHResponse;
import com.gh.common.utils.PageResult;
import com.gh.contract.model.entity.ContractType;
import com.gh.contract.service.ContractTypeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Arrays;
import java.util.List;

@RestController
@Api("合同模版类型管理")
@RequestMapping("/type")
public class ContractTypeController {

    @Autowired
    private ContractTypeService service;

    @PostMapping
    @ApiOperation("添加类型")
    public GHResponse Add(@RequestBody @Valid ContractType type) {
        List<ContractType> types = service.lambdaQuery().eq(ContractType::getName, type.getName())
                .eq(ContractType::getProjectId, type.getProjectId()).list();
        if (CollUtil.isNotEmpty(types)) {
            throw new GhCustomException(ExceptionEnum.PROJECT_REPEATE_ADD);
        }

        boolean insert = service.save(type);

        if (insert) {
            return GHResponse.ok(null, SystemEnumMsg.CREATE_SUCCESS.msg());
        }

        return GHResponse.failed(SystemEnumMsg.CREATE_ERROR.msg());
    }

    @PutMapping
    @ApiOperation("更新类型")
    public GHResponse Patch(@RequestBody @Valid ContractType type) {
        List<ContractType> groups = service.lambdaQuery().eq(ContractType::getName, type.getName())
                .eq(ContractType::getProjectId, type.getProjectId())
                .ne(ContractType::getId, type.getId())
                .list();
        if (CollUtil.isNotEmpty(groups)) {
            throw new GhCustomException(ExceptionEnum.PROJECT_REPEATE_ADD);
        }
        if (type.getId() > 0) {
            boolean update = service.updateById(type);
            if (update) {
                return GHResponse.ok();
            }

        }
        return GHResponse.failed(SystemEnumMsg.Update_ERROR.msg());
    }

    @DeleteMapping
    @ApiOperation("删除合同模版类型")
    public GHResponse Delete(@RequestParam Integer[] ids)
    {

        if(ArrayUtil.isNotEmpty(ids))
        {
            boolean delete = service.removeByIds(Arrays.asList(ids));
            if(delete)
            {
                return GHResponse.ok();
            }
        }

        return GHResponse.failed(SystemEnumMsg.Delete_ERROR.msg());
    }

    @GetMapping
    @ApiOperation("查询模版类型")
    public GHResponse<List<ContractType>> typeList(Integer projectId,String keyword ,Integer page, Integer size)
    {
        LambdaQueryChainWrapper<ContractType> wrapper = service.lambdaQuery().eq(ContractType::getProjectId, projectId)
                .like(keyword != null, ContractType::getName, keyword);
        if(PageResult.isPage(page,size))
        {
            Page<ContractType> page1 = new Page<>(page, size);
            wrapper.page(page1);
            return GHResponse.ok(page1.getRecords(),page1.getTotal());
        }else
        {
            List<ContractType> list = wrapper.list();
            return GHResponse.ok(list);
        }


    }

    @GetMapping("id")
    @ApiOperation("查询模版类型详情")
    public GHResponse type(Integer id)
    {
        ContractType one = service.lambdaQuery().eq(ContractType::getId, id)
                .oneOpt()
                .orElse(null);
        return GHResponse.ok(one);

    }


}
