package com.gh.contract.model.entity;


import com.baomidou.mybatisplus.annotation.*;
import lombok.Builder;
import lombok.Data;

import java.util.Date;

@Builder
@Data
@TableName("GHContract_Template")
public class ContractTemplate {

    @TableId(type = IdType.AUTO)
    private Integer id;

    private String name;

    private String description;

    private Integer projectId;

    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    private Integer typeId;


    private String path;





}
