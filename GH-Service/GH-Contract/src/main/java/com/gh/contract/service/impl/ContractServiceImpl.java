package com.gh.contract.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gh.common.utils.PageResult;
import com.gh.contract.mapper.ContractMapper;
import com.gh.contract.model.dto.ContractDTO;
import com.gh.contract.model.entity.Contract;
import com.gh.contract.service.ContractService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Description:
 * User: zhangkeguang
 * Date: 2024-05-10-09:24
 */

@Service
public class ContractServiceImpl extends ServiceImpl<ContractMapper, Contract> implements ContractService {


    @Autowired
    private ContractMapper mapper;


    @Override
    public PageResult<List<ContractDTO>> getContractList(Integer page, Integer size, String keyword, Integer projectId, Integer deptId) {
        Page page1 = null;
        if(PageResult.isPage(page,size)){
            page1 = new Page(page,size);
        }
        List<ContractDTO> contractList = mapper.getContractList(page1, keyword, projectId, deptId);

        return PageResult.<List<ContractDTO>>
                builder().data(contractList).total(page1==null?contractList.size():page1.getTotal()).build();

    }
}
