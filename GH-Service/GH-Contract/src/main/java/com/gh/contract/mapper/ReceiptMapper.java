
package com.gh.contract.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gh.contract.model.dto.ReceiptDTO;
import com.gh.contract.model.entity.Receipt;

import java.util.Date;
import java.util.List;

public interface ReceiptMapper extends BaseMapper<Receipt> {

    List<ReceiptDTO> getReceiptList(Page page, String keyword, Integer projectId, Date bt, Date et);


}
