package com.gh.contract.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.gh.common.utils.PageResult;
import com.gh.contract.model.dto.ContractTemplateDTO;
import com.gh.contract.model.entity.ContractTemplate;

import java.util.List;

public interface ContractTemplateService extends IService<ContractTemplate> {
    PageResult<List<ContractTemplateDTO>> getContractTemplateList(Integer page, Integer size, String keyword, Integer projectId, Integer typeId);

}
