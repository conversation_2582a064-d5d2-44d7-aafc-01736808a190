package com.gh.contract.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gh.common.utils.PageResult;
import com.gh.contract.mapper.ContractTemplateMapper;
import com.gh.contract.model.dto.ContractTemplateDTO;
import com.gh.contract.model.entity.ContractTemplate;
import com.gh.contract.service.ContractTemplateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Description:
 * User: zhangkeguang
 * Date: 2024-05-10-09:24
 */

@Service
public class ContractTemplateServiceImpl extends ServiceImpl<ContractTemplateMapper, ContractTemplate> implements ContractTemplateService {


    @Autowired
    private ContractTemplateMapper mapper;
    @Override
    public PageResult<List<ContractTemplateDTO>> getContractTemplateList(Integer page, Integer size, String keyword, Integer projectId, Integer typeId) {

        Page page1 = null;
        if(PageResult.isPage(page,size)){
            page1 = new Page(page,size);
        }
        List<ContractTemplateDTO> contractTemplateList = mapper.getContractTemplateList(page1, keyword, projectId, typeId);

        return PageResult.<List<ContractTemplateDTO>>
                builder().data(contractTemplateList).total(page1==null?contractTemplateList.size():page1.getTotal()).build();

    }

}
