package com.gh.user.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Pattern;
import java.util.Date;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("user")
public class AdminUserVo {

    private Integer id;
    @ApiModelProperty("登录的用户名")
    @NotEmpty(message = "登录名称不能为空")
    private String  userName;

    @NotEmpty(message = "用户名不能为空！")
    @ApiModelProperty("用户名")
    private String name;

    @ApiModelProperty("用户状态")
    private boolean status=true;
    @ApiModelProperty("第三方标识，无需传递")
    private String openId;
    @ApiModelProperty("是否删除，无需传递")
    private boolean remove;
    @ApiModelProperty("创建时间，无需传递")
    private Date createTime;
    @ApiModelProperty("更新时间，无需传递")
    private Date updateTime;
    @javax.validation.constraints.Email(message = "邮箱格式不正确！")
    private String email;
    @Pattern(regexp = "^1[3456789]\\d{9}$",message = "手机号码格式不正确！")
    private String phone;
    /**
     * 0 只读 1 写  2 读写
     */
    @Range(min = 0,max = 2,message = "Level取值范围0-2")
    @ApiModelProperty("0 只读 1 写  2 读写")
    private Integer level;

    @ApiModelProperty("创建人员")
    private String creator;

//    private List<Integer> projectId;
    private Integer projectId;
    private List<Integer> roleId;
    private Integer deptId;

    @NotEmpty(message = "pwd不能为空")
    private String pwd;

    @NotEmpty(message = "confirmPwd不能为空")
    private String confirmPwd;

}
