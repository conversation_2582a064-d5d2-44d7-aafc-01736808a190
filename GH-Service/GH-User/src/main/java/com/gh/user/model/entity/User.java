package com.gh.user.model.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@TableName("GHAuth_User")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class User {
    @TableId(type = IdType.AUTO)
    private Integer id;
    private String  userName;
    private String name;
    private boolean status=true;
    private String openId;
    private boolean remove;
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
    private String email;
    private String phone;
    @JsonIgnore
    private byte[] salt;
    @JsonIgnore
    private String password;

    /**
     * 0 只读 1 写  2 读写
     */
    private Integer level;

    private String creator;

    private Integer deptId;

    @Builder.Default
    private Integer sex=0;
    private String company;
    private String address;

    @ApiModelProperty("职务")
    private String position;

    @ApiModelProperty("微信号")
    private String weChat;

    private Integer num;

    private Boolean adminTag;


}
