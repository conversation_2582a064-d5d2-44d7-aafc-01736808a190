package com.gh.asset.model.dto;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.util.Date;

@TableName( "assetsScrap")
@Data
public class AssetsScrapDTO {


   @TableId(type = IdType.AUTO)
   private Integer id;


   private String code;
   private String newStatus;
   private String type;
   private String reason;
   private String status;

   private Integer deptId;
   private String remark;

   @TableField(fill = FieldFill.INSERT)
   private Date createTime;

   @TableField(fill = FieldFill.INSERT_UPDATE)
   private Date updateTime;

   private Integer createId;
   private String updateId;
   private String createName;

   private String assetsName;
   private String assetsCode;

   private  String deptName;

}
