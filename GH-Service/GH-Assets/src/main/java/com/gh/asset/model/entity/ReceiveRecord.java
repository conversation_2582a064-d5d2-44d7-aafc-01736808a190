package com.gh.asset.model.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@TableName("assetsReceiveRecord")
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ReceiveRecord {

   @TableId(type = IdType.AUTO)
   private Integer id;

   @TableField(fill = FieldFill.INSERT)
   private Date createTime;
   @TableField(fill = FieldFill.INSERT_UPDATE)
   private Date updateTime;
   private Integer receiveId;
   private Integer assetId;
   private Integer projectId;

   private String assetName;
   private String assetCode;
   private String deptName;
   private String typeName;
   private String brandName;





}
