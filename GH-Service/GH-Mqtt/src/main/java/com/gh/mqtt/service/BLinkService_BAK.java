//package com.gh.mqtt.service;
//
//import cn.hutool.core.convert.Convert;
//import cn.hutool.core.date.DateUtil;
//import cn.hutool.core.util.StrUtil;
//import cn.hutool.json.JSONObject;
//import cn.hutool.json.JSONUtil;
//import com.gh.common.model.dto.DevSimpleDTO;
//import com.gh.common.model.dto.RespChangeData;
//import com.gh.common.model.entity.RunLog;
//import com.gh.common.utils.GHResponse;
//import com.gh.mqtt.kafka.KafkaProduce;
//import com.gh.mqtt.util.RedisHelper;
//import com.gh.resource.feign.client.ResourceFeignClient;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Service;
//
//import java.util.ArrayList;
//import java.util.Date;
//import java.util.List;
//
///**
// * Description:处理第三方协议
// * User: zhangkeguang
// * Date: 2024-03-27-15:56
// */
//@Service
//@Slf4j
//public class BLinkService_BAK {
//
//    @Autowired
//    private RedisHelper redisHelper;
//
//    @Autowired
//    private RunLogService runLogService;
//
//    @Autowired
//    private KafkaProduce kafkaProduce;
//
//    @Autowired
//    private EmqxPushService emqxPushService;
//
//    @Autowired
//    private TdService tdService;
//
//    @Autowired
//    private ResourceFeignClient resourceFeignClient;
//
//    //mqtt方式推送数据
//    public void processChange(String data, String topic) {
//        new Thread(() -> {
//            RespChangeData v1 = null;
//            if (StrUtil.isNotEmpty(topic)) {
//                if (topic.endsWith("/device/property/post")) {
//                    List<String> dev = new ArrayList<>();
//                    JSONObject obj = JSONUtil.parseObj(data);
//                    Integer deviceId = obj.getInt("deviceId");
//                    JSONObject items = obj.getJSONObject("params");
//                    items.keySet().forEach(k -> {
//                        JSONObject item = items.getJSONObject(k);
//                        GHResponse<DevSimpleDTO> info = resourceFeignClient.getDevInfoByIdAndProperty(k, deviceId);
//                        Date date = item.getDate("time", DateUtil.date());
//                        if (null != info && null != info.getData()) {
//                            Integer productId = info.getData().getProductId();
//                            if (deviceId != null && productId != null) {
//                                String productName = info.getData().getProductName();
//                                String deviceName = info.getData().getDeviceName();
//                                item.keySet().forEach(kk -> {
//                                    String value = item.getStr("value");
//                                    String name = info.getData().getPropertyName();
//                                    Integer projectId = info.getData().getProjectId();
//                                    String oldValue = redisHelper.HashGetFields(14, "product:" + productId + ":" + deviceId, k);
//                                    if (!StrUtil.equals(value, oldValue)) {
//                                        String key = productId + ":" + deviceId + ":" + k + ":" + value;
//                                        dev.add(key);
//                                        redisHelper.setHash(14, "product:" + productId + ":" + deviceId,
//                                                k, value);
//                                    }
//                                    Boolean record = item.getBool("record", false);
//                                    if (record) {
//                                        tdService.insertDeviceChangeData(Convert.toInt(deviceId, 0), Convert.toInt(productId, 0), projectId, value, productName, deviceName, name, k, date);
//                                    }
//                                });
//                            }
//                        }
//
//
//                    });
//                    v1 = RespChangeData.builder()
//                            .dev(dev)
//                            .build();
//                    if (v1 != null && v1.getDev().size() > 0) {
//                        kafkaProduce.Produce("gh_mqtt_variable_change", v1);
//                    }
//
//                } else if (topic.endsWith("/thing/property/event"))//事件上报
//                {
//                    kafkaProduce.Produce("gh_mqtt_variable_alarm_third", data);
//                } else if (topic.endsWith("/thing/service/property/set_reply")) {
//                    JSONObject obj = JSONUtil.parseObj(data);
//                    if (StrUtil.isNotEmpty(obj.getStr("id"))) {
//                        runLogService.lambdaUpdate().eq(RunLog::getRequestId, obj.getStr("id"))
//                                .set(RunLog::getResult, obj.getBool("success") ? 1 : 0)
//                                .set(RunLog::getResultDesc, obj.getStr("msg"))
//                                .update();
//                    }
//                }
//
//
//            }
//
//
//        }).start();
//
//    }
//
//
//}
