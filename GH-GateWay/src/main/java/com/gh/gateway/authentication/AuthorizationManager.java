package com.gh.gateway.authentication;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.gh.common.redis.RedisHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.security.authorization.AuthorizationDecision;
import org.springframework.security.authorization.ReactiveAuthorizationManager;
import org.springframework.security.core.Authentication;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.security.web.server.authorization.AuthorizationContext;
import org.springframework.stereotype.Component;
import org.springframework.util.Base64Utils;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;


@Component
public class AuthorizationManager implements ReactiveAuthorizationManager<AuthorizationContext> {

    @Autowired
    private RedisHelper redisHelper;

    @Override
    public Mono<AuthorizationDecision> check(Mono<Authentication> mono, AuthorizationContext authorizationContext) {
        ServerWebExchange exchange = authorizationContext.getExchange();
        //请求资源
        String requestPath = exchange.getRequest().getURI().getPath();
        return mono
                .map(auth -> new AuthorizationDecision(checkAuthorities(exchange, auth, requestPath)))
                .defaultIfEmpty(new AuthorizationDecision(false));
    }

    //权限校验
    private boolean checkAuthorities(ServerWebExchange exchange, Authentication auth, String requestPath) {
        if (auth.isAuthenticated()) {
            Jwt principal = (Jwt) auth.getPrincipal();
            String jsonStr = JSONUtil.toJsonStr(principal);
            String encode = Base64Utils.encodeToString(jsonStr.getBytes());
            String user_name = principal.getClaims().get("user_name").toString();
            String authoritiesObj = redisHelper.StringGet(0, "auth:" + user_name);
            List<String> reqAuth = new ArrayList<>();
            List<String> projects = new ArrayList<>();
            Integer project = 0;
            if (StrUtil.isNotEmpty(authoritiesObj)) {
                if (!user_name.equals("admin")) {
                    List<String> authorities = StrUtil.splitTrim(authoritiesObj, ',');
                    String proStr = exchange.getRequest().getHeaders().getFirst("project");
                    project = Convert.toInt(proStr, 0);
                    for (String a : authorities) {
                        if (a.startsWith("p:")) {
                            projects.add(a.split(":")[1]);
                            if (("p:" + project + ":true").equals(a)) {
                                reqAuth.add("sys_project_admin");
                            }
                        } else if (a.startsWith("user_")) {
                            reqAuth.add(a);
                        } else {
                            if (StrUtil.subBefore(a, ":", false).equals(project.toString())) {
                                reqAuth.add(StrUtil.subAfter(a, ":", false));
                            }
                        }
                    }
                    String username = principal.getClaim("user_name");
                    String finalUsername = username;
                    Jwt jwt = new Jwt(principal.getTokenValue(), principal.getIssuedAt(), principal.getExpiresAt(), principal.getHeaders(), new HashMap<String, Object>() {{
                        put("authorities", reqAuth);
                        put("user_name", finalUsername);
                    }});

                    encode = Base64Utils.encodeToString(JSONUtil.toJsonStr(jwt).getBytes());

                } else {
                    reqAuth.addAll(StrUtil.splitTrim(authoritiesObj, ','));
                }
            }
            String finalEncode = encode;
            Integer finalProject = project;
            String authStr = CollUtil.join(reqAuth, ",");
            ServerHttpRequest request = exchange.getRequest().mutate()
//                   排除token否则下面服务会再次拦截校验
                    .headers(httpHeaders -> httpHeaders.remove("Authorization"))
                    .headers(httpHeaders -> httpHeaders.add("principal", finalEncode))
                    .headers(httpHeaders -> httpHeaders.add("project", finalProject.toString()))
                    .headers(httpHeaders -> httpHeaders.add("projects", CollUtil.join(projects, ",")))
                    .headers(httpHeaders -> httpHeaders.add("auth", authStr.equals("") ? "no" : authStr))
                    .build();
            exchange.mutate().request(request).build();

            return true;
        }

        return false;
    }


}
