server:
  port: 23000
  error:
    whitelabel:
      enabled: false
#  ssl:
#    key-store: classpath:ibms.club.jks
#    key-store-type: J<PERSON>
#    key-password: 7tn6y4pa
#    enabled: true
spring:
  application:
    name: gateway-service
  cloud:
    gateway:
      discovery:
        locator:
          enabled: true
          lower-case-service-id: true
      routes:
        - id: ws-socket
          uri: lb://ws-service
          predicates:
            - Path=/socket.io/**
        - id: api-route
          uri: lb://{service-name}  # 动态转发到微服务
          predicates:
            - Path=/api/**  # 匹配所有以 /api 开头的请求
          filters:
            - StripPrefix=1  # 去掉路径中的第一级前缀（/api）

      default-filters:
        - DedupeResponseHeader=Access-Control-Allow-Origin, RETAIN_UNIQUE


    nacos:
      server-addr: ************:28848
      config:
        file-extension: yaml
        namespace: 734f1747-1d86-4f76-9cef-91fc7ea262b6
        extension-configs:
          - data-id: redis.yml
      discovery:
        server-addr: ************:28848
        namespace: f0afb10d-cc93-4f3f-9dae-f7d7c3363802

  security:
    oauth2:
      resourceserver:
        jwt:
          jwk-set-uri: 'http://localhost:23000/auth-service/rsa/publicKey'



cors:
  item:
    - /basicinfo-service/**
    - /auth-service/**
    - /filecenter-service/**
    - /maintenance-service/**
    - /mqtt-service/**
    - /resource-service/**
    - /user-service/**
    - /rtdb-service/**
    - /scene-service/**
    - /video-service/**
    - /energy-service/**
    - /bim-service/**
    - /job-service/**
    - /scada-service/**
    - /hk-service/**




