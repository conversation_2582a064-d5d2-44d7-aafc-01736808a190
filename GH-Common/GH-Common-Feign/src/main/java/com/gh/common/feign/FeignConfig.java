package com.gh.common.feign;


import cn.hutool.core.util.StrUtil;
import feign.RequestInterceptor;
import feign.RequestTemplate;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;

@Configuration
public class FeignConfig implements RequestInterceptor {
    @Override
    public void apply(RequestTemplate requestTemplate) {
//        requestTemplate.header("auth","sys_admin");

        try {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if(attributes!=null)
            {
                HttpServletRequest request = attributes.getRequest();
                String project = request.getHeader("project");
                if(StrUtil.isNotEmpty(project))
                {
                    requestTemplate.header("project",project);
                }
            }
            requestTemplate.header("auth","sys_admin");
        }catch (Exception e)
        {
            e.printStackTrace();
        }

    }
}
