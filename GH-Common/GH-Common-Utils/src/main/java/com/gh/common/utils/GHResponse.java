package com.gh.common.utils;


import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@ApiModel(value = "请求返回体")
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class GHResponse<T> implements Serializable {
    private static final long serialVersionUID = 1L;

    private String msg;

    private Boolean success;

    private  T data;

    private long total;

    public static <T> GHResponse<T> ok(T data)
    {

        return restResult(data,ConstInterface.SUCCESS, null,0);
    }
    public static <T> GHResponse<T> ok()
    {
        return restResult(null,ConstInterface.SUCCESS, null,0);
    }

    public static <T> GHResponse<T> ok(T data, String msg) {
        return restResult(data, ConstInterface.SUCCESS, msg,0);
    }

    public static <T> GHResponse<T> ok(T data, long count) {

        return restResult(data, ConstInterface.SUCCESS, null,count);

    }

    public static <T> GHResponse<T> failed() {
        return restResult(null, ConstInterface.FAIL, null,0);
    }

    public static <T> GHResponse<T> failed(String msg) {
        return restResult(null, ConstInterface.FAIL, msg,0);
    }

    private static <T> GHResponse<T> restResult(T data,Boolean success, String msg,long count) {
        GHResponse<T> apiResult = new GHResponse<>();
        apiResult.setSuccess(success);
        apiResult.setData(data);
        apiResult.setTotal(count);
        apiResult.setMsg(msg);
        return apiResult;
    }

}
