package com.gh.common.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("GHAuth_UserProject")
public class UserProject {
    @TableId(type = IdType.AUTO)
    private Integer id;
    private Integer userId;
    private Integer projectId;
    @ApiModelProperty("被谁邀请")
    private Integer invitedId;

    @ApiModelProperty("是否同意邀请")
    private Boolean status;


}
