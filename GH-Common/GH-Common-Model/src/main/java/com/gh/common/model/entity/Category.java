package com.gh.common.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import lombok.Builder;
import lombok.Data;

/**
 * 能耗系统中,分项信息
 */

@Data
@Builder
@TableName("GHEnergy_Category")
@ApiModel(description = "能耗分项信息")
public class Category {
    private Integer id;
    private Integer parentId;
    private String name;
    private String description;
    private Integer projectId;
    private String fullPath;
    private String fullName;
}
