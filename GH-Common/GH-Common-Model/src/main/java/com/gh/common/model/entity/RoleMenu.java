package com.gh.common.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("GHAuth_RoleMenu")
@ApiModel("MenuAuth" )
public class RoleMenu {
    @NotNull(message = "menuId不能为空")
    private  Integer menuId;

    @NotNull(message = "roleId不能为空")
    private  Integer roleId;

    @NotNull(message = "menuType不能为空")
    private  Integer menuType;

    @ApiModelProperty("项目id")
    @NotNull(message = "projectId不能为空")
    private  Integer projectId;

    @ApiModelProperty("功能菜单的权限标识")
    private String authId;


    private Boolean half;
}
