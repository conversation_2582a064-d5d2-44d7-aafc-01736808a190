package com.gh.common.model.dto;


import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor

public class FCRecordDTO {

    @TableId(type = IdType.AUTO)
    private Integer id;

    private Integer planId;

    private Integer creator;

    @NotNull(message = "projectId不能为空")
    private Integer projectId;

    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;


    private String paths;

    private String description;

    private String content;

    private Integer productId;

    private Integer areaId;

    private String areaName;
    private String productName;
    private String deviceName;
    private String userName;
    private String planName;

    //是否延期维保
    private Boolean delay;

    //超时后 正确的维保时间
    private Date nextTime;

    private Date nextTime1;



}
