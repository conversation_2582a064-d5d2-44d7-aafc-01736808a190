package com.gh.common.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("GHPatrol_Plan")
public class Plan {

    @TableId(type = IdType.AUTO)
    private Integer id;

    @NotEmpty(message = "name不能为空")
    private String name;

//    @NotNull(message = "startTime不能为空")
//    private Date startTime;
//
//    @NotNull(message = "endTime不能为空")
//    private Date endTime;



    @NotNull(message = "lineId不能为空")
    private Integer lineId;

    @ApiModelProperty("2循环 1单次")
    @NotNull(message = "type不能为空")
    private Integer type;

    @ApiModelProperty("单次 是否完成")
    private Boolean tag=false;

    private Integer projectId;

    @TableField(exist = false)
    private String lineName;

//    @NotNull(message = "staffId不能为空")
//    private Integer staffId;

    @ApiModelProperty("1一般 2重要 3紧急")
    private Integer level;
    @ApiModelProperty("巡检计划类型  1 现场巡检 2 设备巡检")
    private Integer patrolType;
    @ApiModelProperty("1启用  0停止")
    private Boolean status;
}
