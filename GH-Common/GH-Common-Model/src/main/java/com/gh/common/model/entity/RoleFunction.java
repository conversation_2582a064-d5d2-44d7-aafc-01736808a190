package com.gh.common.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("GHAuth_RoleAuth")
@ApiModel("RoleFunction" )
public class RoleFunction {
    @NotNull(message = "roleId不能为空")
    private  Integer roleId;

    @NotNull(message = "authId不能为空")
    private  String authId;

    @ApiModelProperty("项目id")
    @NotNull(message = "projectId不能为空")
    private  Integer projectId;

    private Boolean half;
}
