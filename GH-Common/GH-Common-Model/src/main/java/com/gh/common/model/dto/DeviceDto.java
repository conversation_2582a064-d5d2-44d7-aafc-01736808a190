package com.gh.common.model.dto;

import com.gh.common.model.entity.DeviceProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

@Data
@Builder
@ApiModel(description = "DeviceDto-设备")
@AllArgsConstructor
@NoArgsConstructor
public class DeviceDto {

    private Integer id;

    @NotEmpty(message = "name 不能为空")
    @ApiModelProperty(value = "设备名称")
    private String name;

    @NotEmpty(message = "code 不能为空")
    @ApiModelProperty(value = "设备编码")
    private String code;

    @NotNull(message = "productId 不能为空")
    @ApiModelProperty(value = "产品类型")
    private Integer productId;

    @ApiModelProperty(value = "设备安装位置")
    private String position;

    @ApiModelProperty(value = "设备厂家")
    private String factoryName;

    @ApiModelProperty(value = "设备安装时间")
    private Date installTime;

    @ApiModelProperty(value = "设备型号")
    private String model;

    @NotNull(message = "status 不能为空")
    @ApiModelProperty(value = "设备状态")
    private Integer status;

    @ApiModelProperty(value = "设备IP")
    private String ip;

    @ApiModelProperty(value = "设备联系方式")
    private String phone;

    @ApiModelProperty(value = "设备联系人")
    private String person;

    @ApiModelProperty(value = "设备质保期")
    private Integer period;

    @NotNull(message = "projectId 不能为空")
    @ApiModelProperty(value = "项目id")
    private Integer projectId;

    @ApiModelProperty(value = "菜单(系统)id")
    private Integer menuId;

    @ApiModelProperty(value = "设备区域id")
    private Integer areaId;


    @ApiModelProperty(value = "设备部门id")
    private Integer departmentId;

    @ApiModelProperty(value = "说明书")
    private String manualPath;

    @ApiModelProperty(value = "设备图纸")
    private String cadPath;

    @ApiModelProperty(value = "是否是摄像机")
    @NotNull(message = "camFlag 不能为空")
    private Boolean camFlag;

    private List<DeviceProperty> deviceItems;


    private String protocol;

    private String gatewayId;


}
