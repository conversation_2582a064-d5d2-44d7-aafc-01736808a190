package com.gh.common.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
//import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Pattern;
import java.util.Date;

@TableName("GHAuth_User")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class User {

    @TableId(type = IdType.AUTO)
    private Integer id;
    @NotEmpty(message = "登录名不能为空！")
    private String  userName;
    @NotEmpty(message = "用户名不能为空！")
    private String name;

    @TableField(exist = false)
    private Date startTime;
    @TableField(exist = false)
    private Date endTime;

    @Builder.Default
    private boolean status=true;
    private String openId;
    private boolean remove;
    private Date createTime;
    private Date updateTime;
    @Email(message = "邮箱格式不正确！")
    private String email;
    @Pattern(regexp = "^1[3456789]\\d{9}$",message = "手机号码格式不正确！")
    private String phone;
    @JsonIgnore
    private byte[] salt;
    @JsonIgnore
    private String password;

    /**
     * 0 只读 1 写  2 读写
     */
//    @Range(min = 0,max = 2,message = "Level取值范围0-2")
    private Integer level;

    private Integer num;
}
