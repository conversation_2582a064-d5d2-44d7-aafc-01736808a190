package com.gh.common.model.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.util.List;


@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("GHDM_DeviceStandard")
@ApiModel(value = "DeviceStandard",description = "设备指标")
public class DeviceStandard {

    @TableId(type = IdType.AUTO)
    private  Integer id;

    @ApiModelProperty("项目id")
    @NotNull(message = "projectId不能为空")
    private Integer projectId;

    private Integer deviceId;

    @ApiModelProperty("引用ghdm_Standard id")
    //级联删除
    private Integer standardId;

    @ApiModelProperty("ioserver变量")
    private String variable;


    @ApiModelProperty("数据源json")
    private String dataSource;

    @ApiModelProperty("数据源显示类型")
    private Integer dataSourceType;

    @ApiModelProperty("数据源请求接口")
    private String apiUrl;

    @ApiModelProperty("数据源请求参数")
    private String params;

    @ApiModelProperty("数据源固定值")
    private String value;

    @TableField(exist = false)
    private String name;


    private String dataVal;

    @TableField(exist = false)
    private String keyCode;

    @TableField(exist = false)
    private String identifier;

    @TableField(exist = false)
    private List<StandardParam> deviceParams;

    //deviceCode
    @TableField(exist = false)
    private String code;

    @TableField(exist = false)
    private Integer productId;
}
