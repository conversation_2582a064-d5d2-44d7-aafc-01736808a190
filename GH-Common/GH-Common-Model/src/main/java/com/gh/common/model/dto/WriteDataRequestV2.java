package com.gh.common.model.dto;

import cn.hutool.core.lang.Snowflake;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
@Builder
//@NoArgsConstructor
@AllArgsConstructor
public class WriteDataRequestV2 {
    public WriteDataRequestV2()
    {
        this.msgId= new Snowflake().nextId();
        this.msgTime= System.currentTimeMillis();
        this.code="";
        this.data=new ArrayList<>();
    }
    public long  msgId;
    public long  msgTime;
    public String code;
    public List<WriteDataV2> data;

}
