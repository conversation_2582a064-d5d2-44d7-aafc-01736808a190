package com.gh.common.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("GHDM_StandardParam")
@ApiModel(value = "StandardParam", description = "指标参数")
public class StandardParam {

    @TableId(type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty("项目id")
    @NotNull(message = "projectId不能为空")
    private Integer projectId;

    @ApiModelProperty("参数名称")
    @NotEmpty(message = "name不能为空")
    private String name;

    @ApiModelProperty("指标参数key")
    @NotEmpty(message = "paramKey不能为空")
    private String paramKey;

    @ApiModelProperty("指标参数值")
    private String paramValue;

    @ApiModelProperty("产品类型id")
    private Integer productId;

    @ApiModelProperty("引用指标id")
    private Integer standardId;

    @ApiModelProperty("显示方式")
    private Integer showType;

    @ApiModelProperty("单位")
    private Integer unit;

    @ApiModelProperty("取值的最大值")
    private String max;

    @ApiModelProperty("取值的最小值")
    private String min;

    @ApiModelProperty("数据类型")
    private String dataType;

    private String icon;

    @TableField(exist = false)
    @ApiModelProperty("指标名称")
    private String stdName;


    @Builder.Default
    @TableField(exist = false)
    private List<Condition> conditions=new ArrayList<>();

    private String config;

}
