package com.gh.common.model.dto;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ApiModel("自定义命令模型")
public class WriteCustomDataRequestV1 {
    private String batchDefinitionId;
    private String clientId;
    private Integer area;
    private Integer level;
    public List<WriteCustomDataV1> requestItems;
    public WriteCustomDataRequestV2 toV2()
    {
        WriteCustomDataRequestV2 writeDataRequestV2=new WriteCustomDataRequestV2();
        List<WriteCustomDataV2> data= new ArrayList<>();
        this.requestItems.forEach(item->{
            WriteCustomDataV2 writeDataV2=WriteCustomDataV2.builder()
                    .iosvrKey(item.getIosvrKey())
                    .chlKey(item.getChlKey())
                    .ctrlKey(item.getCtrlKey())
                    .varKey(item.getVarKey())
                    .cmdCode(item.getCmdCode())
                    .cmdParams(item.getCmdParams())
                    .build();
            data.add(writeDataV2);
        });
        writeDataRequestV2.setData(data);
        return writeDataRequestV2;

    }
}
