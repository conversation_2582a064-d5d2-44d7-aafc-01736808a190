package com.gh.common.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DeviceStandardDto {

    @ApiModelProperty("ioServer 变量")
    private String variable;

    @ApiModelProperty("指标名称")
    private String name;

    @ApiModelProperty("指标id")
    private Integer standardId;

    private String value;
}
