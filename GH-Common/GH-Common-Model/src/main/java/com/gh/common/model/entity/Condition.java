package com.gh.common.model.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@TableName("GHDM_Condition")
@NoArgsConstructor
@AllArgsConstructor
public class Condition {
    @TableId(type = IdType.AUTO)
    private Integer id;
    private String factor;
    private String value;
    private String color;
    private String icon;
    private String text;
    private Integer paramId;
}
