package com.gh.common.model.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import java.util.Date;


@Data
@Builder
@TableName("GHOPS_Project")
@ApiModel(description = "项目")
@AllArgsConstructor
@NoArgsConstructor
public class Project {

    @TableId(type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "项目名称")
    @NotEmpty(message = "name 不能为空")
    private String name;

    @ApiModelProperty(value = "项目描述")
    private String description;

    @ApiModelProperty(value = "创建者，无需传值")

    private String creator;

    @ApiModelProperty(value = "无需传值")
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    @ApiModelProperty(value = "无需传值")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    @ApiModelProperty(value = "项目管理员，谁创建谁是管理员")
    private Integer projectAdminId;


    @ApiModelProperty("项目编号")
    private String code;

    @ApiModelProperty("行政区域")
    private String area;

    @ApiModelProperty("详细地址")
    private String address;

    @ApiModelProperty("建筑类型")
    private Integer buildingType;
    @ApiModelProperty("是否有数据中心")
    private Integer dataFlag;
    @ApiModelProperty("项目图片地址")
    private String imgPath;
    @ApiModelProperty("地理坐标")
    private String coordinate;

    @Builder.Default
    private boolean remove=false;

    @ApiModelProperty("是否是当前用户自己的项目")
    @TableField(exist = false)
    @Builder.Default
    protected boolean selfTag=false;

    @ApiModelProperty("建筑面积")
    private Double areaSize;

    private String title;

    private String video;


    @ApiModelProperty("扬州首页 0视频 1 图片")
    private Integer showType;

    //vr地址
    private String vrUrl;

    //ar地址
    private String arUrl;

    private Float footPrintArea;
    private Float floorArea;
    private Float height;
    private Integer floors;


}
