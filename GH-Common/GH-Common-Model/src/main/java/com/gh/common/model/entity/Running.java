package com.gh.common.model.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * 系统和全局三种模式
 */
@Data
@Builder
@ApiModel(description = "Running")
@TableName("GHOPS_Running")
@NoArgsConstructor
@AllArgsConstructor
public class Running {


    @TableId(type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty("1--智慧 2--策略 3--手动")
    @NotNull(message = "type不能为空")
    private Integer type;

    @ApiModelProperty("项目id")
    @NotNull(message = "projectId不能为空")
    private Integer projectId;

    @ApiModelProperty("子系统菜单id")
    private Integer menuId;

    @ApiModelProperty("操作员id 后台生成")
    private Integer operator;


    @ApiModelProperty("操作员名称 后台生成")
    private String operatorName;

    @ApiModelProperty("模式下各种配置id")
    private String ids;


    @ApiModelProperty("当前模式是否属于激活状态")
    private Boolean status=false;


}
