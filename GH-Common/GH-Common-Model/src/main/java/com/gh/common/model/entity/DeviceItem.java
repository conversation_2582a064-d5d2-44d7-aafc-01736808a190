package com.gh.common.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;

@TableName("GHDM_DeviceItem")
@Data
@Builder
@ApiModel(description = "DeviceItem-设备扩展属性")
@AllArgsConstructor
@NoArgsConstructor
public class DeviceItem {
    @TableId(type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty("扩展属性key")
    @NotEmpty(message = "propertyKey 不能为空")
    private String propertyKey;

    @ApiModelProperty("扩展属性value")
    @NotEmpty(message = "propertyValue 不能为空")
    private String propertyValue;

    @NotEmpty(message = "name 不能为空")
    @ApiModelProperty("扩展属性name")
    private String name;

    private Integer deviceId;
}
