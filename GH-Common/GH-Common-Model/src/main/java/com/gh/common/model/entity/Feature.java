package com.gh.common.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
@TableName("GHEnergy_Feature")
@ApiModel(description = "能耗功能信息")
public class Feature {
    private Integer id;
    private Integer parentId;
    private String name;
    private String description;
    private Integer projectId;
    private String fullPath;
    private String fullName;
}
