package com.gh.common.model.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Date;

@Data
@Builder
@TableName("GHOPS_Department")
@ApiModel(description = "部门信息")
public class Department {
    @TableId(type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "父节点id")
    @NotNull(message = "parentId 不能为空")
    private Integer parentId;

    @ApiModelProperty(value = "部门名称")
    @NotEmpty(message = "name 不能为空")
    private String name;

    @ApiModelProperty(value = "部门描述")
    private String description;

    @ApiModelProperty(value = "项目id")
    private Integer projectId;

    @ApiModelProperty(value = "无需传值")
    private String creator;

    @ApiModelProperty(value = "无需传值")
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    @ApiModelProperty(value = "无需传值")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    @ApiModelProperty(value = "部门全路劲")
    private String fullName;
    @ApiModelProperty(value = "部门全路劲id")
    private String fullPath;

    private Integer userId;
}
