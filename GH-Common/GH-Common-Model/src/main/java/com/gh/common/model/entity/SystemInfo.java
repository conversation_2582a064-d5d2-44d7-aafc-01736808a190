package com.gh.common.model.entity;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
//系统信息
@TableName("GHOPS_Info")
public class SystemInfo {
    @NotEmpty(message = " tagValue不能为空")
    private String tagValue;
    @NotEmpty(message = " tagName不能为空")
    private String tagName;
    private String description;
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
    @NotNull(message = " status不能为空")
    private Boolean status;
    @NotNull(message = " projectId不能为空")
    private Integer projectId;
}
