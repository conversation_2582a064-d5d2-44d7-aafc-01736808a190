package com.gh.common.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WriteDataRequestV1 {
    private String batchDefinitionId;
    private String clientId;
    private Integer area;
    private Integer level;
    private Integer projectId;
    public List<WriteDataV1> requestItems;
    public  WriteDataRequestV2 toV2()
    {
        WriteDataRequestV2 writeDataRequestV2=new WriteDataRequestV2();
        List<WriteDataV2> data= new ArrayList<>();
        this.requestItems.forEach(item->{
            WriteDataV2 writeDataV2=WriteDataV2.builder()
                    .batchDefinitionId(this.getBatchDefinitionId())
                    .clientId(this.getClientId())
                    .level(this.getLevel())
                    .chlKey(item.getChlKey())
                    .ctrlKey(item.getCtrlKey())
                    .id(item.getId())
                    .desc(item.getDesc())
                    .iosvrKey(item.getIosvrKey())
                    .propName(item.getPropName())
                    .value(item.getValue())
                    .varKey(item.getVarKey())
                    .build();
            data.add(writeDataV2);
        });
        writeDataRequestV2.setData(data);
        return writeDataRequestV2;

    }
}
