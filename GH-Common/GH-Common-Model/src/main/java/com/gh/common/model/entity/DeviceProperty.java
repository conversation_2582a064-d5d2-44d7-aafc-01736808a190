package com.gh.common.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("GHDM_DeviceProp")
public class DeviceProperty {
    @TableId(type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty("扩展属性key")
    @NotEmpty(message = "propertyKey 不能为空")
    private String propertyKey;

    @ApiModelProperty("扩展属性value")
    @NotEmpty(message = "propertyValue 不能为空")
    private String propertyValue;

    @NotEmpty(message = "name 不能为空")
    @ApiModelProperty("扩展属性name")
    private String name;

    private String type;

    @NotNull(message = "projectId不能为空")
    private  Integer projectId;

    @ApiModelProperty("设备类型的数据字典id")
    @NotNull(message = "deviceId 不能为空")
    private Integer deviceId;

}
