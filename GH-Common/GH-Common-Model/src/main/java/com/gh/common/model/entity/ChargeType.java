package com.gh.common.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import lombok.Builder;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;

@Data
@Builder
@TableName("GHEnergy_Charge")
@ApiModel(description = "电费收费类型")
public class ChargeType {
    private Integer id;
    @NotNull(message = "status不能为空")
    private Boolean status;
    @NotNull(message = "projectId不能为空")
    private Integer projectId;
    private Date priceTime;
    @NotNull(message = "productId不能为空")
    private Integer productId;

    private BigDecimal price;
    private BigDecimal pPrice;
    private BigDecimal fPrice;
    private BigDecimal gPrice;
}
