package com.gh.common.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gh.common.model.dto.ProductDeviceDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 能耗系统中，能耗设备
 */


@ApiModel(value = "GHEnergy_Device")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("GHEnergy_Device")
public class EnergyDevice {

    @TableId(type = IdType.AUTO)
    private Integer id;

    @NotEmpty(message = "name不能为空")
    private String name;

    /**
     * 级联查询
     */
    @ApiModelProperty("电表id全路径编号")
    private String code;

    @ApiModelProperty("父电表id")
    private Integer parentId;


    @ApiModelProperty("区域id")
    private  Integer areaId;


    @ApiModelProperty("系统区域全路劲id")
    private String areaCode;

    @NotNull(message = "项目id不能为空")
    @ApiModelProperty("项目id")
    private Integer projectId;

    @ApiModelProperty("部门id")
    private Integer deptId;

    @ApiModelProperty("系统部门全路劲id")
    private String deptCode;


    @ApiModelProperty("产品类型  水电气")
    @NotNull(message = "产品类型不能为空")
    private Integer deviceType;

    @ApiModelProperty("分项id")
    private Integer categoryId;


    @ApiModelProperty("功能id")
    private Integer featureId;



    @ApiModelProperty("所属系统id")
    private Integer systemId;


    @ApiModelProperty("是否启用")
    @Builder.Default
    private Boolean status=true;

    @NotNull(message = "areaTag不能为空")
    @ApiModelProperty("是否参与区域统计")
    private Boolean areaTag;




    @TableField(exist = false)
    private List<ProductDeviceDTO> standards;



    @TableField(exist = false)
    private String areaName;

    @TableField(exist = false)
    private String deptName;

    @TableField(exist = false)
    @ApiModelProperty("分项名称")
    private String categoryName;

    @TableField(exist = false)
    @ApiModelProperty("功能用途名称")
    private String featureName;

    @ApiModelProperty("分项能耗code")
    private String categoryCode;

    @ApiModelProperty("功能(用途)能耗code")
    private String featureCode;

    @TableField(exist = false)
    private List<MeterLog> meterLogs;

    //是否参与策略统计
    private Boolean strategy;




}
