package com.gh.common.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("GHOPS_Staff")
public class Staff {
    private  Integer id;
    @NotEmpty(message = "name 不能为空")
    private String name;
    private Integer sex;
    @NotEmpty(message = "phone 不能为空")
    private String phone;
    @NotEmpty(message = "email 不能为空")
    private String email;
    @NotNull(message = "staffType 不能为空")
    @ApiModelProperty("所属专业")
    private Integer staffType;
    @NotNull(message = "userId 不能为空")
    private Integer userId;
    @NotNull(message = "projectId 不能为空")
    private Integer projectId;
    @ApiModelProperty("所属角色")
    private Integer roleType;
}
