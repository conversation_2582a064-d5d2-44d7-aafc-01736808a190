package com.gh.common.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SceneVar {

    @ApiModelProperty("变量值")

    private String varValue;

    @ApiModelProperty("变量组合")

    private String variable;

    private Integer deviceId;

    private Integer deviceStandardId;
}
