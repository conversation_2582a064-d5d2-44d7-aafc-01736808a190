package com.gh.common.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

@Data
@Builder
@TableName("GHAuth_RoleAuth")
@ApiModel(description = "角色权限标识")
public class RoleAuth {

    @ApiModelProperty(value = "角色id")
    @NotEmpty(message = "roleId 不能为空")
    private Integer roleId;

    @ApiModelProperty(value = "项目id")
    @NotEmpty(message = "projectId 不能为空")
    private Integer projectId;

    @ApiModelProperty(value = "权限标识")
    @NotEmpty(message = "authId 不能为空")
    private String authId;


}
