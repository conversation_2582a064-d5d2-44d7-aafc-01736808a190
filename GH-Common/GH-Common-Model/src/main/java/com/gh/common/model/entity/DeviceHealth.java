package com.gh.common.model.entity;


import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.util.Date;

@Data
@TableName("GHDM_DeviceHealth")
public class DeviceHealth {
    @TableId(type = IdType.AUTO)
    private Integer id;
    //上次统计时间
    private Date lastTime;

    private Integer deviceId;

    //故障次数
    private Integer faultTotal;

    //运行时长 单位分钟
    private long runTime;

    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    //上次统计值
    private String lastValue;

    //运行时长得分
    private double runScore=20;
    //维保得分
    private double maintenanceScore=20;
    //维修得分
    private double repairScore=20;
    //故障得分
    private double faultScore=20;
    //离线得分
    private double offLineScore=20;
    //累计离线次数
    private Integer offTotal;

    //保养逾期次数
    private Integer delayTotal;
    //设备名称
    private String deviceName;

    private Integer projectId;

    //维修累计时间  天
    private long maintenanceTime;
}
