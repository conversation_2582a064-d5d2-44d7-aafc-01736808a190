package com.gh.common.model.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("GHOPS_RunLog")
public class RunLog {

   @TableId(type = IdType.AUTO)
    private String id;

    private Date logTime;

    private String deviceName;

    private String standardName;

    private Integer menuId;

    private String oldValue;

    private String newValue;

    @ApiModelProperty("1-智慧 2-策略 3-手工  4--单个设备控制")
    private Integer type;

    private Integer userId;

   @ApiModelProperty("1-全局 2-系统")
    private Integer tag;

   private Integer projectId;

   private Integer deviceId;

   @TableField(exist = false)
   private String username;

   private String variable;
   private String identifier;


    @TableField(exist = false)
    private String deviceName1;

    @TableField(exist = false)
    private String standardName1;


    private String  requestId;

    private String   resultDesc;

    private Boolean result;

}
