package com.gh.common.model.dto;

import cn.hutool.core.lang.Snowflake;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

import java.util.List;
@Data
@Builder
@AllArgsConstructor
public class WriteCustomDataRequestV2  {
    public WriteCustomDataRequestV2()
    {
        this.msgId= new Snowflake().nextId();
        this.mgsTime= System.currentTimeMillis();
        this.code="";
    }
    public long  msgId;
    public long  mgsTime;
    public String code;
    public List<WriteCustomDataV2> data;
}
